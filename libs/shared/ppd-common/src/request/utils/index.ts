import { RequestParams } from '../types';

/**
 * 请求地址参数非第一个拼接字符
 */
export const OTHER_URL_PARAM_STRING = '&' as const;

/**
 * 请求地址参数第一个拼接字符
 */
export const FIRST_URL_PARAM_STRING = '?' as const;

/**
 * 浅层对象转key=value结构
 * @param params 浅层对象
 * @param customFormat 特殊格式化函数
 * @returns
 */
export const objectToKeyValue = (params?: RequestParams, customFormat = item => item) => {
  let kvString = '';
  if (params) {
    const keyArr = Object.keys(params);
    keyArr.forEach((key, index) => {
      const lastString = index !== keyArr.length - 1 ? OTHER_URL_PARAM_STRING : '';
      kvString += `${customFormat(key)}=${customFormat(params[key])}${lastString}`;
    });
  }
  return kvString;
};

/**
 * 是否第一个路由参数
 * @param url
 * @returns
 */
export const isFirstParams = (url: string) => !url.includes('?');

/**
 * 是否http开头的地址
 * @param url
 * @returns
 */
export const isUrl = (url: string) => url.indexOf('http') === 0;

/**
 * 处理query string，默认都会encodeURIComponent，防止中文出问题
 * @param url 需要拼接的url
 * @param params 拼接的对象
 * @returns 返回拼接后的url
 */
export const buildUrl = (url: string, params?: RequestParams) => {
  const concatString = objectToKeyValue(params, encodeURIComponent);
  if (isFirstParams(url)) {
    return `${url}${concatString ? `${FIRST_URL_PARAM_STRING}${concatString}` : ''}`;
  }
  return `${url}${concatString ? `${OTHER_URL_PARAM_STRING}${concatString}` : ''}`;
};

/**
 * 合并配置
 * @param defaultConfig 默认配置
 * @param customConfig 自定义配置
 * @param isFilterNullValue 是否过滤空值,默认不过滤
 * @returns 返回最终合并配置
 */
export const mergeConfig = <DefaultConfig = unknown, CustomConfig = DefaultConfig>
  (
    defaultConfig: DefaultConfig,
    customConfig?: CustomConfig,
  ): DefaultConfig => {
  if (!defaultConfig) {
    // eslint-disable-next-line no-param-reassign
    defaultConfig = {} as unknown as DefaultConfig;
  }
  if (customConfig) {
    Object.keys(customConfig).forEach((key: string) => {
      const customConfigItem = customConfig[key];
      const defaultConfigItem = defaultConfig[key];
      if (defaultConfigItem && typeof defaultConfigItem === 'object') {
        if (Array.isArray(defaultConfigItem) && Array.isArray(customConfigItem)) {
          defaultConfigItem.push(...customConfigItem);
          return;
        }
        return mergeConfig(defaultConfigItem, customConfigItem);
      }
      // eslint-disable-next-line no-param-reassign
      defaultConfig[key] = customConfig[key];
    });
  }
  return defaultConfig;
};


/**
 * 判断请求状态是否成功
 * @param status http状态码
 * @returns
 */
export function isHttpSuccess(status: number) {
  return (status >= 200 && status < 300) || status === 304;
}
