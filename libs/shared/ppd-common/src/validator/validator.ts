/**
 * @file 代码逻辑文件
*/

/**
 * 校验中文名长度2到101
 * @param value 数值
 * @param message 异常信息
 */

import type { Message } from './index.type';

export function checkCN(value: string, message: Message) {
  return /^[\u4e00-\u9fa5·•]{2,10}$/.test(value)
    ? true
    : message;
}
/**
 * 判断内容是否合法的整数或整数字符串
 * @param value 判断内容
 */
export const isValidatedInt = (value: unknown) => !isNaN(parseInt(value as string, 10));
