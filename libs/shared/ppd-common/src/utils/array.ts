/**
 * 修改数据结构，将数组中的某个值作为key提取出来形成一个新的对象，方便调用方使用例如：
 * 输入：[{"country_code":"CHN","country_name":"中国"}]
 * 返回：{"CHN":{"country_code":"CHN","country_name":"中国"}}
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function arrayToMap<T extends Record<string, any>, K extends keyof T>(arr: T[], key: K): Record<string, T> {
  const res: Record<string, T> = {};
  arr.forEach((item) => {
    res[item[key]] = item;
  });
  return res;
}
