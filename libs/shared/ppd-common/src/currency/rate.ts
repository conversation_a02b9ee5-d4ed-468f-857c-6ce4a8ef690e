import BigNumber from 'bignumber.js';
import { Currency, CurrencyPosition } from './index.type';
// 银行家舍入
BigNumber.config({ ROUNDING_MODE: BigNumber.ROUND_HALF_EVEN });
/**
 * 生成汇率文本如 1USD=6.33883198CNY
 * @param rate 汇率
 * @param rateDirect 汇率方向
 * @param fixedNumber 保留小数位数
 */
export const handleRateFormat = (
  rate: string | number,
  rateDirect: string,
  fixedNumber = 6,
  splitString = '',
) => {
  const buyCurrency = rateDirect.slice(0, 3) as Currency;
  const sellCurrency = rateDirect.slice(3, 6) as Currency;
  const fixedAmt = transformDestRate(rate, buyCurrency, sellCurrency, fixedNumber);
  return `1${splitString}${buyCurrency} = ${fixedAmt}${splitString}${sellCurrency}`;
};

/**
 * 转换为可直接计算的汇率
 * @param rate 汇率，10的8次方倍的汇率
 * @param srcCurrency 源币种
 * @param destCurrency 目标币种
 * @param fixedNumber 保留小数位
 * @returns 转换为小数的汇率
 */
export function transformDestRate(
  rate: string | number,
  srcCurrency: Currency,
  destCurrency: Currency,
  fixedNumber = 6,
) {
  const buyRate = getCurrencyRate(srcCurrency);
  const sellRate = getCurrencyRate(destCurrency);
  const fixedAmt = new BigNumber((Number.parseFloat(rate.toString()) * buyRate) / sellRate).div(10 ** 8)
    .toFixed(fixedNumber, BigNumber.ROUND_HALF_EVEN);
  return fixedAmt;
}

/**
 * 返回币种的移位比例，如100，1
 * @param currencyType 币种
 * @returns 移位比例
 */
export function getCurrencyRate(currencyType: Currency): number {
  const decimals = CurrencyPosition[currencyType];
  if (decimals === undefined) {
    throw new Error(`not-${currencyType}-position`);
  }
  return decimals > 0 ? 10 ** decimals : 1;
}

/**
 * 根据汇率信息，获取计算后的金额
 * @param amount 被计算的金额
 * @param currency 被计算金额对应的币种
 * @param srcCurrency 汇率源币种
 * @param destCurrency 汇率目标币种
 * @param fixedNumber 保留的小数位
 * @param rate 汇率，10的8次方倍的汇率
 * @returns 转换后的金额
 */
export function getAmountByRate(
  amount: string | number,
  currency: Currency,
  srcCurrency: Currency,
  destCurrency: Currency,
  rate: number| string,
  fixedNumber = 2,

) {
  // 计算的币种既不是源币种，也不是目标币种，则抛错
  if (currency !== srcCurrency && currency !== destCurrency) {
    throw new Error('currency error');
  }
  let fixedAmt = new BigNumber(transformDestRate(rate, srcCurrency, destCurrency));

  if (currency === srcCurrency) {
    fixedAmt = new BigNumber(1).div(fixedAmt);
  }
  return new BigNumber(amount).times(fixedAmt)
    .toFixed(fixedNumber, BigNumber.ROUND_HALF_EVEN);
}
