/**
 * 用户微信相关信息仓储
 * 包含关注公众号信息、微信头像昵称等
*/

export interface  UserWxInfo{
  /**
   * 微信头像url
  */
  avatar?: string,

  /**
   * 微信昵称
  */
  nickname?: string
}

/**
 * 用户微信信息仓储
*/
export interface UserWxInfoRepository{
  /**
   * 查询用户微信信息
  */
  queryUserWxInfo: () => Promise<{
    user_info?: UserWxInfo
  }|undefined>;

  /**
   * 通过微信公众号H5 OAuth 方式保存用户微信信息
  */
  saveUserWxInfoByH5OAuth: (code: string) => Promise<{
    user_info?: UserWxInfo
  }|undefined>;


}

