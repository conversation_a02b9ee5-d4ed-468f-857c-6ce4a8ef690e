/**
 * 用户微信信息实体
*/

import { UserWxInfo } from '../repository';

export class UserWxEntity {
  /**
   * 微信头像url
  */
  private _avatar?: string;

  /**
   * 微信昵称
  */
  private _nickname?: string;

  constructor(userWxInfo?: UserWxInfo) {
    this._avatar = userWxInfo?.avatar;
    this._nickname = userWxInfo?.nickname;
  }

  /**
   * 实体保存了微信头像或昵称
  */
  get hasAvatarOrNickname() {
    return !!(this._avatar || this._nickname);
  }

  /**
   * 获取用户微信头像url
  */
  get avatar() {
    return this._avatar;
  }

  /**
   * 获取用户微信昵称
  */
  get nickname() {
    return this._nickname;
  }
}
