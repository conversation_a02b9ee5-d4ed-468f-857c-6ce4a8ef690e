
// 基础返回
export interface BaseRes {
  retcode: string;
  retmsg: string
}
// 登录渠道
export enum LoginChannel {
  H5 = 0,
  Miniapp = 1
}
// 多语言key
export enum MultiLangKey {
  ZH_CN = 'zh-cn',
  ZH_HK = 'zh-hk',
}
// 多语言文案
export interface MultiLangStr {
  [MultiLangKey.ZH_CN]: string;
  [MultiLangKey.ZH_HK]: string;
}
/**
 * bi上报参数
 * @param tag 埋点事件
 * @param ext 埋点拓展字段
 */
export interface BiParams {
  tag: string;
  ext?: Record<string, string>;
}
