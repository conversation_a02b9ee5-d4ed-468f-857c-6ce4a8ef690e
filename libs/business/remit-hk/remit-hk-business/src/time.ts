import { globalRemit } from './index';
/**
 *
 * @param startTime 开始时间 datetime形式 2020/11/12 12:20
 * @param endTime 结束时间 datetime形式 2020/11/12 12:20
 * @param time 对比时间 如果不填 默认取服务器时间，如果填了应该要为字符串
 */
export function isTimeValidate(startTime: string, endTime: string, time?: string) {
  const { getServerTime } = globalRemit;
  console.log('startTime', startTime, 'endTime', endTime);
  const newStartTime = startTime.replace(/-/g, '/');
  const newEndTime = endTime.replace(/-/g, '/');
  const curTime = time ? (+new Date(time.replace(/-/g, '/'))) : getServerTime();
  if (+new Date(newStartTime) < curTime && curTime < +new Date(newEndTime)) {
    return true;
  }
  return false;
}
