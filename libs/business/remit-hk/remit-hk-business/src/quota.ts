/* eslint-disable camelcase */
/**
 * 限额处理
 */

import { fenToYuan, toThousands, getPointNumber } from './currency';
import { CURRENCY, CddState, EkycState, QuotaState, StdKycState } from '@tencent/remit-hk-types';
import { globalRemit } from '.';
export interface QuotaReq {
  quota_state: QuotaState;
  remain_quota: string;
  senior_quota: string;
  std_kyc_state: StdKycState;
  cdd_state: CddState;
  ekyc_state: EkycState;
}

interface QuotaConfig {
  text: string;
  linkUrl?: string;
  linkText?: string;
  hottag?: string;// 上报
  click?: Function;// 点击时触发事件
}

/**
 * 限额触发页面
 */
export enum QuotaType {
  HOMT = 'home',
  PAY = 'pay'
}

// type QuotaConfigRes = {
//   [key in SENIOR_KYC_STATE]: {
//     [key in Exclude<QuotaState, QuotaState.NORMAL>]?: QuotaConfig;
//   };
// };
/**
 * 限额数据格式化
 * @param data
 */
function getQuotaData(data: QuotaReq) {
  const pointNumber = getPointNumber(CURRENCY.HKD);
  // 这里要求百分号展示 取整
  return Object.assign({}, data, {
    remain_quota: `HK$${toThousands(
      `${parseInt(`${fenToYuan(data.remain_quota, CURRENCY.HKD)}`, 10)}`,
      pointNumber,
      ',',
    )}`,
    senior_quota: `HK$${toThousands(
      `${parseInt(`${fenToYuan(data.senior_quota, CURRENCY.HKD)}`, 10)}`,
      pointNumber,
      ',',
    )}`,
  });
}

export default function createQuota({
  onRaseQuota,
  kyc,
}: {
  onRaseQuota: () => void
  kyc: {
    getKycUrl: () => string
  }
}) {
  const { i18n, CustomException } = globalRemit;
  // 首页 - 文案 - 1 - 超过限额
  const getQuotaDataHomeDefault = (quotaData: QuotaReq) => ({
    [QuotaState.COUNT_LIMIT]: {
      text: i18n('单笔最大汇款额度{{remain_quota}}', {
        remain_quota: quotaData.remain_quota,
      }),
    },
    [QuotaState.DAY_LIMIT]: {
      text: i18n('单日剩余最大汇款额度{{remain_quota}}', {
        remain_quota: quotaData.remain_quota,
      }),
    },
    [QuotaState.MONTH_LIMIT]: {
      text: i18n('单月剩余最大汇款额度{{remain_quota}}', {
        remain_quota: quotaData.remain_quota,
      }),
    },
  });
  // 首页 - 文案 - 2 - 提升额度
  const getQuotaDataHomeRaise = (quotaData: QuotaReq) => ({
    [QuotaState.COUNT_LIMIT]: {
      text: i18n(
        '单笔最大汇款额度{{remain_quota}}，完成高级身份认证，单笔额度可提高至{{senior_quota}}。',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去提额'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.DAY_LIMIT]: {
      text: i18n(
        '单日剩余最大汇款额度{{remain_quota}}，完成高级认证，单日额度可提高至{{senior_quota}}。',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去提额'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.MONTH_LIMIT]: {
      text: i18n(
        '单月剩余最大汇款额度{{remain_quota}}，完成高级认证，单月额度可提高至{{senior_quota}}。',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去提额'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
  });
  // 支付页 - 文案 - 1 - 已达最高限额
  const getQuotaDataPayDefault = (quotaData: QuotaReq) => ({
    [QuotaState.COUNT_LIMIT]: {
      text: i18n('单笔最大汇款额度{{remain_quota}}，请修改汇款金额', {
        remain_quota: quotaData.remain_quota,
      }),
    },
    [QuotaState.DAY_LIMIT]: {
      text: i18n('你的汇款金额已超限，今日剩余额度{{remain_quota}}，请修改汇款金额', {
        remain_quota: quotaData.remain_quota,
      }),
    },
    [QuotaState.MONTH_LIMIT]: {
      text: i18n('单月剩余最大汇款额度{{remain_quota}}，请修改汇款金额', {
        remain_quota: quotaData.remain_quota,
      }),
    },
  });
  // 支付页 - 文案 - 2 - 提额审核中
  const getQuotaDataPayReview = (quotaData: QuotaReq) => ({
    [QuotaState.COUNT_LIMIT]: {
      text: i18n('单笔最大汇款额度{{remain_quota}}，如想继续汇款，请修改汇款金额。（你的提额申请还在审核中，预计3个工作日内完成）', {
        remain_quota: quotaData.remain_quota,
      }),
    },
    [QuotaState.DAY_LIMIT]: {
      text: i18n('你的汇款金额已超限，今日剩余额度{{remain_quota}}，如想继续汇款，请修改汇款金额。（你的提额申请还在审核中，预计3个工作日内完成）', {
        remain_quota: quotaData.remain_quota,
      }),
    },
    [QuotaState.MONTH_LIMIT]: {
      text: i18n('单月剩余最大汇款额度{{remain_quota}}，如想继续汇款，请修改汇款金额。（你的提额申请还在审核中，预计3个工作日内完成）', {
        remain_quota: quotaData.remain_quota,
      }),
    },
  });
  // 支付页 - 文案 - 3 - 提额审核失败
  const getQuotaDataPayFail = (quotaData: QuotaReq) => ({
    [QuotaState.COUNT_LIMIT]: {
      text: i18n(
        '单笔最大汇款额度{{remain_quota}}，你可修改申请信息，优享{{senior_quota}}/日额度，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去修改提额申请'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.DAY_LIMIT]: {
      text: i18n(
        '你的提额申请信息有误，今日剩余额度{{remain_quota}}，你可修改申请信息，优享{{senior_quota}}/日额度，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去修改提额申请'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.MONTH_LIMIT]: {
      text: i18n(
        '单月剩余最大汇款额度{{remain_quota}}，你可修改申请信息，优享{{senior_quota}}/日额度，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去修改提额申请'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
  });
  // 支付页 - 文案 - 4 - 提升额度
  const getQuotaDataPayRaise = (quotaData: QuotaReq) => ({
    [QuotaState.COUNT_LIMIT]: {
      text: i18n(
        '单笔最大汇款额度{{remain_quota}}，你可完成高级身份认证提额至{{senior_quota}}，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去提额'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.DAY_LIMIT]: {
      text: i18n(
        '你的汇款金额已超限，今日剩余额度{{remain_quota}}，你可完成高级身份认证提额至{{senior_quota}}，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去提额'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.MONTH_LIMIT]: {
      text: i18n(
        '单月剩余最大汇款额度{{remain_quota}}，你可完成高级身份认证提额至2万HKD，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去提额'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
  });
  // 支付页 - 文案 - 5 - 补充信息
  const getQuotaDataPaySubmit = (quotaData: QuotaReq) => ({
    [QuotaState.COUNT_LIMIT]: {
      text: i18n(
        '单笔最大汇款额度{{remain_quota}}，你可补充信息，优享{{senior_quota}}/日额度，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去补充提额申请'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.DAY_LIMIT]: {
      text: i18n(
        '你的提额申请信息未提交全，今日剩余额度{{remain_quota}}，你可补充信息，优享{{senior_quota}}/日额度，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去补充提额申请'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
    [QuotaState.MONTH_LIMIT]: {
      text: i18n(
        '单月剩余最大汇款额度{{remain_quota}}，你可补充信息，优享{{senior_quota}}/日额度，或修改金额继续汇款',
        {
          remain_quota: quotaData.remain_quota,
          senior_quota: quotaData.senior_quota,
        },
      ),
      linkText: i18n('去补充提额申请'),
      linkUrl: kyc.getKycUrl(),
      click: onRaseQuota,
    },
  });

  /**
 * 首页限额弹窗
 * @param data
 */
  const getQuotaConfigHome = (data: QuotaReq) => {
    const quotaData = getQuotaData(data);
    // 默认文案
    const defaultQuotaConfig = getQuotaDataHomeDefault(quotaData); // 1
    // 提额文案
    const raseQuotaConfig = getQuotaDataHomeRaise(quotaData); // 2

    return {
      [StdKycState.SENIOR_KYC]: defaultQuotaConfig,
      [StdKycState.EKYC]: defaultQuotaConfig,
      [StdKycState.PAYMENT]: {
        [EkycState.INIT]: raseQuotaConfig,
        [EkycState.REVIEWING]: defaultQuotaConfig,
        [EkycState.FAIL]: raseQuotaConfig,
        [EkycState.REJECT]: raseQuotaConfig,
        [EkycState.SUBMITTING]: raseQuotaConfig,
      },
      [StdKycState.PRIMARY_KYC]: {
        [CddState.PRIMARY_SUCC]: {
          [EkycState.INIT]: raseQuotaConfig,
          [EkycState.REVIEWING]: defaultQuotaConfig,
          [EkycState.FAIL]: raseQuotaConfig,
          [EkycState.REJECT]: raseQuotaConfig,
          [EkycState.SUBMITTING]: raseQuotaConfig,
        },
        [CddState.SENIOR_REVIEWING]: {
          [EkycState.INIT]: defaultQuotaConfig,
          [EkycState.REVIEWING]: defaultQuotaConfig, // 正常不会存在这种情况
          [EkycState.FAIL]: defaultQuotaConfig, // 正常不会存在这种情况
          [EkycState.REJECT]: defaultQuotaConfig, // 正常不会存在这种情况
          [EkycState.SUBMITTING]: defaultQuotaConfig, // 正常不会存在这种情况
        },
        [CddState.SENIOR_FAIL]: {
          [EkycState.INIT]: raseQuotaConfig,
          [EkycState.REVIEWING]: defaultQuotaConfig,
          [EkycState.FAIL]: raseQuotaConfig,
          [EkycState.REJECT]: raseQuotaConfig,
          [EkycState.SUBMITTING]: raseQuotaConfig,
        },
      },
    };
  };


  /**
 * 首页限额弹窗
 * @param data
 */
  const getQuotaConfigPay = (data: QuotaReq) => {
    const quotaData = getQuotaData(data);
    // 默认文案
    const defaultQuotaConfig = getQuotaDataPayDefault(quotaData); // 1
    // 审核中文案
    const reviewQuotaConfig = getQuotaDataPayReview(quotaData); // 2
    // 失败文案
    const failQuotaConfig = getQuotaDataPayFail(quotaData); // 3
    // 提额文案
    const raseQuotaConfig = getQuotaDataPayRaise(quotaData); // 4
    // 提交中文案
    const submitQuotaConfig = getQuotaDataPaySubmit(quotaData); // 5


    return {
      [StdKycState.SENIOR_KYC]: defaultQuotaConfig,
      [StdKycState.EKYC]: defaultQuotaConfig,
      [StdKycState.PAYMENT]: {
        [EkycState.INIT]: raseQuotaConfig,
        [EkycState.REVIEWING]: reviewQuotaConfig,
        [EkycState.FAIL]: failQuotaConfig,
        [EkycState.REJECT]: failQuotaConfig,
        [EkycState.SUBMITTING]: submitQuotaConfig,
      },
      [StdKycState.PRIMARY_KYC]: {
        [CddState.PRIMARY_SUCC]: {
          [EkycState.INIT]: raseQuotaConfig,
          [EkycState.REVIEWING]: reviewQuotaConfig,
          [EkycState.FAIL]: failQuotaConfig,
          [EkycState.REJECT]: failQuotaConfig,
          [EkycState.SUBMITTING]: submitQuotaConfig,
        },
        [CddState.SENIOR_REVIEWING]: {
          [EkycState.INIT]: reviewQuotaConfig,
          [EkycState.REVIEWING]: reviewQuotaConfig, // 正常不会存在这种情况
          [EkycState.FAIL]: reviewQuotaConfig, // 正常不会存在这种情况
          [EkycState.REJECT]: reviewQuotaConfig, // 正常不会存在这种情况
          [EkycState.SUBMITTING]: reviewQuotaConfig, // 正常不会存在这种情况
        },
        [CddState.SENIOR_FAIL]: {
          [EkycState.INIT]: raseQuotaConfig,
          [EkycState.REVIEWING]: reviewQuotaConfig,
          [EkycState.FAIL]: failQuotaConfig,
          [EkycState.REJECT]: failQuotaConfig,
          [EkycState.SUBMITTING]: submitQuotaConfig,
        },
      },
    };
  };


  /**
 * 获取限额配置
 * 首页限额跟支付限额的弹窗是不一样的 需要做区别配置
 * @param data
 */
  function getQuotaStateConfig(data: QuotaReq, type: QuotaType): QuotaConfig {
  // 限额文案映射配置
    const quotaConfigMap = type === QuotaType.HOMT ? getQuotaConfigHome(data) : getQuotaConfigPay(data);
    const quotaData = getQuotaData(data);

    // 默认限额文案
    const defaultQuotaConfig = type === QuotaType.HOMT
      ? getQuotaDataHomeDefault(quotaData)[data.quota_state]
      : getQuotaDataPayDefault(quotaData)[data.quota_state];

    try {
      switch (data.std_kyc_state) {
        case StdKycState.SENIOR_KYC:
        case StdKycState.EKYC:
          return quotaConfigMap[data.std_kyc_state][data.quota_state] || defaultQuotaConfig;
        case StdKycState.PAYMENT:
          return quotaConfigMap[data.std_kyc_state][data.ekyc_state][data.quota_state] || defaultQuotaConfig;
        case StdKycState.PRIMARY_KYC:
          return (
            quotaConfigMap[data.std_kyc_state][data.cdd_state][data.ekyc_state][data.quota_state]
          || defaultQuotaConfig
          );
        default:
          return defaultQuotaConfig;
      }
    } catch (err) {
      throw new CustomException(data, 'getQuotaStateConfigFail', `获取限额配置失败,std_kyc_state:${data.std_kyc_state},quota_state:${data.quota_state}`);
    }
  }

  return {
  /**
   * 是否超过限额
   * @param quotaState 限额状态
   */
    isQuotaValid(quotaState: QuotaState) {
      console.log(quotaState, '限额参数');
      return quotaState === QuotaState.NORMAL;
    },
    getQuotaStateConfig,
  };
}
