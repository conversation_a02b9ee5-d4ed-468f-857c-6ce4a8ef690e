<template>
  <template v-if="receiveOrder?.order || isOtherPeople">
    <!-- 其他人收款异常情况 -->
    <ReceiverOther v-if="isOtherPeople" />
    <!-- 汇款人视角 -->
    <Sender
      v-else-if="receiveOrder?.isSender"
      :receive-order="receiveOrder"
    />
    <!-- 收款人视角 -->
    <Receiver
      v-else-if="receiveOrder?.isReceiver"
      :receive-order="receiveOrder"
      :origin-list-id="originListId"
    />
  </template>
</template>

<script setup lang="ts">
import { toRefs, watch } from 'vue';
import Sender from './components/sender.vue';
import Receiver from './components/receiver-index.vue';
import { useReceiveOrder } from './hooks/use-receive-order';
import ReceiverOther from './components/receiver-other.vue';
import { globalRemit } from '@tencent/remit-hk-business';

interface Props{
  listid: string
  /**
 * 原始未加密订单号
 */
  originListId: string;
}

const props = defineProps<Props>();
const { listid } = toRefs(props);
const { receiveOrder, initReceiveOrder, isOtherPeople } = useReceiveOrder();
// 注意 这里必须要在setup阶段设置 如果子组件落后于页面创建 会导致onshow不触发
initReceiveOrder(listid);
watch(receiveOrder, () => {
  const { biHottag } = globalRemit;
  biHottag('remit.newhk2cn.collection.brow', {
    userType: receiveOrder.value?.isReceiver ? '1' : '2', // 1-收款人, 2-汇款人
  });
});
</script>

<style scoped lang="scss">

</style>
