/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";

@weuiGalleryOprHeight: 60px;
.weui-gallery {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #000000;
    z-index: 1000;
}
.weui-gallery__img {
    position: absolute;
    top: constant(safe-area-inset-top);
    top: env(safe-area-inset-top);
    right: constant(safe-area-inset-right);
    right: env(safe-area-inset-right);
    bottom: calc(@weuiGalleryOprHeight ~"+ constant(safe-area-inset-bottom)");
    bottom: calc(@weuiGalleryOprHeight ~"+ env(safe-area-inset-bottom)");
    left: constant(safe-area-inset-left);
    left: env(safe-area-inset-left);
    background: center center no-repeat;
    background-size: contain;
}
.weui-gallery__opr {
    position: absolute;
    right: constant(safe-area-inset-right);
    right: env(safe-area-inset-right);
    bottom: constant(safe-area-inset-bottom);
    bottom: env(safe-area-inset-bottom);
    left: constant(safe-area-inset-left);
    left: env(safe-area-inset-left);
    background-color: #0D0D0D;
    color: #FFFFFF;
    line-height: @weuiGalleryOprHeight;
    text-align: center;
}
.weui-gallery__del {
    display: block;
}
