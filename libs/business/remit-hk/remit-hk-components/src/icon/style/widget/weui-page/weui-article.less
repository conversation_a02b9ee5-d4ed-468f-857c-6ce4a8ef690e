/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";

.weui-article {
    padding: 24px 16px;
    padding:24px calc(16px ~"+ constant(safe-area-inset-right)") calc(24px ~"+ constant(safe-area-inset-bottom)") calc(16px ~"+ constant(safe-area-inset-left)");
    padding:24px calc(16px ~"+ env(safe-area-inset-right)") calc(24px ~"+ env(safe-area-inset-bottom)") calc(16px ~"+ env(safe-area-inset-left)");
    font-size: 17px;
    color:rgba(0,0,0,.9);
    section {
        margin-bottom: 1.5em;
    }
    h1 {
        font-size: 22px;
        font-weight:700;
        margin-bottom: .9em;
        line-height:1.4;
    }
    h2 {
        font-size: 17px;
        font-weight:700;
        margin-bottom: .34em;
        line-height:1.4;
    }
    h3 {
        font-weight:700;
        font-size: 15px;
        margin-bottom: .34em;
        line-height:1.4;
    }
    * {
        max-width: 100%;
        box-sizing: border-box;
        word-wrap: break-word;
    }
    p {
        margin: 0 0 .8em;
    }
}
