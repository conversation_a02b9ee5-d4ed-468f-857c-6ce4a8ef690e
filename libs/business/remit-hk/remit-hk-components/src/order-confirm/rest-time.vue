<template>
  <div class="rest">
    {{ $t('剩余支付时间') }}
    <span class="g-color-orange">
      <p-count-down
        :time="resetTime"
        auto-start
        format="mm:ss"
        @finish="onTimeout"
      />
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { globalRemit } from '@tencent/remit-hk-business';

const props = defineProps({
  /**
   * 当状态为待支付时返回，单位：秒
   */
  remainPayTime: {
    type: String,
    default: '0',
  },
});

// 倒计时 30s
const resetTime = computed(() => +props.remainPayTime * 1000);

const { i18n: $t } = globalRemit;


const emits = defineEmits(['timeOut']);
/** 倒计时结束触发 → 超时 */
const onTimeout = () => {
  emits('timeOut');
};

</script>
<style lang="less">
@color: rgba(0, 0, 0, 0.56);
.rest{
  color: @color;
  @apply mt-160rpx;
  @apply text-center;
  @apply text-28rpx;
  @apply leading-38rpx;
  @apply mb-48rpx;
  .g-color-orange{
    @apply inline-block;
    @apply w-72rpx;
  }
}
</style>
