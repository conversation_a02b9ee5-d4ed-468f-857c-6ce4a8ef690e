import { DetailQryResp } from '@tencent/remit-hk-types';
import { IOrderRepository } from '../repository/order.repository.interface';
import { IOrderService } from './order.service.interface';
import { OrderEntity } from '../entity/order.entity';

export class OrderService implements IOrderService {
  constructor(private readonly orderRepository: IOrderRepository) {
  }
  // 查询订单详情
  async detailQry(listid: string) {
    return  this.orderRepository.detailQry(listid);
  }
  // 创建基础订单实体
  createOrderEntity(data: DetailQryResp) {
    return new OrderEntity(data);
  }
}
