
<template>
  <view
    class="flex items-center observer-container"
  >
    <view
      v-stat.brow.click="'kyc_authorize_form.form_item'"
      :data-stat="JSON.stringify({ verify_status: hasValue ? '1': '0' })"
      class="flex-1"
    >
      <payment-password-verify
        :get-sign-func="config.props.getSignFunc"
        @success="onKycVerifySuc"
        @fail="onKycVerifyFail"
        @get-sign-fail="onGetSignFail"
      >
        <view
          v-if="hasValue"
          class="inline-block"
        >
          <view
            class="success-icon"
          /><text class="value">
            已授权
          </text>
        </view>
        <text
          v-else
          class="placeholder"
        >
          {{ config.placeholder }}
        </text>
      </payment-password-verify>
    </view>
    <p-icon
      size="32rpx"
      class="arrow"
      name="arrow-right"
    />
  </view>
</template>
<script setup lang="ts">
import 'miniprogram-api-typings';
import { computed, toRef } from 'vue';
import { PaymentPasswordVerifyElement } from '../types';
import PaymentPasswordVerify from '../../payment-password-verify/index.vue';
import { PaymentPasswordVerifyEventName, PaymentPasswordVerifySuccessParams } from '../../payment-password-verify/types';
type Props = {
  // 值
  modelValue: PaymentPasswordVerifySuccessParams,
  config: PaymentPasswordVerifyElement,
};
const emit = defineEmits(['update:modelValue', 'change', 'fail']);
const props = defineProps<Props>();
const modelValue = toRef(props, 'modelValue');

function onKycVerifySuc(data: PaymentPasswordVerifySuccessParams) {
  // TODO 验证埋点
  emit('update:modelValue', data);
  emit('change', data);
}

function onKycVerifyFail(err) {
  props.config.props?.action?.(PaymentPasswordVerifyEventName.fail, err);
}

function onGetSignFail(err) {
  props.config.props?.action?.(PaymentPasswordVerifyEventName.getSignFail, err);
}
const hasValue = computed(() => !!modelValue.value?.token);

</script>
<style scoped lang="scss">
@import "../scss/common.scss";
@import "../scss/icon.scss";
.placeholder{
  @include placeholder;
  padding-right: 32rpx;
}
.value{
  @include value;
}
.arrow{
  @include arrow;
}
</style>
