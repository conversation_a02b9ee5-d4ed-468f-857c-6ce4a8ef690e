/*
 * @Author: toby <EMAIL>
 * @Date: 2023-03-16 13:20:07
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-05-10 15:05:59
 * @FilePath: /ppd-uniapp/apps/tuition/src/components/form/types.ts
 * @Description:
 */
import type { RuleItem as RuleItemTmp } from 'async-validator/dist-types';
import type { ComputedRef } from 'vue';
import { GetPaymentPasswordSignFunc } from '../payment-password-verify/types';
export interface RuleItem extends RuleItemTmp {
  /** 需要触发校验的字段，不填则所有字段变更都校验 */
  triggerKeys?: string[]
};

export interface SubTitleConfig {
  [key: string]: {
    title: string,
    class: string,
  }
}

export interface KeyObj {
  [key: string | number]: any
};
interface  BaseFormElement<T extends string = string>{
  /**
   * 参数名称
   */
  id: T;
  /** 输入提示、输入示例 */
  placeholder: string;
  /**
   * 参数描述
   */
  label: string;
  /**
   * 辅助提示
   */
  labelSuffixContent?: string[],
  /**
   * 描述
   */
  desc?: ComputedRef<string> | string;
  /**
   * 描述样式
   */
  descStyle?: string;
  /**
   * 提示信息
   */
  help?: string;
  /** 左侧label宽度 */
  labelWidth?: string;
  /** 是否为禁止输入，可读输入框 */
  disabled?: boolean;
  /**
   * 初始值
   */
  initValue?: unknown;

  // 额外样式
  style?: string;
  // 是否隐藏
  hidden?: boolean;
  /**
   * 格式化数据
   * @param val 数据
   * @returns
   */
  formatter?: (value: any, formData: any) => any;
  /** 校验规则 */
  rules?: RuleItem[];
  /** 需要同时校验的字段 */
  relationFields?: string[],
  /**
   * 是否只读
   */
  readonly?: boolean,
  formatLabel?: (value: any, formData?: any) => string,

}


export interface TextAreaElement<T extends string = string> extends BaseFormElement<T>{
  component: 'textarea'
}

export interface RegionElement<T extends string = string> extends BaseFormElement<T>{
  component: 'region',
  props?: {
    // 选择器层级
    level?: 'province' | 'city' | 'region' | 'sub-district'
  }
}
export interface TimeElement<T extends string = string> extends BaseFormElement<T>{
  component: 'time',
  props?: {
    // 表示有效日期范围的开始，字符串格式为"YYYY-MM-DD"
    start?: string,
    // 表示有效日期范围的结束，字符串格式为"YYYY-MM-DD"
    end?: string,
  }
}

export interface RadioElement<T extends string = string> extends BaseFormElement<T>{
  component: 'radio',
  // 枚举
  enums?: {
    // 显示值
    label: string,
    // 实际值
    value: string | number
  }[]
}

export interface CustomActionElement<T extends string = string> extends BaseFormElement<T>{
  component: 'customAction',
  // 枚举
  props?: {
    /**
     * 触发时间
     * @param change 触发变更
     * @param val 值
     */
    action?: (change: (value: any) => void, val: unknown) => void,
    // 格式化显示值
    formatLabel?: (value: any) => string,
  }
}
export interface AuthPhoneElement<T extends string = string> extends BaseFormElement<T>{
  component: 'authPhone',
  // 枚举
  props?: {
    // 显示值
    action?: () => void,
  }
}

export interface PaymentPasswordVerifyElement<T extends string = string> extends BaseFormElement<T>{
  component: 'paymentPasswordVerify',
  // 枚举
  props: {
    action?: (eventName: string, err?: any) => void,
    getSignFunc: GetPaymentPasswordSignFunc
  }
}

export interface InputElement<T extends string = string> extends BaseFormElement<T>{
  component: 'input',
  props?: {
    // 类型
    type?: 'text' | 'number' | 'idcard' | 'digit' | 'password'
  }
}

export interface InputDateElement<T extends string = string> extends BaseFormElement<T>{
  component: 'inputDate',
  props?: {
    /**
     * 分割字符，如配置为-，则会返回YYYY-MM-DD
     */
    splitChar?: string
  }
}

export type  UiFormElement<T extends string = string> = TextAreaElement<T> | RegionElement<T> |
TimeElement<T> | RadioElement<T > | CustomActionElement<T > |
AuthPhoneElement<T > |
PaymentPasswordVerifyElement<T> | InputElement<T > | InputDateElement<T>;
