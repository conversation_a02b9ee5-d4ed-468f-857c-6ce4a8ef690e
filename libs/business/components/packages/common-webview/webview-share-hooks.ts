/**
 * webview 分享
 * 当webview页面需要使用分享能力时，可引入此hooks
 * 此hooks只给页面层使用
 *
 * 单独拆分一个share hooks的背景：
 * 小程序分享一定要挂在页面方法上。故需要单独一个hooks给页面使用
*/

import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { WebViewOptions, WebViewShareInfo, UseCustomShare } from './type';

export type EventMessage = {
  shareInfo?: {
    title: string,
    imageUrl: string,
    path: string,
    shareKey: string,
    // 优先级，越大越靠前
    priority: number
  }

};
export function useWebViewShareHooks() {
  /**
   * 小程序分享信息
  */
  const shareInfo = ref<WebViewShareInfo|{}>({});

  /**
   * 分享key，适用于上报等场景
  */
  const shareKey = ref<string>('');

  /**
   * @private
   * 初始化分享
  */
  function _initShareOnLoad(
    shareTitle?: string,
    sharePath?: string,
    shareImageUrl?: string,
    useCustomShare?: UseCustomShare,
  ) {
    if (shareTitle && sharePath && shareImageUrl) {
      shareInfo.value = {
        title: shareTitle,
        imageUrl: shareImageUrl,
        path: sharePath,
      };
      return;
    }

    /**
     * 如果未传入上述字段，则禁用当前页面的分享
    */
    if (`${useCustomShare}` !== UseCustomShare.YES) {
      // 如果初始化未传入分享配置，且未传入自定义分享标志位，则禁用分享
      uni.hideShareMenu({
        hideShareItems: ['shareAppMessage', 'shareTimeline'],
      });
    }
  }

  /**
   * 基于postmessage初始化分享
  */
  function onPostMessageInitShare(e: WechatMiniprogram.CustomEvent) {
    const data = e.detail.data as EventMessage[];
    // 过滤出分享信息，并进行优先级排序
    const { shareInfo: msgShareInfo } = data.filter(item => item.shareInfo)
      .sort((pre, next) => {
        const prePriority = pre?.shareInfo?.priority ?? 0;
        const nextPriority = next?.shareInfo?.priority ?? 0;
        // 优先级一样，则后面一项排前面
        if (nextPriority  === prePriority) {
          return -1;
        }
        return nextPriority - prePriority;
      })[0];


    if (msgShareInfo) {
      console.log('common_webview_onPostMessageInitShare', e, msgShareInfo);
      shareInfo.value = msgShareInfo;
      shareKey.value = msgShareInfo?.shareKey;
    }
  }

  onLoad((options: Partial<WebViewOptions>) => {
    // 初始化页面分享
    _initShareOnLoad(
      options.shareTitle,
      options.sharePath,
      options.shareImageUrl,
      options.useCustomShare,
    );
  });

  return {
    shareInfo,
    onPostMessageInitShare,
    shareKey,
  };
}
