/*
 * @LastEditTime: 2023-10-19 20:12:33
 * @FilePath: /ppd-uniapp/libs/business/components/packages/privacy-dialog-polyfill/composition.ts
 * @Description: 隐私协议弹窗相关composition
 */
import { ref } from 'vue';
import { getDialogTexts } from './dialog-text';
import { privacyPolyfill } from './privacy-polyfill';
import { getCurPage } from '@tencent/ppd-common/src/route';
/**
 * 待处理回调队列
 * 如果调用 resolve({ buttonId: 'agree-btn'， event:'agree' })，则触发当前 onNeedPrivacyAuthorization 事件的原隐私接口或组件会继续执行
 */
const pendingQueue: Set<WechatMiniprogram.OnPrivacyAuthorizationCallback> = new Set();

/**
 * 隐私协议名称缓存
 */
let cachedName = '';

// 隐私相关
export const usePrivacy = (emit) => {
  /**
   * 是否展示隐私授权弹窗
   */
  const isShowDialog = ref(false);
  /**
   * 隐私协议名称
   */
  const protocolName = ref('《小程序隐私保护指引》');
  /**
   * 自定义隐私弹窗文案内容
   * 根据不同api适配不同文案
   */
  const customDialogText = ref('');
  /**
   * 自定义Toast内容
   * 根据不同api适配不同toast文案
   */
  const customToastText = ref('');
  /**
   * 隐私协议来源
   */
  const referrer = ref('');
  /**
   * 获取小程序隐私协议名称
   */
  /**
   * 绑定事件的页面
   */
  let curPage: string | undefined = '';

  /**
   * 触发隐私弹窗展示次数（拒绝或同意后清零），用于判断弹窗展示是否异常
   */
  let showTimes = 0;

  if (!cachedName) {
    privacyPolyfill.getPrivacySetting().then((res: { privacyContractName: string }) => {
      cachedName = res.privacyContractName;
      protocolName.value = cachedName;
    })
      .catch((err) => {
        console.error('获取隐私协议名称失败', err);
      });
  } else {
    protocolName.value = cachedName;
  }

  /**
   * 注册微信回调监听
   */
  const registerListener = async () => {
    console.log('注册onNeedPrivacyAuthorization');
    try {
      curPage = getCurPage()?.route;
      privacyPolyfill.onNeedPrivacyAuthorization((resolve, eventInfo) => {
        console.log('需要引导用户进行隐私授权');
        referrer.value = eventInfo?.referrer || '';
        emit('onNeedPrivacyAuthorization', eventInfo?.referrer || '');
        pendingQueue.add(resolve);
        const dialogText = getDialogTexts(eventInfo?.referrer || '');
        customDialogText.value = dialogText.customDialogText;
        customToastText.value = dialogText.customToastText;
        showDialog();
      });
    } catch (error) {
      console.error('注册微信回调监听失败：', error);
      emit('registerListenerErr', error);
    }
  };

  /**
   * 清空计数
   */
  const clearShowTimes = () => {
    showTimes = 0;
  };

  /**
   * 展示隐私授权弹窗
   */
  const showDialog = () => {
    showTimes += 1;
    if (isShowDialog.value) {
      if (showTimes > 1) {
        emit('triggerShowDialogRepeatErr', showTimes);
        // 当触发2次弹窗无反应时，跳转进入兜底隐私协议页面进行授权
        showTimes === 2 && privacyPolyfill.dealOpenDialogErr(referrer.value || '');
      };
      return;
    }
    privacyPolyfill.showAuth();
    emit('onShowDialog');
    isShowDialog.value = true;

    // 隐私弹窗曝光
    pendingQueue.forEach((resolve) => {
      resolve({ event: 'exposureAuthorization' });
    });

    if (curPage !== getCurPage()?.route) {
      emit('showPrivacyDialogErr', curPage);
      privacyPolyfill.dealOpenDialogErr(referrer.value || '');
    }
  };

  /**
   * 关闭隐私授权弹窗
   */
  const hideDialog = () => {
    clearShowTimes();
    if (!isShowDialog.value) {
      return;
    }
    emit('onHideDialog');
    isShowDialog.value = false;
  };


  /**
   * 跳转小程序隐私指引
   */
  const openPrivacyContract = () => {
    privacyPolyfill.openPrivacyContract().then(() => {
      emit('onOpenPrivacyContract');
    })
      .catch((err) => {
        emit('openPrivacyContractErr', err);
      });
  };

  /**
   * 处理协议回调
   */
  function resolvePending(isConfirm: boolean) {
    // 回调所有pending中的方法
    pendingQueue.forEach((resolve) => {
      if (isConfirm) {
        // 用户点击确认，原隐私接口或组件会继续执行
        resolve({ buttonId: 'mp-privacy-agree-btn', event: 'agree' });
      } else {
        // 用户点击拒绝，原隐私接口或组件会失败并返回 API:fail privacy permission is not authorized 的错误信息
        resolve({ event: 'disagree' });
      }
    });
    pendingQueue.clear();
    if (isConfirm) {
      privacyPolyfill.authorize();
    } else {
      privacyPolyfill.reject();
    }

    hideDialog();
  }

  return {

    /**
     * 是否展示隐私授权弹窗
     */
    isShowDialog,
    /**
     * 隐私协议名称
     */
    protocolName,
    /**
     * 自定义隐私弹窗文案内容
     * 根据不同api适配不同文案
    */
    customDialogText,
    /**
     * 自定义Toast内容
     * 根据不同api适配不同toast文案
     */
    customToastText,
    /**
     * 注册微信回调监听
     */
    registerListener,
    /**
     * 展示隐私授权弹窗
     */
    showDialog,
    /**
     * 关闭隐私授权弹窗
     */
    hideDialog,
    /**
     * 跳转小程序隐私指引
     */
    openPrivacyContract,
    /**
     * 处理协议回调
     */
    resolvePending,
    /**
     * 隐私协议来源
     */
    referrer,
  };
};

/**
 * 查询隐私授权情况
 * 是否需要用户授权隐私协议（如果开发者没有在[mp后台-设置-服务内容声明-用户隐私保护指引]中声明隐私收集类型则会返回false；
 * 如果开发者声明了隐私收集，且用户之前同意过隐私协议则会返回false；如果开发者声明了隐私收集，且用户还没同意过则返回true；
 * 如果用户之前同意过、但后来小程序又新增了隐私收集类型也会返回true）
 */
export const usePrivacySetting = () => {
  /**
   * 获取是否需要用户授权
   */
  const getIsNeedAuthorization = () => new Promise<boolean>((resolve, reject) => {
    // #ifdef MP-WEIXIN
    privacyPolyfill.getIsNeedAuthorization().then((res) => {
      resolve(res);
    })
      .catch((err) => {
        reject(err);
      });
    // #endif
    // #ifndef MP-WEIXIN
    resolve(false);
    // #endif
  });
  return {

    /**
     * 获取是否需要用户授权
     */
    getIsNeedAuthorization,
  };
};
