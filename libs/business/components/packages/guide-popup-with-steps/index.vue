<template>
  <view>
    <p-popup
      :show="isShow"
      :round="40"
      @close="onConfirm"
    >
      <view
        v-if="config"
        class=""
        style=""
      >
        <scroll-view
          scroll-y
          class="ph-scroll-view"
          :show-scrollbar="false"
        >
          <view class="pl-32rpx pr-32rpx">
            <!-- 标题 -->
            <view
              v-if="config.title"
              class="title4-sem color-text-title pt-80rpx pl-6rpx text-center font-700"
            >
              {{ config.title }}
            </view>
            <!-- 描述 -->
            <view
              v-if="config.desc"
              class="text5-reg color-text-title mt-24rpx"
            >
              {{ config.desc }}
            </view>
            <slot name="customContent" />
            <!-- tips -->
            <view
              v-if="config.tips"
              class="color-text-title mt-56rpx mb-24rpx lh-44rpx text-32rpx font-500"
            >
              {{ config.tips }}
            </view>
            <!-- 图片引导 -->
            <view class="flex flex-col justify-between w-670rpx">
              <template
                v-for="(item, index) in config.steps"
                :key="index"
              >
                <view
                  class="w-full"
                  :style="item.style"
                >
                  <p-image
                    width="100%"
                    height="auto"
                    mode="widthFix"

                    :src="item.stepImg"
                  />
                </view>
              </template>
            </view>
            <!-- 确认按钮 -->
            <view
              v-stat.click="'fail_guide_popup.confirm'"
              class="flex items-center justify-center mt-24rpx mb-32rpx"
              :data-stat="JSON.stringify({failType: biReportFailType || config.title})"
            >
              <navigator
                v-if="isBtnExitMiniprogram"
                open-type="exit"
                target="miniProgram"
              >
                <p-button
                  type="primary"
                  :size="'large'"
                  class="self-center"
                  @click="onConfirm"
                >
                  我知道了
                </p-button>
              </navigator>

              <p-button
                v-else
                type="primary"
                :size="'large'"
                class="self-center"
                @click="onConfirm"
              >
                我知道了
              </p-button>
            </view>
            <!-- 客服按钮 -->
            <view
              v-if="config.showCustomService"
              v-stat.click="'fail_guide_popup.customService'"
              class="flex items-center justify-center mt-24rpx mb-32rpx color-primary-normal text-34rpx font-bold"
              :data-stat="JSON.stringify({failType: biReportFailType || config.title})"
            >
              <weremitKefu>联系客服</weremitKefu>
            </view>
            <slot name="afterButton" />
          </view>
          <safeBottom
            ios-fit="0px"
            android-fit="0px"
          />
        </scroll-view>
      </view>
    </p-popup>
  </view>
</template>
<script setup lang="ts">
import { GuidePopUpConfig } from './types';
import { watch, toRef } from 'vue';
import { biBrow, biClick } from '@tencent/fit-bi-sdk';
import safeBottom from '../safe-bottom/index.vue';
import weremitKefu from '../weremit-kefu/index.vue';

export type RealNameFailProps = {
  /**
   * 错误弹窗配置
   */
  config: GuidePopUpConfig,
  /**
   * 是否展示弹窗
   */
  isShow: boolean,
  /**
   * 点击【知道了】
   */
  onConfirm: () => void,
  /**
   * 点击按钮是否退出小程序
   */
  isBtnExitMiniprogram?: boolean,
  /**
   * 埋点上报用的失败类型
  */
  biReportFailType?: string
};


const props = withDefaults(defineProps<RealNameFailProps>(), {
  config: () => ({
    title: '请上传身份证影印件',
    desc: '抱歉！你未上传身份证照片或照片信息已失效，暂时无法使用收款服务。根据国家跨境支付相关管理规定，微信支付（财付通）需要你的身份证信息。请在微信内重新上传身份证照片，以便使用本服务！',
    tips: '如何上传身份证照片？',
    showCustomService: false,
    steps: [],
  }),
  isShow: true,
  isShowCustom: false,
  onConfirm: () => {},
  isBtnExitMiniprogram: false,
  biReportFailType: undefined,
});
const show = toRef(props, 'isShow');
watch(() => show.value, () => {
  if (show.value) {
    biBrow('fail_guide_popup.show', {
      failType: props.biReportFailType ?? props.config.title ?? '',
    });
  } else {
    biClick('fail_guide_popup.close', {
      failType: props.biReportFailType ?? props.config.title ?? '',
    });
  }
});

</script>
<style scoped lang="scss">
@import './uno.scss';
.step::before{
  content: '';
  display: block;
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  opacity: 0.7;
  background: #9AFF94;
  top: 5rpx;
  left: 8rpx;
  border-radius: 50%;
  z-index: -1;
}
.color {
  color: #3089F0;
}
.ph-scroll-view {
  max-height: calc(100vh - 88rpx);
}
</style>
