
import {
  Adaptor,
  RequestBottomException,
  AdaptorRequestResolve,
  HttpResponse,
  TransformRequestOptions,
  RequestOptions,
  Request,
} from '@tencent/ppd-common/src/request-core';

/**
 * 接口的其他选项
 */
export type UniDownloadFileConfig = Omit<UniApp.DownloadFileOption, 'url' | 'complete' | 'success' | 'fail'| 'data'>;

/**
 * 下载请求的配置
 */
export type UniDownloadFileRequestOptions = RequestOptions<UniDownloadFileConfig>;

/**
 * 请求底层异常类
 */
export class UniDownloadFileBottomExceptions extends RequestBottomException<
unknown,
TransformRequestOptions<UniDownloadFileConfig>
> {
  name = 'UniDownloadFileBottomExceptions';
}

/**
 * 下载请求响应
 */
export type UniDownloadFileResponse<BaseResponse = unknown> = HttpResponse<
UniDownloadFileConfig,
UniApp.DownloadSuccessData['tempFilePath'],
UniApp.DownloadSuccessData,
BaseResponse
>;

/**
 * uni.downloadFile的适配类
 */
export class UniDownloadFileAdaptor<BaseResponse extends string> extends Adaptor<
UniDownloadFileConfig, UniApp.DownloadSuccessData, BaseResponse
> {
  /**
   * uniapp请求底层适配器
   * @param options 请求配置，options会多出params，params已在上层会被合并到url上，这个地方这个配置是预留项，没有需求应使用
   * @param adaptorRequestResolve 请求处理函数
   */
  adapter(
    options: TransformRequestOptions<UniDownloadFileConfig>,
    adaptorRequestResolve: AdaptorRequestResolve<UniDownloadFileConfig, UniApp.DownloadSuccessData, BaseResponse>,
    request: Request<UniDownloadFileConfig,
    UniApp.DownloadSuccessData,
    BaseResponse>,
  ): void {
    const task = uni.downloadFile({
      url: options.apiURL,
      ...options.config,
      success: (response) => {
        adaptorRequestResolve.success(new HttpResponse({
          options,
          statusCode: response.statusCode,
          data: response.tempFilePath as BaseResponse,
          rawResponse: response,
        }, request));
      },
      fail(err) {
        adaptorRequestResolve.fail(new UniDownloadFileBottomExceptions(
          'downloadFile::fail',
          err,
          options,
        ));
      },
    });
    adaptorRequestResolve.onCancel(() => {
      if (task) {
        task.abort();
      }
    });
  }
}
