// 来自vueuse，修改兼容小程序
import type { Awaitable, ConfigurableEventFilter, ConfigurableFlush, MaybeComputedRef, RemovableRef } from '@vueuse/shared';
import { isFunction, pausableWatch } from '@vueuse/shared';
import { ref, unref } from 'vue';
import type { StorageLike } from '@vueuse/core';
// import { useEventListener } from '@vueuse/core';
import { guessSerializerType } from './guess';

export interface Serializer<T> {
  read(raw: string): T
  write(value: T): string
}

export interface SerializerAsync<T> {
  read(raw: string): Awaitable<T>
  write(value: T): Awaitable<string>
}

export const StorageSerializers: Record<'boolean' | 'object' | 'number' | 'any' | 'string' | 'map' | 'set' | 'date', Serializer<any>> = {
  boolean: {
    read: (v: any) => v === 'true',
    write: (v: any) => String(v),
  },
  object: {
    read: (v: any) => JSON.parse(v),
    write: (v: any) => JSON.stringify(v),
  },
  number: {
    read: (v: any) => Number.parseFloat(v),
    write: (v: any) => String(v),
  },
  any: {
    read: (v: any) => v,
    write: (v: any) => String(v),
  },
  string: {
    read: (v: any) => v,
    write: (v: any) => String(v),
  },
  map: {
    read: (v: any) => new Map(JSON.parse(v)),
    write: (v: any) => JSON.stringify(Array.from((v as Map<any, any>).entries())),
  },
  set: {
    read: (v: any) => new Set(JSON.parse(v)),
    write: (v: any) => JSON.stringify(Array.from(v as Set<any>)),
  },
  date: {
    read: (v: any) => new Date(v),
    write: (v: any) => v.toISOString(),
  },
};

export interface UseStorageOptions<T> extends ConfigurableEventFilter, ConfigurableFlush {
  /**
   * Watch for deep changes
   *
   * @default true
   */
  deep?: boolean

  /**
   * Listen to storage changes, useful for multiple tabs application
   *
   * @default true
   */
  listenToStorageChanges?: boolean

  /**
   * Write the default value to the storage when it does not exist
   *
   * @default true
   */
  writeDefaults?: boolean

  /**
   * Merge the default value with the value read from the storage.
   *
   * When setting it to true, it will perform a **shallow merge** for objects.
   * You can pass a function to perform custom merge (e.g. deep merge), for example:
   *
   * @default false
   */
  mergeDefaults?: boolean | ((storageValue: T, defaults: T) => T)

  /**
   * Custom data serialization
   */
  serializer?: Serializer<T>

  /**
   * On error callback
   *
   * Default log error to `console.error`
   */
  onError?: (error: unknown) => void

  /**
   * Use shallow ref as reference
   *
   * @default false
   */
  shallow?: boolean
}

export function useStorage(key: string, defaults: MaybeComputedRef<string>,
  storage?: StorageLike, options?: UseStorageOptions<string>): RemovableRef<string>;
export function useStorage(key: string, defaults: MaybeComputedRef<boolean>,
  storage?: StorageLike, options?: UseStorageOptions<boolean>): RemovableRef<boolean>;
export function useStorage(key: string, defaults: MaybeComputedRef<number>,
  storage?: StorageLike, options?: UseStorageOptions<number>): RemovableRef<number>;
export function useStorage<T>(key: string, defaults: MaybeComputedRef<T>,
  storage?: StorageLike, options?: UseStorageOptions<T>): RemovableRef<T>;

/**
 * Reactive LocalStorage/SessionStorage.
 *
 * @see https://vueuse.org/useStorage
 */
export function useStorage<T extends(
  string | number | boolean | object | null)>(
  key: string,
  defaults: MaybeComputedRef<T>,
  storage: StorageLike | undefined,
  options: UseStorageOptions<T> = {},
): RemovableRef<T> {
  const {
    // listenToStorageChanges = true,
    writeDefaults = true,
    mergeDefaults = false,
    /**
     * TODO 23年7月18日注:此处有异步问题
     * 验证发现flush为pre的情况下回调函数是异步执行的，如果把flush改成sync可实现同步回调
     * 当前实现里回调异步执行对业务代码暂无影响，不过是个隐藏的坑，后续要决策一下看是否把flush改成sync
     * 参考文档：https://www.vueusejs.com/guide/config.html
    */
    flush = 'pre',
    deep = true,
    eventFilter,
    onError = (e) => {
      console.error(e);
    },
  } = options;

  const data = ref(defaults) as unknown as  RemovableRef<T>;
  if (!storage) return data;

  const rawInit = unref(defaults as T);
  const type = guessSerializerType<T>(rawInit);
  const serializer = options.serializer ?? StorageSerializers[type];

  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(
    data,
    () => write(data.value),
    { flush, deep, eventFilter },
  );
  // if (window && listenToStorageChanges) useEventListener(window, 'storage', update);

  update();
  return data;

  function write(v: unknown) {
    try {
      if (v === null) storage!.removeItem(key);
      else storage!.setItem(key, serializer.write(v));
    } catch (e) {
      onError(e);
    }
  }

  function read(event?: StorageEvent) {
    pauseWatch();
    try {
      const rawValue = event
        ? event.newValue
        : storage!.getItem(key);

      if (rawValue === null) {
        if (writeDefaults && rawInit !== null) storage!.setItem(key, serializer.write(rawInit));
        return rawInit;
      }
      if (!event && mergeDefaults) {
        const value = serializer.read(rawValue);
        if (isFunction(mergeDefaults)) return mergeDefaults(value, rawInit);
        if (type === 'object' && !Array.isArray(value)) return { ...rawInit as any, ...value };
        return value;
      }
      if (typeof rawValue !== 'string') {
        return rawValue;
      }

      return serializer.read(rawValue);
    } catch (e) {
      onError(e);
    } finally {
      resumeWatch();
    }
  }

  function update(event?: StorageEvent) {
    if (event && event.storageArea !== storage) return;

    if (event && event.key === null) {
      data.value = rawInit;
      return;
    }

    if (event && event.key !== key) return;

    data.value = read(event);
  }
}
