import { QryDpMsgReq } from '../types';

/**
 * 检查uin是否为空或者不符合钱包uin格式
 */
export const isValidUinFormat = (uin = '') => {
  const reg = new RegExp('^[a-zA-Z0-9_-]{28}$');
  const result = reg.exec(uin);
  return !!result;
};

// 用于自测投放计划的字段
export type DpField = 'dpId' | 'pageId' | 'positionId';
// queryDpMessage 查询入参
export type QryParams = Partial<Pick<QryDpMsgReq, 'appid'>> & Omit<QryDpMsgReq, 'appid'|'dpChannel' | 'uin'>;

/**
 * 检查是否需要查询自测计划（根据dpId查投放计划）
 * @param urlParams - { dpId, pageId, positionId } 来自url参数的字段
 * @param qryParams queryDpMessageByPosIdByPosOrDpId查询入参
 */
export const shouldQueryDpMessageByDpId = (
  { dpId, pageId, positionId }: Record<DpField, string>,
  qryParams: QryParams,
) => {
  // 三个url字段都不为空
  if (!dpId || !pageId || !positionId) {
    return false;
  }
  // url字段pageId和positionId在qryParams中
  const { dpPage: paramsPageId, dpPositions: paramsPositionIds } = qryParams;
  const isPageIdValid = paramsPageId === pageId;
  const isPositionIdValid = paramsPositionIds?.includes(positionId);
  return isPageIdValid && isPositionIdValid;
};
