/**
 * 西联相关模块的cgi掩码配置
*/

import { mask } from '@tencent/ppd-uni-common';
import { maskAllHandle } from '../base/index';

type CgiMaskConfig = mask.CgiMaskConfig;

/**
 * 接口掩码配置，此配置格式与后台掩码配置对齐
 * 注：后台使用的是preset来作掩码规则，当前前端还未实现preset逻辑，使用maskHandle来代替
*/
export const wuCgiMaskConfig: CgiMaskConfig['mask_rules'] = {

  '/remitgc/we_remit_gc_homepage_init.fcgi': {
    response: {
      qlskey: maskAllHandle,
    },
  },

  '/remitgc/we_remit_gc_contract_req.fcgi': {
    response: {
      qlskey: maskAllHandle,
    },
  },

  '/remitgc/we_remit_gc_tranlist_qry.fcgi': {
    response: {
      qlskey: maskAllHandle,
      'list_array[].remitter_name': maskAllHandle,
      'list_array[].exchange_ccy': maskAllHandle,
      'list_array[].exchange_amt': maskAllHandle,
      'list_array[].tran_amt': maskAllHandle,
      'list_array[].exchange_rate': maskAllHandle,
      'list_array[].tran_ccy': maskAllHandle,
      'list_array[].remit_country': maskAllHandle,
    },
  },

  '/remitgc/we_remit_gc_trandtl_qry.fcgi': {
    response: {
      remitter_name: maskAllHandle,
      exchange_ccy: maskAllHandle,
      exchange_amt: maskAllHandle,
      tran_amt: maskAllHandle,
      exchange_rate: maskAllHandle,
      tran_ccy: maskAllHandle,
      remit_country: maskAllHandle,
      ref_exchange_rate: maskAllHandle,
      ref_tran_amt: maskAllHandle,
      branch_name: maskAllHandle,
      collect_code: maskAllHandle,
    },
  },

  '/remitgc/we_remit_gc_remit_qry.fcgi': {
    request: {
      collect_code: maskAllHandle,
      remit_country: maskAllHandle,
      exchange_ccy: maskAllHandle,
      exchange_amt: maskAllHandle,
    },
    response: {
      remitter_name: maskAllHandle,
      exchange_rate: maskAllHandle,
      exchange_amt: maskAllHandle,
      tran_ccy: maskAllHandle,
      remit_country: maskAllHandle,
      ref_exchange_rate: maskAllHandle,
      ref_tran_amt: maskAllHandle,
    },
  },

  '/remitgc/we_remit_gc_remit_req.fcgi': {
    request: {
      relationship: maskAllHandle,
      purpose_code: maskAllHandle,
      tran_type: maskAllHandle,
    },
    response: {
      bank_name: maskAllHandle,
    },
  },

  '/remitgc/we_remit_gc_kycinfo_req.fcgi': {
    request: {
      name_en: maskAllHandle,
      name_py_list_aes: maskAllHandle,
      forename_en: maskAllHandle,
      lastname_en: maskAllHandle,
      id_effdate: maskAllHandle,
      id_expdate: maskAllHandle,
      issue_org: maskAllHandle,
      address: maskAllHandle,
      address_en: maskAllHandle,
      mobile: maskAllHandle,
      occupation: maskAllHandle,
      country: maskAllHandle,
      birth_country: maskAllHandle,
      province: maskAllHandle,
      province_en: maskAllHandle,
      province_code: maskAllHandle,
      city: maskAllHandle,
      city_en: maskAllHandle,
      city_code: maskAllHandle,
      district: maskAllHandle,
      district_en: maskAllHandle,
      district_code: maskAllHandle,
      post_code: maskAllHandle,
      ethnic_code: maskAllHandle,
    },
    response: {
      qlskey: maskAllHandle,
    },
  },
};
