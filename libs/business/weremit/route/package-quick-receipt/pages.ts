import { PackageName, getPath } from './../../common/index';
/**
 * 页面列表
 */
enum Page{

  /**
   * 原生小鹅快收已签署的协议页面
   */
  SignedContract = '/pages/protocol/signed-contracts/index',
  /**
   * 原生小鹅渠道协议页面
  */
  BranchProtocol = '/pages/protocol/receipt',
  /**
   * 原生小程序主包
   */
  /**
   * 原生小鹅快收事后调单列表
   */
  RetrievalList = '/pages/retrieval/retrieval-list/index',

  /**
   * 原生小鹅快收事后调单详情页
   */
  RetrievalDetail = '/pages/retrieval/retrieval-result/index',

  /**
   * 原生小鹅快收事后调单上传材料页
   */
  RetrievalUpload = '/pages/retrieval/upload-materials/index',

  /**
   * 原生小鹅快收收集九要素页面
  */
  CollectInfo= '/pages/collect-info/collect-info',

  /**
   * 原生小鹅绑卡页
  */
  BankCard = '/pages/bank-card/bank-card',

  /**
   * 原生小鹅零钱收款限制范围说明页
  */
  WechatExchangeExplain='/pages/wechat-exchange-explain/index',

  /**
   * 订单列表页
   */
  RecordList = '/pages/records/record-list',

  /**
   * 订单分享页
  */
  RecordShare = '/pages/records/record-share',

  /**
   * 原生事中调单页
   */
  UploadMaterials = '/pages/materials/materials',
  /**
   * 原生事中调单页
   */
  PreviewSign = '/pages/preview-sign/preview-sign',

  /**
   * 原生小鹅错误提示页面
  */
  ErrPage = '/pages/err-page/index',

  /**
   * 原生小鹅互联结果页
  */
  MessageResult = '/pages/receipt-result/message-result',

  /**
   * 原生小鹅互联入口页（金额引导页）
  */
  ReceiveEntry = '/pages/receive-entry/receive-entry',

};

export const packageName = PackageName.Gpc;
export const PAGE = new Proxy(Page, {
  get(obj, prop) {
    return getPath(packageName, obj[prop]);
  },
});
