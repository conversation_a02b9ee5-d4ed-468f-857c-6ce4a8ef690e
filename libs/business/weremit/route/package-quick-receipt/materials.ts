/**
 * 原小鹅分包 - 收集九要素页面
*/
import { PAGE } from './pages';
import { NavigateType, navigate, objToQueryType } from '../route';
import { Entrance } from './type';

// 证件类型
enum CreType {
  ID_CARD = 1, // 身份证
  HK_MACAO_PASS_CARD = 5, // 港澳居民往来内地通行证（回乡证）
}

export interface ToMaterialsParams{
  entrance: Entrance;
  listId: string;
  remitterName: string;
  name: string;
  idNo: string;
  relationship?: string;
  materialType: number;
  auditTimes: number;
  step?: number; // 当前步骤
  creType: CreType;
  ageGap?: number;
}
export interface PreviewSignParams{
  relationship?: string;
  name: string;
  id_no: string;
  remit_name?: string; // 当前步骤
  creType: CreType;
}

/**
 * 上传事后调单证明材料页入口
*/
export enum RetrievalEntrance{
  sms = '短信',
  gzhMessage = '公众号模板消息',
  recordDetail = '订单详情',
  retrievalList = '调单列表',
  retrievalNotice = '小黄条',
  retrievalResult = '调单状态页',
};

/**
 * 事后调单上传材料页入参
*/
export interface RetrievalUploadParams{
  entrance: RetrievalEntrance;
  auditListid: string
}

/**
 * 小鹅快收事后调单详情页入参
*/
export interface RetrievalDetailParams{
  entrance: RetrievalEntrance;
  auditListid: string
}

/**
 * 跳转补充材料/签署承诺函页
 */
export const navigateToPreviewSign = (
  params: PreviewSignParams,
  navigateType: NavigateType = NavigateType.navigateTo,
) => {
  navigate({
    url: `${PAGE.PreviewSign}?${objToQueryType(params as unknown as Record<string, string>, false)}`,
  }, navigateType, true);
};

/**
 * 跳转事后调单上传材料页
*/
export const navigateToRetrievalUpload = (
  params: RetrievalUploadParams,
  navigateType: NavigateType = NavigateType.navigateTo,
) => {
  navigate({
    url: `${PAGE.RetrievalUpload}?${objToQueryType(params as unknown as Record<string, string>, false)}`,
  }, navigateType, true);
};

/**
 * 跳转小鹅快收事后调单详情页
*/
export const navigateToRetrievalDetail = (
  params: RetrievalDetailParams,
  navigateType: NavigateType = NavigateType.navigateTo,
) => {
  navigate({
    url: `${PAGE.RetrievalDetail}?${objToQueryType(params as unknown as Record<string, string>, false)}`,
  }, navigateType, true);
};
