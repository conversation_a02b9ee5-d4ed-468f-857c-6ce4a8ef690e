
import { EnterType } from './element';
import { NavigateType, navigate } from '../route';
import { PAGE } from './pages';

/**
 * 首页类型
 */
export type SignContractParam =  {
  /**
   * 协议列表，用|分割，如1001|1002
   */
  branchType: string,
  /**
   * 是否有单，有单传1
   */
  hasOrder?: string,
  /**
   * 来源类型
   */
  type: EnterType,
  /**
   * 开始时间
   */
  startTime?: string,
  /**
   * 跟单进入时，带入listId
   */
  listId?: string,
};
/**
 * 跳转到签署协议页
 */
export const navigateToSignContract = (
  params: SignContractParam,
  navigateType: NavigateType = NavigateType.navigateTo,
) => {
  navigate({
    url: PAGE.SignContract,
    params,
  }, navigateType, true);
};
/**
 * 跳转到已签署协议
 */
export const navigateToSignedContract = (navigateType: NavigateType = NavigateType.navigateTo) => {
  navigate({
    url: PAGE.SignedContract,
  }, navigateType, true);
};
/**
 * 跳转到协议变更记录
 */
export const navigateToContractChangeRecord = (
  params: {
    signedContracts: string[]
  },
  navigateType: NavigateType = NavigateType.navigateTo,
) => {
  navigate({
    url: `${PAGE.ContractChangeRecord}?signedContracts=${params.signedContracts.join('_')}`,
  }, navigateType, true);
};
