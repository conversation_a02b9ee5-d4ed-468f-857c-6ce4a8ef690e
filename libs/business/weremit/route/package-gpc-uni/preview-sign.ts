
import { NavigateType, navigate, objToQueryType } from '../route';
import { PAGE } from './pages';

// 证件类型
enum CreType {
  ID_CARD = 1, // 身份证
  HK_MACAO_PASS_CARD = 5, // 港澳居民往来内地通行证（回乡证）
}
export enum PreviewType {
  /**
   * 关系承诺函
   */
  RelationCommitment = 'RelationCommitment',
  /**
   * 个人声明
   */
  SelfStatement = 'SelfStatement',
}


export type PreviewSignPagePrams = {
  creType: CreType;
  remit_name: string;
  name: string;
  id_no: string;
  relationship: string;
  previewType: PreviewType;
};

/**
 * 跳转九要素页面
 */
export const navigateToPreviewSignPage = (
  params: PreviewSignPagePrams,
  navigateType: NavigateType = NavigateType.navigateTo,
) => {
  navigate({
    url: `${PAGE.PreviewSign}?${objToQueryType(params as unknown as Record<string, string>)}`,
  }, navigateType, true);
};
