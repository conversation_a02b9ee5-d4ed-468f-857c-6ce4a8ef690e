import { Entity } from '@tencent/ppd-domain-framework';
import { EditFlag, PaymentDetailRes, IsBetter, NeedRealName, PayShareFlag, ScannerIdentity, PayType, BigPayFlag, BigPayLeftTimeType } from '../repository/payment-order.repository.interface';
import { fenToRMB, fenToYuan, formatRate, compareAmt, FormatType } from '@tencent/tuition-common';
// 手续费信息
export interface FeeInfo{
  // 手续费（后端返回值）
  rawFee: string;
  // 原始手续费（后端返回值）
  rawOriginalFee: string;
  // 手续费，带币种，如190CNY
  fee: string;
  // 原始手续费，带币种，如370CNY
  originFee: string;
  // 是否有手续费优惠
  isFeeBetter: boolean;
}
export interface PayInfo{
  // 支付金额 - 人民币
  payAmt: string;
  // 汇率相关
  rate: {
    // 展示
    display: string;
    // 是否优于银行汇率
    isBetter: boolean;
  }
  // 缴费金额 - 外币（带符号）
  userTuitionAmt: string;
  // 手续费 - 人民币
  feeInfo: FeeInfo;
  // 订单号
  listId: string;
  // 机构订单号
  orgListid: string;
  // 学校英文名
  schoolNameEn: string;
}

/**
 * 获取默认格式化后的支付单详情返回
 * @returns
 */
export function getDefaultPayInfo(): PayInfo {
  const res = {
    payAmt: '',
    rate: {
      display: '',
      isBetter: false,
    },
    userTuitionAmt: '',
    feeInfo: {
      rawFee: '',
      rawOriginalFee: '',
      fee: '',
      originFee: '',
      isFeeBetter: false,
    },
    listId: '',
    orgListid: '',
    schoolNameEn: '',
  };
  return res;
};

/**
 * 获取默认支付单详情返回
 * @returns
 */
export function getDefaultGetPaymentDetailRes(): PaymentDetailRes {
  const res = {
    listid: '',
    org_listid: '',
    currency_symbol: '',
    list_state: '',
    is_user_list: ScannerIdentity.SELF,
    list_user_name: '',
    channel_id: '',
    kyc_flag: NeedRealName.NOT_NEED,
    pay_share_flag: PayShareFlag.SELF_PAY,
    user_tuition_amt: '',
    tuition_amt: '',
    currency: '',
    currency_point_num: '',
    total_fee: '',
    pay_amt: '',
    rate: '',
    sign: '',
    pay_share_switch: '',
    modify_time: '',
    edit_flag: EditFlag.ABLE,
    is_better: IsBetter.NO,
    file_list: [],
    default_pay_type: PayType.WECHAT,
    big_pay_flag: BigPayFlag.ENABLE,
    pay_type_tips: '',
    big_account_name: '',
    big_account_id: '',
    big_account_bank: '',
    big_pay_left_time_type: BigPayLeftTimeType.COUNT_DOWN, // 假设你有一个默认的 BigPayLeftTimeType
    big_pay_left_time: '',
    big_pay_end_time: '',
    big_pay_not_in_use_left_time: '',
    big_pay_user_account: '',
    big_pay_user_account_type: '',
    big_pay_user_account_bank_logo_url: '',
    big_pay_user_cardno_tail: '',
    big_pay_user_card_bankname: '',
    student_name: '',
  };
  return res;
};

/**
 * 支付单实体
 */
export class PaymentOrderEntity extends Entity {
  // 订单详情
  detail: PaymentDetailRes;
  constructor(detail?: PaymentDetailRes) {
    super();
    this.detail = detail ?? getDefaultGetPaymentDetailRes();
  }
  // 获取支付订单详情
  getPaymentDetail() {
    return this.detail;
  }
  // 更新支付状态
  updateStatus(detail: PaymentDetailRes) {
    this.detail = detail;
  }
  // 判断汇率是否优于银行
  checkIsRateBetter(isBetter: string): boolean {
    if (isBetter === IsBetter.YES) return true;
    return false;
  }
  // 获取支付信息
  getPayInfo(): PayInfo {
    // 本人支付金额为user_tuition_amt， 代付人支付金额为tuition_amt
    const tuitionAmt = this.detail?.user_tuition_amt || this.detail?.tuition_amt;
    return {
      // 支付金额 - 人民币
      payAmt: fenToRMB(this.detail?.pay_amt),
      // 汇率
      rate: {
        display: `1 ${this.detail?.currency} = ${formatRate(this.detail?.rate)} CNY`,
        isBetter: this.checkIsRateBetter(this.detail?.is_better),
      },
      // 缴费金额 - 外币（带符号），如$3,000.00
      userTuitionAmt: `${fenToYuan({
        amount: tuitionAmt,
        pointNum: +this.detail?.currency_point_num,
      })} ${this.detail?.currency}`,
      // 手续费 - 人民币
      feeInfo: {
        rawFee: this.detail.total_fee,
        // 如果后端没返回original fee，则认为original fee就是fee
        rawOriginalFee: this.detail.original_total_fee ?? this.detail.total_fee,
        fee: fenToRMB(this.detail?.total_fee, FormatType.CURRENCY),
        originFee: fenToRMB(this.detail.original_total_fee ?? this.detail.total_fee, FormatType.CURRENCY),
        isFeeBetter: +(this.detail?.total_fee || '') < +(this.detail.original_total_fee || ''),
      },
      // 订单号
      listId: this.detail?.listid || '',
      // 机构订单号
      orgListid: this.detail?.org_listid || '',
      // 学校英文名
      schoolNameEn: this.detail?.school_name_en || '',
    };
  }
  // 是否超过指定限额，单位为分（最小单位）。用于推荐支付方式
  getIsOverThreshold(threshold: number) {
    return compareAmt(+this.detail?.pay_amt, threshold) > 0;
  }
}


