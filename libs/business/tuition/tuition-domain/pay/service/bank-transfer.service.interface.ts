import { BankTransferResult, BankTransferStatementReq, BankTransferStatementRes, GetBankTransferStatementParams } from '../repository/bank-transfer.repository.interface';

export interface IBankTransferService {
  /**
   * 发起银行转账
   */
  initiateBankTransfer(): string;
  /**
   * 创建银行转账账单
   */
  createBankTransferStatement(params: BankTransferStatementReq): Promise<unknown>;
  /**
   * 获取银行转账账单
   */
  getBankTransferStatement(param: GetBankTransferStatementParams): Promise<BankTransferStatementRes>;
  /**
   * 获取银行转账结果
   */
  getBankTransferResult(listid: string): Promise<BankTransferResult>;
}
