import { ActivityCenterConfig } from '../repository/activity-center.repository.interface';
import { Entity } from '@tencent/ppd-domain-framework';

export interface IActivityCenterEntity {
  /**
   * 活动数据
   */
  data: ActivityCenterConfig  | null;
}

export class ActivityCenterEntity extends Entity implements IActivityCenterEntity {
  data: ActivityCenterConfig | null = null;

  getData<Data>() {
    return this.data as ActivityCenterConfig<Data>;
  }

  setData(data: ActivityCenterConfig) {
    if (data) {
      this.data = data;
    }
  }
}
