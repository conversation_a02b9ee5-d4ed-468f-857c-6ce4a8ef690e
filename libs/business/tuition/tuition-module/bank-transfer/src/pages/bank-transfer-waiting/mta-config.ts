import { getRule } from '../../bi-rules';

const failType = getRule(process.env.BI_PROJECT_NAME, 'failType');

const bankType = getRule(process.env.BI_PROJECT_NAME, 'bankType');

export default [{
  key: 'checktransfer_status_button',
  desc: '底部查看转账结果按钮',
  params: {
    img: '',
    productor: '',
  },
  statList: [{
    subgroupCode: 'button',
    subgroupDesc: '点击查看结果',
  }],
}, {
  key: 'transferresult_button',
  desc: '转账结果查询页，点击确认按钮',
  params: {
    img: '',
    productor: '',
  },
  statList: [{
    subgroupCode: 'button',
    subgroupDesc: '点击确认按钮',
    rule: [
      failType,
    ],
  }],
},
{
  key: 'bank_transfer_info',
  desc: '银行转账信息',
  params: {
    img: '',
    productor: '',
  },
  statList: [{
    subgroupCode: 'brow',
    subgroupDesc: '曝光',
    rule: [
      bankType,
    ],
  }],
},
];
