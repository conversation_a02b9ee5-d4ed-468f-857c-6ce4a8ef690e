<template>
  <view class="flex flex-row items-center mb-25rpx">
    <view class="text5-reg color-text-title">
      选择支付方式
    </view>
    <view class="caption1-reg color-text-disable">
      （支付金额大于3万时, 推荐使用银行转账）
    </view>
  </view>
  <p-line
    class="color-line-divider"
  />
  <view
    v-if="payTypeList"
  >
    <view
      v-for="(item, index) in payTypeList"
      :key="item.title"
    >
      <view @click="onSelect(index)">
        <payTypeItem
          :detail="item"
          :is-selected="index===curIndex"
          :is-recommend="index===0"
        />
        <p-line
          v-if="index!==(payTypeList.length-1)"
          class="color-line-divider"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IPayTypeItem } from '../../composition/use-pay-select';
import payTypeItem from '../pay-type-item/index.vue';
const emit = defineEmits<{
  (e: 'select', index: number)
}>();

type Props = {
  payTypeList: IPayTypeItem[] | null;
  curIndex: number;
};

withDefaults(defineProps<Props>(), {
  payTypeList: null,
  curIndex: 0,
});

const onSelect = (e: number) => {
  emit('select', e);
};
</script>

<style scoped lang="scss">

</style>
