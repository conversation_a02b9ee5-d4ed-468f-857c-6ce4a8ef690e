<!-- 基础支付方式选择组件 -->
<template>
  <view class="flex items-start w-686rpx justify-start pt-36rpx pb-36rpx">
    <!-- icon -->
    <p-image
      width="40rpx"
      height="40rpx"
      :src="detail?.icon"
      class="mr-24rpx w-40rpx h-36rpx mt-6rpx"
    />
    <!-- 文案 -->
    <view class="flex flex-col">
      <view class="flex mb-8rpx">
        <text class="text4-reg color-text-title mr-8rpx">
          {{ detail?.title }}
        </text>
        <p-tag
          v-if="isRecommend"
          class="self-center"
          text="推荐"
          type="primary"
        />
      </view>
      <view class="caption1-reg">
        <text class="color-text-primary">
          {{ detail?.desc }}
        </text>
        <text
          v-if="detail?.urlLink?.text"
          class="caption1-reg color-state-info ml-16rpx"
          @click="onLinkClick"
        >
          {{ detail?.urlLink?.text }}
        </text>
      </view>
    </view>
    <!-- 打勾 -->
    <view
      v-if="isSelected"
      class="self-center ml-auto"
    >
      <p-icon
        size="40"
        name="success"
        class="color-primary-normal"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { useExternal } from '../../use-app.store';
import { PayType } from '@tencent/tuition-domain/pay/repository/payment-order.repository.interface';
import { biClick } from '@tencent/fit-bi-sdk';
import {
  NavigateType,
} from '@tencent/tuition-common';
export interface IPayTypeItem {
  payType: PayType,
  title: string,
  icon: string,
  desc: string,
  urlLink?: {
    text: string,
    link: string
  },
}

type Props = {
  detail: IPayTypeItem|null;
  isSelected: boolean;  // 是否选中
  isRecommend: boolean; // 是否推荐
};

const props = withDefaults(defineProps<Props>(), {
  detail: null,
  isSelected: false,
  isRecommend: false,
});

const onLinkClick = () => {
  const { navigateToWebView } = useExternal();
  navigateToWebView({
    url: props.detail?.urlLink?.link || '',
  }, NavigateType.navigateTo);
  biClick('cashier_wxpay.amtlimitation');
};

</script>

<style scoped lang="scss">

</style>
