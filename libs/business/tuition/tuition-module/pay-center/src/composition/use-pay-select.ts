import { Ref, computed, ref } from 'vue';
import { PayType, PaymentDetailRes } from '@tencent/tuition-domain/pay/repository/payment-order.repository.interface';
import { useExternal } from '..';
import { JumpType } from '../routes/pay-type-choose';
import { externalToRefs } from '@tencent/tuition-common';

export interface IPayTypeItem {
  payType: PayType,
  title: string,
  icon: string,
  desc: string,
  urlLink?: {
    text: string,
    link: string
  },
}

export interface IPayInfoItem{
  title: string,
  desc: string,
  isBold?: boolean,
  isAmt?: boolean,
}

/**
 * 获取支付方式
 */
const getPayItems = () => {
  const { payIcons } = useExternal();
  // 微信支付item
  const wechatPayItem: IPayTypeItem = {
    payType: PayType.WECHAT,
    title: '微信支付',
    icon: payIcons.wechatPayIcon,
    desc: '支持储蓄卡/零钱/零钱通支付',
    urlLink: {
      text: '查看支付限额',
      link: 'https://kf.qq.com/touch/sappfaq/151210NZzmuY151210ZRj2y2.html',
    },
  };

  // 大额转账item
  const bigPayItem: IPayTypeItem = {
    payType: PayType.BIGPAY,
    title: '银行转账',
    icon: payIcons.bankTransferIcon,
    desc: '单笔额度更高，支持手机银行/网银转账',
  };

  // 亲友代付item
  const sharePayItem: IPayTypeItem = {
    payType: PayType.SHARE_PAY,
    title: '邀请家长付款',
    icon: payIcons?.sharePayIcon || '',
    desc: '分享订单，支付金额以实际汇率为准',
  };
  return {
    wechatPayItem,
    bigPayItem,
    sharePayItem,
  };
};

// 支付选择相关
export const usePaySelect = (detail: Ref<PaymentDetailRes>, isOverThreshold: Ref<boolean>, jumpType: Ref<JumpType>) => {
  const { wechatPayItem, bigPayItem, sharePayItem } = getPayItems();
  const { payTypes } = externalToRefs(useExternal());
  const curIndex = ref(0);
  /**
   * 支付方式列表
   * 支付方式包含微信支付、大额转账、家长代付，具体是否展示该项取决于payTypes是否声明该项
   * 支付方式排序遵循：
   * 1、当待支付人民币大于大额转账推荐门槛时，优先【大额转账】，其次【微信支付】
   * 2、反之，优先【微信支付】，其次【大额转账】
   * 3、【家长代付】总是放在最后一位
   */
  const payTypeList = computed(() => {
    const templateOrder = [PayType.WECHAT, PayType.BIGPAY, PayType.SHARE_PAY];
    const payList: IPayTypeItem[] = [];

    // 根据isOverThreshold调整前两位的顺序
    if (isOverThreshold.value) {
      [templateOrder[0], templateOrder[1]] = [templateOrder[1], templateOrder[0]];
    }

    // 根据payTypes和jumpType生成最终的支付顺序
    const finalOrder = templateOrder.filter((payType) => {
      if (payType === PayType.SHARE_PAY) {
        // 对于代付，需要额外判断当前支付人是不是本人
        return payTypes.value.includes(payType) && jumpType.value === JumpType.SELF;
      }
      return payTypes.value.includes(payType);
    });

    // 根据最终的支付顺序生成payList
    finalOrder.forEach((payType) => {
      switch (payType) {
        case PayType.WECHAT:
          payList.push(wechatPayItem);
          break;
        case PayType.BIGPAY:
          payList.push(bigPayItem);
          break;
        case PayType.SHARE_PAY:
          payList.push(sharePayItem);
          break;
      }
    });

    return payList;
  });

  // 当前选中的支付类型
  const curPayType = computed(() => payTypeList.value[curIndex.value].payType);
  // 当前是否选择为大额转账
  const isBigPaySelected = computed(() => payTypeList.value[curIndex.value].payType === PayType.BIGPAY);
  // 大额转账支付提示
  const bigPayTips = computed(() => detail.value.pay_type_tips);
  return {
    // 当前选中下标
    curIndex,
    // 当前选中支付类型
    curPayType,
    // 支付列表
    payTypeList,
    // 大额是否被选中
    isBigPaySelected,
    // 大额提醒
    bigPayTips,
  };
};
