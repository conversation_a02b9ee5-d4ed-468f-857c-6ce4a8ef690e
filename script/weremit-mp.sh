 
# 切换node版本
export PATH=/opt/node-v16.13.1-linux-x64/bin:$PATH 
echo 'node版本：'
node -v
npm -v
# 变量
WORKSPACE=$(pwd)
echo $WORKSPACE
TARGET_DIR=${WORKSPACE}/bin
ORIGINAL_PATH=$PATH

# 临时目录安装pnpm
mkdir -p $TARGET_DIR \
&& cd $TARGET_DIR \
&& npm init -y \
&& npm i --global-style pnpm@6.32.20 \
&& npm config set registry https://mirrors.tencent.com/npm/ \
&& tar -czf node_modules.tgz node_modules \
&& export PATH=$(pwd)/node_modules/.bin:$ORIGINAL_PATH

# 设置npm源为腾讯源
pnpm config set registry https://mirrors.tencent.com/npm/ 
pnpm install

# 进入项目目录 && 编译 && 产物生成
cd ${WORKSPACE}/apps/we_remit_mp_main \
&& npm run bootstrap && npm run build:ci \
&& mv ${WORKSPACE}/apps/we_remit_mp_main/dist ${WORKSPACE}/dist

# 留学模块项目路径
path_array=("tuition-module/bank-transfer" "tuition-module/guide-subscribe" "tuition-module/page-base" "tuition-module/pay-center" "tuition-module/real-name")

# 留学模块化开发 创建留学模块内告警策略
# 定义模块路径 新增引用模块需在此处新增
cd $WORKSPACE \
&& cd .. \
&& pnpm install -g @tencent/ppd-alarm-cli \
&& for path in "${path_array[@]}"; do
    echo "运行 ppd-alarm 命令，路径：${path}..."
    ppd-alarm customException --path "libs/${path}" --package-json-path "apps/tuition" --alarm-json-path "apps/tuition" --th=3
done

# 留学模块化开发 创建组件埋点上报
# 切换到 WORKSPACE 目录并执行 for 循环
# pnpm install -g @tencent/fit-bi-cli@0.3.7
# 安装 cross-env
# pnpm install -g cross-env
# cd $WORKSPACE && cd .. && for path in "${path_array[@]}"; do
#     echo "切换到目录：libs/${path}..."
#     cd "libs/${path}"
#     echo "在目录：libs/${path} 下执行 bi scan 命令..."
#     cross-env BI_PROJECT_NAME=tuition bi scan -c ../../../apps/tuition/mta-config-global.ts
#     cd ../../..
# done