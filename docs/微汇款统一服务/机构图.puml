@startuml
component 业务项目A {
  component 业务代码A
  component 业务项目A服务适配层
}

component 业务项目B {
  component 业务代码B
  component 业务项目B服务适配层
}


component "微汇款公共服务" {

  () 获取服务方法

  component 服务中心 {
    component 服务map
    component 服务注册方法
  }

  component 加解密服务 {
  
  }

  component 掩码服务 {
  
  }

  获取服务方法 -down- 服务中心

}

业务项目A服务适配层 -down- 获取服务方法
业务项目B服务适配层 -down- 获取服务方法

服务中心 -down- 加解密服务
服务中心 -down- 掩码服务

业务代码A -down- 业务项目A服务适配层
业务代码B -down- 业务项目B服务适配层

@enduml