<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="activity">
    <div class="activity-bg">
      <img class="activity-bg-img" :key="index" v-for="(item, index) in images" :src="item.url" />
    </div>
    <div class="activity-button-wrap"
      :style="buttonWrapStyle">
      <AwardButton
        :style="{
          'margin-top': buttonTop,
        }"
        class="award-button"
        :disableBgImage="disableBgImage"
        :activeBgImage="activeBgImage"
        :buttonText="buttonText"
        :activityStatus="activityStatus"
        :activityId="activityId"
        :navigateToUrl="navigateToUrl"></AwardButton>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import AwardButton from './components/award-button.vue';
import { ActivityStatus, ButtonText } from './types';

@Component({
  name: 'AwardProm',
  components: {
    AwardButton,
  },
})

export default class AwardProm extends Vue {
  // 活动id
  @Prop() public activityId = '';
  // 活动背景图
  @Prop() public images = [{
    url: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/wlgift/pageBgPartOne11.png',
  },
  {
    url: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/wlgift/pageBgPartTwo11.png',
  },
  ];
  // 禁用状态按钮背景图
  @Prop() public disableBgImage = '';
  // 可点状态按钮背景图
  @Prop() public activeBgImage = '';
  // 按钮文案
  @Prop() public buttonText: ButtonText = {
    [ActivityStatus.NOT_YET]: '活动未开始',
    [ActivityStatus.FINISHED]: '奖品已发完',
    [ActivityStatus.ACCEPTED]: '已领取',
    [ActivityStatus.END]: '活动已结束',
    [ActivityStatus.IN_PROGRESS]: '去完成一笔收款',
    [ActivityStatus.READY_TO_ACCEPT]: '领取10元奖励金',
  };
  // 活动状态
  @Prop() public activityStatus: ActivityStatus = ActivityStatus.NOT_YET;
  // 跳转的小程序页面路径
  @Prop() public navigateToUrl = '';
  // 按钮区域样式
  @Prop() public buttonWrapStyle = {
    height: '90px',
    'background-color': '#a4e1d0',
    'background-image': 'none',
  };
  // 按钮距离按钮区域顶部距离
  @Prop() public buttonTop = '16px';
}
</script>
<style lang="less" scoped>
.activity{
  position: relative;
}
.activity-bg{
  position: relative;
  top: 0;
  left: 0;
}
.activity-bg-img{
  width: 100%;
  display: block;
}
.activity-button-wrap{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  background-size: 100% 100%;
  background-position: top center;
}
</style>
