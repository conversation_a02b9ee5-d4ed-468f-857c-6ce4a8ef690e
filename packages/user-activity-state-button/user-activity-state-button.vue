<template>
  <div
    v-show="showBtn"
    class="w-full h-full relative z-30 safe-area-padding-bottom"
    :style="{
      backgroundColor:props.btnBg.backgroundColor,
      backdropFilter: `blur(${props.btnBg.blur}rpx)`
    }"
  >
    <div
      class="bg-no-repeat btn absolute"
      :style="{
        width:`${btnWidth*2}rpx`,
        height:`${btnHeight*2}rpx`,
        top: `${btnTop*2}rpx`,
        left: `${btnLeft*2}rpx`,
        backgroundImage:`url(${btnBg})`
      }"
      @click="onBtnClick"
    />
  </div>
  <!-- 分享tips -->
  <div
    v-if="showShareTips"
    class="fixed top-0 left-0 right-0 bottom-0 z-1010"
    @click.stop="closeShareTips"
  >
    <div
      class="absolute share-tips rounded-8rpx bg-grey box-border py-32rpx px-40rpx"
    >
      <div class="triangle" />
      点这里分享给好友
    </div>
  </div>
</template>

<script setup lang="ts">
import { ActivityStatus, SourceEnum, QryUserActStateRes } from '../../../api/prom/index';
import { getQryUserActStateSinglePromise } from '../../../domain/prom/index';
import { jumpHooks } from '../../../hooks/jump-hooks/index';
import { CustomException } from '../../../adapters/report';
import { to } from '@tencent/ppd-common/src/utils';
import { onMounted } from 'vue';
import { biBrow, biClick } from '@tencent/fit-bi-sdk';

/**
 * 按钮功能
*/
enum BtnFunc {
  NONE = 0, // 无功能
  JUMP_TO_MP_PAGE = 1,  // 跳转小程序页面
  MP_SHARE = 2  // 小程序分享
}

const props = defineProps<{
  actId: string,  // 活动id
  btnConfigList: {  // 按钮配置列表
    userActivityState: ActivityStatus[],  // 用户状态（什么用户状态下展示此按钮）
    btnFunc: BtnFunc, // 按钮功能
    imageUrl: string,

    // 热区宽高
    width: number,
    height: number,

    // 热区坐标
    x: number,
    y: number,

    // 跳转url
    jumpUrl?: string,
  }[],
  btnBg: {
    backgroundColor: string,
    blur: string,
    bgWidth: number,
    bgHeight: number
  }
}>();

const btnWidth = ref(0);
const btnHeight = ref(0);
const btnTop = ref(0);
const btnLeft = ref(0);
const btnBg = ref('');
const btnFunc = ref(0);
const jumpUrl = ref('');
const showShareTips = ref(false);
const showBtn = ref(false);

let userActState: ActivityStatus;

onMounted(async () => {
  await initUserActState();
  initBtnConfig();
});

const {
  jumpJudgeByEnv,
} = jumpHooks();

/**
 * 查询用户-活动状态
*/
const initUserActState = async () => {
  const singlePromise = getQryUserActStateSinglePromise({
    /**
     * 活动id
     */
    act_id: props.actId,
    /**
     * 调用来源
     */
    source: SourceEnum.PUBLIC_ACCOUNT,
  });
  const [err, res] = await to<QryUserActStateRes>(singlePromise);
  if (err || !res?.state) {
    new CustomException(err || res, 'getUserActState_error', '查询活动状态错误');
    return;
  }
  userActState = res.state;
  showBtn.value = true;
};

const initBtnConfig = () => {
  for (const btnConfig of props.btnConfigList) {
    if (btnConfig.userActivityState.includes(userActState as ActivityStatus)) {
      biBrow('user-act-state-btn.brow', {
        userActState,
      });
      btnBg.value = btnConfig.imageUrl;
      btnWidth.value = btnConfig.width;
      btnHeight.value = btnConfig.height;
      btnTop.value = btnConfig.y;
      btnLeft.value = btnConfig.x;
      btnFunc.value = btnConfig.btnFunc;
      jumpUrl.value = btnConfig.jumpUrl || '';
      return;
    }
  }
};

/**
 * 按钮点击事件
*/
const onBtnClick = () => {
  biClick('user-act-state-btn.click', {
    userActState,
  });
  switch (btnFunc.value) {
    case BtnFunc.NONE:
      break;
    case BtnFunc.JUMP_TO_MP_PAGE:
      jumpToMpPage();
      break;
    case BtnFunc.MP_SHARE:
      showShareTips.value = true;
      break;
  }
};

const closeShareTips = () => {
  biClick('user-act-state-btn-share-tips.close');
  showShareTips.value = false;
};

const jumpToMpPage = () => {
  setTimeout(() => {
    jumpJudgeByEnv({
      /**
       * 小程序跳转路径
       */
      mpPath: jumpUrl.value,
    });
  }, 300);
};
</script>
<style lang="less" scoped>
.btn{
  background-size: 100% auto;
}
.share-tips{
  top:18rpx;
  right:18rpx;
  background-color: #6C6C6C;
  color: white;
  font-weight: 600;
  font-size: 28rpx;
  line-height: 40rpx;
}

.triangle {
  position: absolute;
  top: -18rpx;
  right: 115rpx;

  width: 0;
  height: 0;
  border-top: 10rpx solid transparent;
  border-bottom: 10rpx solid #6C6C6C;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
}
.safe-area-padding-bottom{
  position: absolute;
  bottom:0;
  padding-bottom: env(safe-area-inset-bottom);
}
</style>


