/** 这里编写storybook文档*/

import SeamlessCarouselImage from './seamless-carousel-image.vue';

export default {
  title: '业务组件/SeamlessCarouselImage',
  component: SeamlessCarouselImage,
  parameters: {
    design: {
      type: 'figma',
      url: '',
    },
  },
};

// @ts-ignore
const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { SeamlessCarouselImage },
  template: '<SeamlessCarouselImageInput v-bind="$props"/>',
});

export const 默认 = Template.bind({});
// props参数模拟, 如果数据组合比较多，可以在mock文件中写好再导过来
(默认 as any).args = {
  text: '哈喽',
};

