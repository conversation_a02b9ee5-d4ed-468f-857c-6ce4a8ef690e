<template>
  <div :style="{
    backgroundImage:`url(${props.image})`,
    width:`${props.width}`,
    height:`${props.height}`
  }"/>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  name?: string,
  image?: string,
  width?: number,
  height?: number,
  url?: string,
  orgId?: string,
}>(), {
  name: '',
  image: '',
  url: '',
  orgId: '',
  width: 0,
  height: 0,
});
</script>
<style lang="less" scoped>
</style>


