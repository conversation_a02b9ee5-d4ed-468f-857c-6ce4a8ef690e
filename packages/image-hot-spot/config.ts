import component from './image-hot-spot-ui.vue';

const config = {
  type: 'image-hot-spot',
  maintainer: 'or<PERSON>hu',
  name: 'ImageHotSpot组件',
  desc: 'ImageHotSpot组件',
  icon: '',
  runtime: 'custom',
  component: (element, props) => {
    const data = ref(props);
    createApp(() => h(component, data.value)).mount(element);
    return {
      setUpdateData(newData) {
        data.value = newData;
      },
    };
  },
  dataSets: [

    { key: 'bgWidth', title: '背景图宽度', type: 'number' },
    { key: 'bgHeight', title: '背景图高度', type: 'number' },
    { key: 'bgImageUrl', title: '背景图url', type: 'string' },

    { key: 'hotSpotKey', title: '热区key', type: 'string' },
    { key: 'hotSpotWidth', title: '热区宽度', type: 'number' },
    { key: 'hotSpotHeight', title: '热区高度', type: 'number' },
    { key: 'hotSpotX', title: '热区X', type: 'number' },
    { key: 'hotSpotY', title: '热区Y', type: 'number' },
    { key: 'a', title: '加密串A', type: 'string' },
    { key: 'url', title: '端外跳转链接', type: 'string' },
    { key: 'mpPath', title: '小程序跳转路径', type: 'string' },

  ],
  stateSets: [],
  eventSets: [],
  create() {
    return {
      style: {
        position: 'static',
      },
      data: {
      },
    };
  },
};

export default config;
