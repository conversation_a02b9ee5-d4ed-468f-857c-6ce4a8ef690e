/**
 * 机构定制hooks
*/
import { onLoad } from '@dcloudio/uni-app';
import { getKeyFromHashOrQuery } from '../../../../utils/url';
import { LoggerService, PpdUniDomainToken } from '@tencent/ppd-uni-domain';
import { ref } from 'vue';
import Container from 'typedi';

const loggerService = Container.get<LoggerService>(PpdUniDomainToken.LOGGER_SERVICE_TOKEN);
const { CustomException } = loggerService;

export const orgCustomizationHooks = (orgCustomInfoList?: Array<{
  orgKey: string,
  // 机构定制顶部图片
  orgCustomTopImage: string
  // 机构定制底部图片
  orgCustomBottomImage: string
}>) => {
  const orgCustomBottomImage = ref('');
  const orgCustomTopImage = ref('');
  const orgKey = getKeyFromHashOrQuery('orgKey') || '';
  onLoad(() => {
    if (orgKey) {
      const orgCustomInfo = orgCustomInfoList?.find(item => item.orgKey === orgKey);
      if (orgCustomInfo) {
        orgCustomBottomImage.value = orgCustomInfo.orgCustomBottomImage;
        orgCustomTopImage.value = orgCustomInfo.orgCustomTopImage;
      } else {
        new CustomException(orgKey, 'm2_unknownOrgKey', 'm2页面未知的定制机构key');
      }
    }
  });

  return {
    orgCustomBottomImage,
    orgCustomTopImage,
    orgKey,
  };
};
