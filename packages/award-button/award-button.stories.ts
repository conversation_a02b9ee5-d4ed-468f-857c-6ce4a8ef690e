/** 这里编写storybook文档*/

import AwardButton from './award-button.vue';
import { ActivityStatus } from './types';

export default {
  title: '业务组件/AwardButton',
  component: AwardButton,
  parameters: {
    design: {
      type: 'figma',
      url: '',
    },
  },
};

// @ts-ignore
const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { AwardButton },
  template: '<AwardButton style="font-size: 17px;color: #ffffff;" v-bind="$props"/>',
});

export const 默认 = Template.bind({});

// 这里写props参数的mock数据
(默认 as any).args = {
  disableBgImage: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/wlgift/disableButton.svg',
  // 可点状态按钮背景图
  activeBgImage: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/wlgift/activeButton.svg',
  buttonText: {
    [ActivityStatus.NOT_YET]: '活动未开始',
    [ActivityStatus.FINISHED]: '奖品已发完',
    [ActivityStatus.ACCEPTED]: '已领取',
    [ActivityStatus.END]: '活动已结束',
    [ActivityStatus.IN_PROGRESS]: '去完成一笔收款',
    [ActivityStatus.READY_TO_ACCEPT]: '领取10元奖励金',
  },
  activityStatus: ActivityStatus.NOT_YET,
  activityId: '',
  navigateToUrl: '',
};

