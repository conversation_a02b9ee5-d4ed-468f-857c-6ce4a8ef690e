<template>
  <div style="display: none;"/>
</template>

<script setup lang="ts">
import { biBrow } from '@tencent/fit-bi-sdk';
const props = defineProps<{
  timezoneUrlMapArray: {
    timezone: number,
    url: string
  }[],
  defaultUrl: string
}>();

onMounted(() => {
  const curTimeZone = (new Date().getTimezoneOffset()) / 60 * -1;
  biBrow('timezone.brow', {
    timezone: curTimeZone,
  });

  for (const timezoneUrlMapItem of props.timezoneUrlMapArray) {
    if (+timezoneUrlMapItem.timezone === curTimeZone) {
      if (timezoneUrlMapItem.url) {
        biBrow('timezone.jump', {
          timezone: `${curTimeZone}`,
          url: timezoneUrlMapItem.url,
        });
        locationReplace(timezoneUrlMapItem.url);
      }
      // 如果命中的时区没有配置url，则表示此时区不需要进行跳转，则直接return
      return;
    }
  }

  if (props.defaultUrl) {
    locationReplace(props.defaultUrl);
  }
});

/**
 * 重定向到其他网页
 * ! 加500ms延时的原因：
 * 小程序webview下不加delay的话reload有可能先于replace执行
 * 经验证即使200ms也有可能出现时序问题
 * 故此处加了500ms delay
*/
function locationReplace(url: string) {
  location.replace(url);
  setTimeout(() => {
    location.reload();
  }, 500);
}

</script>
<style lang="less" scoped>

</style>


