<template>
  <div class="invite-container">
    <div
      class="env-bg"
      :class="showType"
      :style="`background-image:url(${envBgImg})`"
    />
    <RetryContainer
      :mode="status"
      inner-style="padding-top:54px"
      :texts="{
        [RetryMode.Loading]: '',
        [RetryMode.Failed]: ''
      }"
    >
      <div
        v-if="showType === 'init'"
        v-stat.brow="'mgm-m1-invite.container'"
        :data-stat="JSON.stringify(reportInfo)"
        class="invite-init-container"
      >
        <p-button
          v-stat.click="'mgm-m1-invite.invite-btn'"
          type="primary"
          class="btn"
          :data-stat="JSON.stringify(reportInfo)"
          :disabled="!btnAbleFlag"
          @click="onShare"
        >
          {{ btnText }}
        </p-button>
        <p-button
          v-stat.click="'mgm-m1-invite.subscribe-btn'"
          type="link"
          class="subscribe mt-26rpx"
          :data-stat="JSON.stringify(reportInfo)"
          @click="onSubscribe"
        >
          订阅活动信息通知
        </p-button>
      </div>
      <div
        v-else
        v-stat.brow="'mgm-m1-invite.container'"
        class="px-47rpx"
        :data-stat="JSON.stringify(reportInfo)"
      >
        <div class="invite-invited-container relative">
          <div class="invite-data mb-56rpx">
            <div
              v-stat.click="'mgm-m1-invite.userNum'"
              class="item flex-1 "
              :data-stat="JSON.stringify(reportInfo)"
              @click="onShowUserList"
            >
              <h2 class="title">
                已成功邀请人数
              </h2>
              <div class="data">
                <span class="number2">{{ totalUser }}</span>人
              </div>
              <div class="link flex justify-center">
                邀请记录<p-icon
                  name="arrow-right"
                  size="24"
                />
              </div>
              <div class="line">
                <p-line
                  direction="col"
                />
              </div>
            </div>

            <div
              v-stat.click="'mgm-m1-invite.prizeNum'"
              class="item flex-1"
              :data-stat="JSON.stringify(reportInfo)"
              @click="onShowPrizeList"
            >
              <h2 class="title">
                已获得奖励金额
              </h2>
              <div class="data">
                <span class="number2">{{ totalPrize }}</span>元
              </div>
              <div class="link flex justify-center">
                立减金记录<p-icon
                  name="arrow-right"
                  size="24"
                />
              </div>
            </div>
          </div>
          <p-button
            v-stat.click="'mgm-m1-invite.invite-btn'"
            type="primary"
            class="btn"
            :data-stat="JSON.stringify(reportInfo)"
            :disabled="!btnAbleFlag"
            @click="onShare"
          >
            {{ btnText }}
          </p-button>
          <p-button
            v-stat.click="'mgm-m1-invite.subscribe-btn'"
            type="link"
            :data-stat="JSON.stringify(reportInfo)"
            class="subscribe mt-40rpx mb-34rpx"
            @click="onSubscribe"
          >
            订阅活动信息通知
          </p-button>
          <!-- 引导订阅tips -->
          <div
            v-if="showTooltips"
            class="absolute top-428rpx left-22rpx z-30"
          >
            <tooltips
              :tooltips-width="612"
              :triangle-left="294"
            >
              <div class="flex w-532rpx items-center justify-between">
                订阅后可在立减金到账时马上收到通知
                <div
                  class="w-32rpx h-32rpx tooltips-close-btn"
                  @click="onTooltipsCloseBtnClick"
                />
              </div>
            </tooltips>
          </div>
        </div>
      </div>
    </RetryContainer>
    <popup-user
      v-if="userList"
      v-model="showUserList"
      :user-list="userList"
      :act-online-flag="m1PageState===M1PageState.ACT_ONLINE"
      @notice="onShare"
    />
    <popup-prize
      v-if="prizeList"
      v-model="showPrizeList"
      :prize-list="prizeList"
    />
    <ShareTips
      v-model="showShareTips"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import RetryContainer from '@tencent/ppd-uni-component/packages/retry-container/index.vue';
import { RetryMode } from '@tencent/ppd-uni-component/packages/retry-container/types';
import PopupUser from './components/popup-user/index.vue';
import PopupPrize from './components/popup-prize/index.vue';
import ShareTips from '../private/share-tips/index.vue';
import {
  getM1PageState,
  M1PageState,
  handleDefaultException,
  m1PageBtnMap,
} from './logic/pageStateHandler';
import { MGM_SERVICE_KEY, USER_INFO_SERVICE_KEY } from '@/domain/prom/token';
import { AssistState, BaseUserPrizeInfoEntity, PrizeState, ShareRelationInfoEntity } from '@tencent/ppd-uni-domain';
import { LOGGER_SERVICE_KEY } from '@tencent/ppd-uni-component/packages/token';
import { UserInfo } from '@/adapters/domain';
import {  shareHooks } from '@/hooks/share-hooks';
import { getOpenid } from '@/adapters/login';
import { getCommonWebViewUrl, navigatorToHelper, navigatorToHome, redToCommonWebView } from '@/route/miniprogram';
import { queryAndSelectivelySaveUserWxInfo } from '@/hooks/user-info-hooks';
import { getKeyFromHashOrQuery, replaceUrlPlaceholders } from '@/utils/url';
import { UseCustomShare } from '@tencent/ppd-uni-component/packages/common-webview/type';
import { commonModal } from '@/adapters/uni-api-common';
import tooltips from '../private/tooltips/index.vue';

const emit = defineEmits(['share']);

const mgmService = inject(MGM_SERVICE_KEY);
const userInfoService = inject(USER_INFO_SERVICE_KEY);
const loggerService = inject(LOGGER_SERVICE_KEY);
// const uiService = inject(UI_SERVICE_KEY);
const CustomException = loggerService?.CustomException;

// 自动拉起奖品列表标志位
const isOpenPrizePopup = getKeyFromHashOrQuery('openPrize');

// 自动拉起邀请用例列表标志位
const isOpenUserPopup = getKeyFromHashOrQuery('openUserList');
/**
 * 订阅消息tooltips可视标志位
*/
const showTooltips = ref(false);

const entryChannel = getKeyFromHashOrQuery('entryChannel');
const props = withDefaults(defineProps<{
  subActId: string,
  envBgImg: string,
  m2RedPath: string,
  shareInfo: {
    title: string,
    imageUrl: string,
    path: string,
    shareKey?: string,
    barBgColor?: string,
  }
}>(), {
  subActId: '',
  envBgImg: '',
  shareInfo: () => ({
    title: '',
    imageUrl: '',
    path: '',
  }),
});

const m1PageState = ref<M1PageState>(M1PageState.ACT_NOT_START);

/**
 * 按钮文案
*/
const btnText = ref<string>('');
const btnAbleFlag = ref<boolean>(false);

const userList = ref<ShareRelationInfoEntity<UserInfo>[]>([]);
const prizeList = ref<BaseUserPrizeInfoEntity[]>([]);
const userInfo = ref<{avatar: string, nickname: string}|null>(null);
const shareKey = ref<string>('');
const showUserList = ref(false);
const showPrizeList = ref(false);
const showShareTips = ref(false);

const totalUser = computed(() => {
  if (!userList.value) {
    return 0;
  }
  return userList.value.filter(item => +item.assist_state === +AssistState.ASSISTED).length;
});

/**
 * 落地页展示中台：
 * init：初始态，用户未邀请过任何人时命中，不会展示邀请人数、已得奖品等活动数据
 * detail：详情态，用户邀请了M2后命中，展示邀请人数、已得奖品等活动数据
*/
const showType = computed(() => (userList.value.length <= 0 ? 'init' : 'detail'));
const totalPrize = computed(() => {
  if (!prizeList.value) {
    return 0;
  }
  return prizeList.value?.reduce((pre, info) => pre + (info.price_amount_yuan ?? 0), 0);
});
const reportInfo = computed(() => ({
  // 展示方式
  showType: showType.value,
  totalSuccessUser: totalUser.value,
  totalPrize: totalPrize.value,
}));
const status = ref(RetryMode.Loading);

const onShare = () => {
  showShareTips.value = true;
  emit('share');
};
const onSubscribe = () => {
  navigatorToHelper({
    background: 'https://weremit-static.tenpay.com/upload-static/remit/Pp5g2NAPTlIf9v1GoxW0Ec-slice4.jpg',
    btnTop: 1100,
    backgroundColor: '#f5f5f5',
    type: 'requestSubscribeMessage',
    entryChannel,
    options: {
      templates: {
        'lIyj-a8N50-cmdQ7JEpm3LIxoV8A0ONNA-uvdBLR5n4': '优惠券到账通知',
        QY0gbxWZjFtlvRf3i0wTC3n71dqCfRcf0FXuKcvDSbU: '活动进度通知',
        h8GFJDWRF9kw8HHi15DEw2ID2EHmfNhUpWJbtxn9yx0: '活动上新通知',
      },
    },
  });
};
const onShowUserList = () => {
  showUserList.value = true;
};
const onShowPrizeList = () => {
  showPrizeList.value = true;
};
const onTooltipsCloseBtnClick = () => {
  showTooltips.value = false;
};

watch(() => [showType.value], () => {
  if (showType.value === 'detail') {
    showTooltips.value = true;
    setTimeout(() => {
      showTooltips.value = false;
    }, 10000);
  }
});

/**
 * shareKey和userInfo都有值的时候，才更新分享信息
 **/
watch(() => [shareKey.value, userInfo.value], () => {
  if (!shareKey.value || !userInfo.value) {
    return;
  }
  const openid = getOpenid() ?? '';
  if (!openid && CustomException) {
    new CustomException(null, 'not-openid', '获取openid失败');
  }
  const sharePath = getCommonWebViewUrl({
    url: replaceUrlPlaceholders(props.shareInfo.path, {
      shareKey: encodeURIComponent(shareKey.value),
      uid: encodeURIComponent(userInfoService?.getSm3HashedOpenid(openid) ?? ''),
      nickname: encodeURIComponent(userInfo.value.nickname),
      avatar: encodeURIComponent(userInfo.value.avatar),
    }),
    barBgColor: props.shareInfo.barBgColor ?? '',
    barFontColor: props.shareInfo.barBgColor ? '#000000' : '',
  });
  const { initShare } = shareHooks({
    ...props.shareInfo,
    priority: 2,
    path: sharePath,
  });
  initShare();
});
/**
 * 设置默认分享
 **/
function initDefaultShare() {
  let url = `${location.protocol}//${location.host}${location.pathname}${location.hash}`;
  // 替换渠道号
  url = url.replace(/([?&])entryChannel=[^&]*/, '$1entryChannel=Iqr46p009v001');
  const sharePath = getCommonWebViewUrl({
    url,
    barBgColor: props.shareInfo.barBgColor ?? '',
    barFontColor: props.shareInfo.barBgColor ? '#000000' : '',
    useCustomShare: UseCustomShare.YES,
  });
  const { initShare } = shareHooks({
    ...props.shareInfo,
    priority: 1,
    path: sharePath,
  });
  initShare();
}

/**
 * 判断是否是M2点开完成任务分享
*/
function judgeAndDealM2SelfClick() {
  const sm3M2Openid = decodeURIComponent(getKeyFromHashOrQuery('sm3M2Openid') ?? '');
  const uid = getOpenid() ?? '';
  if (uid && sm3M2Openid) {
    // 获取当前sm3哈希的openid
    const sm3HashedOpenid = userInfoService?.getSm3HashedOpenid(uid);
    // 如果openid摘要一致，则视为本人点开分享卡片，将重定向到M2页面
    if (sm3HashedOpenid === sm3M2Openid) {
      const m1ShareKey = getKeyFromHashOrQuery('m1ShareKey');
      const url = replaceUrlPlaceholders(props.m2RedPath, {
        entryChannel: 'Ifa50p009v001',  // 前端含义：M2 完成任务后分享卡片
        shareKey: m1ShareKey,
        uid: '',
        nickname: '',
        avatar: '',
        enableShare: 'true',
      });
      redToCommonWebView({
        url,
        title: '',
        barBgColor: '#E9FDE8',
        barFontColor: '#000000',
        useCustomShare: UseCustomShare.YES,
      });
      return true;
    }
  }
}

/**
 * 判断并处理目标分享M1用户点开分享卡片
*/
function judgeAndDealTargetM1OpenShare(shareKey: string) {
  const m1ShareKey = decodeURIComponent(getKeyFromHashOrQuery('m1ShareKey') ?? '');
  if (m1ShareKey === shareKey) {
    showPrizeList.value = true;
  }
}

onLoad(() => {
  initDefaultShare();
  (async () => {
    try {
      /**
       * 判断是否M2本人点开分享卡片
      */
      if (judgeAndDealM2SelfClick()) {
        return;
      }
      // 获取活动信息
      const basePromInfoEntity = await mgmService!.qrySubActInfo(props.subActId);

      /**
       * 二次判断是否是本人点开分享卡片
       * 一次判断时，可能由于没登录态导致无openid
      */
      if (judgeAndDealM2SelfClick()) {
        return;
      }
      // 如果没有奖品id，直接抛错
      if (!basePromInfoEntity.defaultPrizeClassId) {
        throw new Error('qryAllPrizeList-params-error');
      }

      m1PageState.value = getM1PageState(basePromInfoEntity);

      // 映射按钮文案和启用状态
      const btnInfo = m1PageBtnMap[m1PageState.value];
      btnText.value = btnInfo.text;
      btnAbleFlag.value = btnInfo.able;

      // 活动未开始情况下，不用走后置流程了，直接return
      if (m1PageState.value === M1PageState.ACT_NOT_START) {
        commonModal({
          content: '活动还未开始，敬请期待',
          showCancel: false,
          confirmColor: '#0CBD6A',
          confirmText: '返回微汇款',
          success: (res) => {
            if (res.confirm) {
              navigatorToHome();
            }
          },
        });
        return;
      }

      /**
       * 获取M1信息
      */
      const res = await mgmService!.getM1InitData(
        props.subActId,
        basePromInfoEntity.defaultPrizeClassId,
        PrizeState.Received,
        m1PageState.value === M1PageState.ACT_ONLINE,
      );
      prizeList.value = res.prizeList;

      let info;
      if (m1PageState.value === M1PageState.ACT_ONLINE) {
      // 获取用户信息
        info = await queryAndSelectivelySaveUserWxInfo();
      }

      // 设置用户微信信息
      userInfo.value = { nickname: info?.nickname ?? '', avatar: info?.avatar ?? '' };

      const { shareRelationInfoList } = res.inviterShareInfo;

      shareRelationInfoList.sort((itemA, itemB) => {
        // 首先比较 hasCompletedAssist 字段
        if (itemA.hasCompletedAssist && !itemB.hasCompletedAssist) {
          return -1; // itemA 在 itemB 前面
        } if (!itemA.hasCompletedAssist && itemB.hasCompletedAssist) {
          return 1; // itemB 在 itemA 前面
        }
        // 如果 hasCompletedAssist 相同，则按 create_relation_time 排序
        const timeA = itemA.create_relation_timestamp || 0;
        const timeB = itemB.create_relation_timestamp || 0;
        return timeB - timeA; // 时间晚的在前面
      });

      userList.value  = shareRelationInfoList;

      const openid = getOpenid() ?? '';
      if (!openid && CustomException) {
        new CustomException(null, 'not-openid', '获取openid失败');
      }
      shareKey.value = res.shareKey;
      status.value = RetryMode.Success;
    } catch (err) {
      handleDefaultException();
      if (CustomException) {
        new CustomException(err, 'mgmService-getM1InitData-error', '获取M1数据失败');
      }
    }

    /**
     * 判断自动拉起半弹层逻辑
     * 前置条件是页面命中详情态
    */
    if (showType.value === 'detail') {
      if (isOpenPrizePopup) {
        /**
         * 当页面命中详情态，且页面配置了自动拉起奖品弹窗标志位时，展示奖品弹窗
        */
        showPrizeList.value = true;
      } else if (isOpenUserPopup) {
        /**
        * 当页面配置了自动拉起奖品弹窗标志位时，展示奖品弹窗
        * 此处有个else逻辑，避免一次拉起两个弹窗。但是实际场景里正常不会出现
        */
        showUserList.value = true;
      }
      judgeAndDealTargetM1OpenShare(shareKey.value);
    }
  })();
});

</script>
<style lang="less" scoped>
.invite-container{
  width: 100%;
  box-sizing: border-box;

  .invite-init-container{
    padding-bottom: 0rpx;
  }
  .invite-invited-container{
    background-color: rgba(255,255,255, .64);
    border-radius: 18rpx;
    padding: 56rpx 0rpx 16rpx;
  }
  .invite-data{
    display: flex;
    text-align: center;
    .data{
      margin-top:3rpx;
      .number2{
        font-size:68rpx;
      }
    }
    .item{
      flex:1;
      position: relative;
      .line{
        position: absolute;
        top: 0px;
        right:0px;
        height: 54rpx;
        top: 54rpx;
      }
    }
    .link{
      font-size: 20rpx;
    }
    .title{
      color: rgba(0,0,0,0.64);
      font-size:24rpx;
      font-weight: 600;
    }
  }
  .btn{
    width: 432rpx;
    height: 88rpx;
    margin-left: auto;
    margin-right: auto;
    font-size: 34rpx;
    line-height: 48rpx;
    font-weight: 600;
  }
}
.popup-title{
  height: 136rpx;
  padding: 48rpx;
  box-sizing: border-box;
  background-color: #E9FCEB;
}
.popup-content{
  overflow: scroll;
  background: linear-gradient( #E9FCEB, #F7F7F7);
}
.subscribe{
  color:#0CBD6A;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 28rpx;
  height: 28rpx;
}
.env-bg{
  width: 100%;
  background-size: 100% auto;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  height: 570rpx;
  transition: all .5s;
  &.init{
    height: 610rpx;
  }

}
.tooltips-close-btn{
  background-image: url('https://weremit-static.tenpay.com/upload-static/remit/whckDadhAv6ete5bTRJsqj-cHVibGljL2Nsb3Nl.svg');
  background-repeat: no-repeat;
  background-size: 100% auto;
}
</style>


