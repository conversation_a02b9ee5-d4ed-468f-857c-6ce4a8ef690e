<template>
  <div
    v-bind="$attrs"
    class="w-full h-full"
    @click="onRuleBtnClick"
  />
</template>

<script setup lang="ts">
import { useEventEmitter } from '../../../hooks/emitter-hooks/index';
import { biClick } from '@tencent/fit-bi-sdk';

/**
 * 从总线中获取规则按钮点击事件的emit方法
*/
const {
  emit,
} = useEventEmitter('ON_CLICK_RULE_POPUP_BTN');

const onRuleBtnClick = () => {
  biClick('rule-btn.click');
  // emit事件，规则弹窗监听到事件后会弹窗
  emit(true);
};
</script>
<style lang="less" scoped>
</style>


