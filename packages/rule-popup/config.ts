import component from './rule-popup.vue';

const config = {
  type: 'rule-popup',
  maintainer: 'orizhu',
  name: 'RulePopup组件',
  desc: 'RulePopup组件',
  icon: '',
  runtime: 'custom',
  component: (element, props) => {
    const data = ref(props);
    createApp(() => h(component, data.value)).mount(element);
    return {
      setUpdateData(newData) {
        data.value = newData;
      },
    };
  },
  dataSets: [
    { key: 'ruleContent', title: '活动规则内容', type: 'array', defaultValue: [
      '1.活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则',
      '2.活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则',
      '3.活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则',
      '4.活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则活动规则'], properties: [{
      type: 'string',
    }] },
    { key: 'popupStyle', title: '活动规则弹窗样式，可设置背景颜色背景图等，作用于活动弹窗', type: 'string', defaultValue: 'background-color: #fff;'  },
  ],
  stateSets: [],
  eventSets: [],
  create() {
    return {
      style: {
        position: 'absolute',
        left: 0,
        right: 0,
        'z-index': 999,
      },
      data: {
      },
    };
  },
};

export default config;
