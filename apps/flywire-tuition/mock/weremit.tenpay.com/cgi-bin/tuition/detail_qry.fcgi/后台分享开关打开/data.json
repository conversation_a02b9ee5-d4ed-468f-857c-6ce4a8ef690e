{"retcode": "0", "retmsg": "OK", "account": "**********", "account_code": "zh20200318113811000167", "account_name": "New York University", "attach": "Cxx", "bank_name": "JP Morgan Chase Bank", "big_pay_finish_flag": "0", "big_pay_flag": "1", "big_pay_not_in_use_left_time": "0", "big_pay_not_in_use_reason": "", "can_edit_more_again": "1", "channel_id": "we_remit", "crn_code": "", "crn_type": "1", "currency": "USD", "currency_point_num": "2", "default_pay_type": "1", "edit_flag": "1", "is_user_list": "1", "kyc_flag": "0", "last_audit_fail_reason": "", "last_audit_fail_time": "", "list_state": "20010", "list_user_name": "", "listid": "5991800008742202109223280015498", "original_total_fee": "2", "pay_amt": "261043", "pay_share_flag": "0", "pay_share_switch": "1", "pay_type_tips": "", "rate": "6526025", "relation": "2", "routing_code": "*********", "routing_type": "ABA", "school_name_en": "New York University (New York)", "school_system": "14", "share_param": "AW8BAGqKbD8spKIslRX4nFutHagiCX0IbFxsglj4zA8BNt5_LCRE3QDboVgL-s5njQJC1B45y98", "sign": "AW8BAGqKbD8spKIslRX4nFutHagiCX0IbFxsglj4zA8BNt5_LCRE3bcLuQ0ocnMUh384z5IN-_D7tQ019oCYTQMj-Uy8ehjRwVnJK1EGkT2DXX7RlWjT_9LL6SX8p_4ge7VgleS0u2ubshabdNioEGXX0ouPa-tRIf3ZbX8Uui533LMS-TTRCA", "student_cre_id": "410821********0159", "student_email": "t***@qq.com", "student_first_name": "w******", "student_id": "USD", "student_last_name": "liu", "student_name": "li********", "student_passport_no": "123****23", "student_phone": "188****2222", "swift_code": "CHASUS33", "total_fee": "2", "tuition_amt": "40000", "tuition_deadline": "2021-10-22", "type": "1", "file_list": [{"file_id": "AW8BAGqKbD-4t28foE1m98pWC6FARXo1Syuug9I1eYavK21OS_uKuLXMUE9QbEzhJfvqLnGa34Lr1UK7BXaMGwQaL8mv_V7ECE69R7XgDhQsPNj9F7AHrhGPG_zY8tO8BQYEZ9P5kLI", "file_type": "OFE"}, {"file_id": "AW8BAGqKbD-4t28foE1m98pWC6FARXo1Syuug9I1eYavK21OS_uKuH3ARgzIpmQoG_A2yxZSukXmMsPK3gPTfYPUpILGEuN9WxEVpUcTj_KLDRVZy0CgU9hjRQEobAsxMS3LoYfDapE", "file_type": "INV"}]}