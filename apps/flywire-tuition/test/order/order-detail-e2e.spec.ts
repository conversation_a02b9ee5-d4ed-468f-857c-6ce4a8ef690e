import automator from 'miniprogram-automator';
import { describe, beforeAll, afterAll, expect, it } from 'vitest';
import fs from 'fs-extra';
import { testCaseConfig } from './data';
import { NameSpace } from '../../app-config/enum';
import MiniProgram from 'miniprogram-automator/out/MiniProgram';
import Page from 'miniprogram-automator/out/Page';
import Element from 'miniprogram-automator/out/Element';
import { OrderState } from '../../app-config/types/pages/order-detail';


function createTestList(testCase): {
  code: string,
  state: string,
  title: string,
  desc: string;
  failState: string,
  failStateStr: string,
  editFlag: string,
  label: string,
}[] {
  return testCase.split('\n').map((item) => {
    const strArr = item.split('\t');
    const [code,,state, title, desc,,failStateStr, editFlag, label] =  strArr;
    const matchRes = failStateStr?.match(/\d+/);
    const failState = matchRes ? matchRes[0] : '';
    return { code, state, title, desc, failState, failStateStr, editFlag, label };
  })
    .filter(item => item.code && item.failStateStr !== '代码中不会有这种情况');
}

function mockOrder(options) {
  // 基础mock数据
  const baseData = { retcode: '0', retmsg: 'OK', last_audit_fail_reason: '测试失败原因', audit_finaltime: '2023-02-06 08:55:43', back_fc_amt: '0', bank_name: '', channel_id: 'flywire', create_time: '2023-02-02 10:50:12', currency: 'CAD', currency_point_num: '2', currency_symbol: '$', edit_flag: '0', fail_reason: '', fail_state: '30030', fail_time: '1970-01-01 00:00:01', is_user_list: '1', kyc_flag: '0', list_state: '80250', list_user_name: '', listid: '5002911117631202302027', modify_time: '2023-02-06 10:37:30', org_listid: '1555155111112023020201', original_total_fee: '738001', pay_amt: '*********', pay_share_flag: '0', pay_share_switch: '1', pay_time: '2023-02-02 14:14:34', payment_fc_finaltime: '1970-01-01 00:00:01', rate: '0', refund_amt: '*********', refund_fee: '738001', refund_finaltime: '1970-01-01 00:00:01', refund_time: '1970-01-01 00:00:01', relation: '14', school_name_en: 'Columbia International', share_param: '', student_name: '*江', total_fee: '738001', tuition_amt: '0', user_tuition_amt: '1231112' }  ;
  // 把时间字段都转换成字符串
  Object.keys(baseData).forEach((key) => {
    if (key.endsWith('time')) {
      baseData[key] = key;
    }
  });
  if (['60140', '80250'].includes(options.list_state)) {
    // 修改为部分退款
    baseData.pay_amt += 1000;
  }
  return Object.assign({}, baseData, options, {
    edit_flag: options.edit_flag || '0',
    big_pay_finish_flag: options.big_pay_finish_flag || '0',
  });
}


describe('index', () => {
  let miniProgram: MiniProgram | undefined;
  let page: Page | undefined;

  beforeAll(async () => {
    miniProgram = await automator.launch({
      projectPath: './dist/dev/mp-weixin',
    });
    page = await miniProgram.reLaunch('/pages/order/detail?listid=5002911117631202212078894960016');
    await page?.waitFor(2000);
  }, 300000);

  it('获取标题', async () => {
    if (!process.env.UNI_CUSTOM_DEFINE) {
      throw new Error('process.env.UNI_CUSTOM_DEFINE不存在');
    }

    const APP_NAME = JSON.parse(process.env.UNI_CUSTOM_DEFINE).APP_NAME as NameSpace;
    console.log(`-------当前环境是${APP_NAME}--------`);
    const testList = createTestList(testCaseConfig[APP_NAME]);
    fs.removeSync('dist/img');
    for (let i = 0;i < testList.length;i++) {
      const item = testList[i];
      console.log(item);
      const order = mockOrder({
        list_state: item.code,
        fail_state: item.failState,
        edit_flag: item.editFlag ? '2' : '0',
      });
      // eslint-disable-next-line no-loop-func
      await miniProgram?.evaluate((order: any) => {
        getApp().globalDetail.value = order;
      }, order);
      await page?.waitFor(500);
      const filePath = `dist/img/${i}-${item.code}-${item.state}-${item.failStateStr}.png`;
      fs.ensureFileSync(filePath);
      await miniProgram?.screenshot({
        path: filePath,
      });
      const titleNode = await page?.$('.title3-sem.ml-28rpx');
      const descNode = await page?.$('.order-desc-node');
      const listNode = await page?.$$('.order-info-item-tag .order-item-label') as Element[] | null;
      const texts: string[] = [];
      if (!titleNode) {
        throw new Error('titleNode节点不存在！');
      }
      if (!descNode) {
        throw new Error('descNode节点不存在！');
      }
      if (listNode) {
        for (const textItem of listNode)  {
          texts.push(await textItem.text());
        }
      } else {
        throw new Error('listNode节点不存在！');
      }
      // 检查标题是否正确
      expect(await titleNode?.text()).toBe(item.title);
      // 检查副标题是否正确
      if (
        item.code === OrderState.ORDER_VERIFYING
        && item.editFlag
      ) {
        expect((await descNode?.text())).toContain(item.desc.replace('【接口返回的审核失败原因】', ''));
      } else {
        // 验证副标题
        expect(await descNode?.text()).toEqual(item.desc);
      }
      // 检查表单字段是否正确
      expect(texts.join(',')).toBe(item.label);
    }
  });

  afterAll(async () => {
    // await miniProgram.close();
  });
});
