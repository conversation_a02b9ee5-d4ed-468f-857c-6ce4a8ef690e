import { InfoConfig, OrderInfoType } from '../../src/pages/order/components/components/types';
import { OrderInfoTextMap, OrderProcessConfig, OrderState, ProcessType } from '../types/pages/order-detail/index';
import _ from 'lodash';

type PartialObject<T> = Partial<{
  [Key in keyof T]: Partial<PartialObject<T[Key]>>;
}>;

/**
 * 获取的订单进度文案映射配置
 * @param override 覆盖配置
 * @returns
 */
export function getOrderProcessTextMap(overrideConfig: PartialObject<OrderProcessConfig>): OrderProcessConfig {
  const orderProcessTextMap: OrderProcessConfig = {
    [OrderState.UN_PAY]: {
      bigTit: '等待付款',
      desc: '',
      listData: {
        icon: 'info',
      },
      detailData: {
        time: '',
        [ProcessType.IN_PROCESS]: {
          type: 'success',
        },
      },
    },
    // 订单超时, 中间状态，对了映射为订单关闭，只有时间线配置有意义，其余配置无效
    [OrderState.EXPIRED]: {
      bigTit: '超时未支付',
      desc: '',
      listData: {
        icon: 'error',
      },
      detailData: {
        time: '',
        preProcess: {
          type: 'success',
        },
        inProcess: {
          type: 'error',
        },
      },
    },
    [OrderState.CLOSED]: {
      bigTit: '订单关闭',
      desc: '超时未支付，请重新下单',
      listData: {
        icon: 'error',
      },
      detailData: {
        time: '',
        afterProcess: {
          type: 'hold',
        },
        inProcess: {
          type: 'error',
        },
      },
    },
    [OrderState.PAY_SUCCESS]: {
      bigTit: '支付成功',
      desc: '',
      listData: {
        icon: 'success',
      },
      detailData: {
        time: '',
        preProcess: {
          type: 'success',
        },
        inProcess: {
          type: 'success',
        },
        afterProcess: {
          type: 'hold',
        },
      },
    },
    [OrderState.ORDER_VERIFYING]: {
      bigTit: '订单审核',
      desc: '预计工作日6小时内返回审核结果，如非工作日，请等待至工作日处理',
      listData: {
        icon: 'info',
      },
      detailData: {
        time: '',
        preProcess: {
          tit: '审核成功',
          type: 'success',
        },
        inProcess: {
          tit: '订单审核中',
          type: 'success',
        },
        afterProcess: {
          tit: '审核成功',
          type: 'hold',
        },
      },
    },
    [OrderState.AUDIT_FAIL]: {
      bigTit: '审核失败',
      desc: '',
      listData: {
        icon: 'error',
      },
      detailData: {
        time: '',
        inProcess: {
          type: 'error',
        },
      },
    },
    [OrderState.ORDER_AUDITED_BUT_NOT_SUCCESS]: {
      bigTit: '',
      desc: '',
      listData: {
        icon: 'success',
      },
      detailData: {
        time: '',
        preProcess: {
          type: 'success',
        },
        inProcess: {
          type: 'success',
        },
        afterProcess: {
          type: 'hold',
        },
      },
    },
    [OrderState.ORDER_REMIT_SUCCESS]: {
      bigTit: '',
      desc: '',
      listData: {
        icon: 'success',
      },
      detailData: {
        time: '',
        preProcess: {
          type: 'success',
        },
        inProcess: {
          type: 'success',
        },
        afterProcess: {
          type: 'hold',
        },
      },
    },
    [OrderState.REMIT_FAIL]: {
      bigTit: '缴费失败，退款中',
      desc: '',
      listData: {
        icon: 'error',
      },
      detailData: {
        time: '',
        preProcess: {
          tit: '缴费失败',
          type: 'error',
        },
        inProcess: {
          tit: '缴费失败',
          type: 'error',
        },
        afterProcess: {
          type: 'hold',
        },
      },
    },
    [OrderState.REFUNDING]: {
      bigTit: '缴费失败，退款中',
      desc: '',
      listData: {
        icon: 'error',
      },
      detailData: {
        time: '',
        preProcess: {
          tit: '退款中',
          type: 'success',
        },
        inProcess: {
          type: 'success',
          tit: '退款中',
        },
        afterProcess: {
          tit: '退款中',
          type: 'hold',
        },
      },
    },
    [OrderState.REFUND_SUCCESS]: {
      bigTit: '缴费失败，已退款',
      desc: '',
      listData: {
        icon: 'error',
      },
      detailData: {
        time: '',
        inProcess: {
          type: 'success',
          tit: '退款成功',
        },
        afterProcess: {
          type: 'hold',
          tit: '退款成功',
        },
      },
    },
  };
  return _.merge(orderProcessTextMap, overrideConfig);
}


export enum OrderInfoTextId {
  /**
   * 退款金额
   */
  FORMAT_REFUND_AMT,
  /**
   * 支付金额
   */
  FORMAT_PAY_AMT,
  /**
   * 缴费金额
   */
  FORMAT_TUITION_AMT,
  /**
   * 收款学校
   */
  SCHOOL_NAME_EN,
  /**
   * 学生姓名
   */
  STUDENT_NAME,
  /**
   * 支付订单号
   */
  LIST_ID,
  /**
   * 付款编号
   */
  ORG_LIST_ID,
  /**
   * 下单渠道
   */
  CHANNEL,
}

export interface InfoConfigExpand extends InfoConfig {
  /**
   * 唯一id
   */
  id: OrderInfoTextId;
}

/**
 * 获取基础订单信息列表schema
 * @param override schema配置覆盖
 * @returns
 */
export function getOrderInfoListBaseSchema(override?: PartialObject<
Record<OrderInfoTextId, InfoConfig>>): InfoConfigExpand[] {
  const config =  [
    {
      id: OrderInfoTextId.FORMAT_REFUND_AMT,
      key: 'formatRefundAmt',
      label: '退款金额',
    },
    {
      id: OrderInfoTextId.FORMAT_PAY_AMT,
      key: 'formatPayAmt',
      label: '支付金额',
    },
    {
      id: OrderInfoTextId.FORMAT_TUITION_AMT,
      key: 'formatTuitionAmt',
      label: '缴费金额',
    },
    {
      id: OrderInfoTextId.SCHOOL_NAME_EN,
      key: 'school_name_en',
      label: '收款学校',
    },
    {
      id: OrderInfoTextId.STUDENT_NAME,
      key: 'student_name',
      label: '学生姓名',
    },
    {
      id: OrderInfoTextId.LIST_ID,
      key: 'listid',
      label: '支付订单号',
    },
    {
      id: OrderInfoTextId.ORG_LIST_ID,
      key: 'org_listid',
      label: '付款编号',
    },
    {
      id: OrderInfoTextId.CHANNEL,
      key: 'formatChannelName',
      label: '下单渠道',
    },
  ];

  return config.map(item => ({
    ...item,
    ...override?.[item.id],
  }));
}

export interface Info {
  /**
   * schema配置的id列表，用于配置当前需要的schema是哪种，且展示顺序按数组顺序的配置
   */
  ids: OrderInfoTextId[];
  /**
   * 覆盖对应id的配置
   */
  override?: PartialObject<Record<OrderInfoTextId, InfoConfig>>;
}

export interface Config {
  /**
   * schema基础配置原串
   */
  schemaBase: InfoConfigExpand[];
  /**
   * schema详细展示配置
   */
  config: Partial<Record<OrderState, Info>>;
  /**
   * schema兜底展示配置
   */
  bottomConfig: Info;
}

export interface OrderInfoTextMapUI {
  /**
   * 订单文案映射配置
   */
  orderInfoTextMap: OrderInfoTextMap;
  /**
   * 订单详情兜底配置
   */
  orderInfoBaseConfig: InfoConfig[];
}

export function getOrderInfoTextMapBase(options: Config): OrderInfoTextMapUI {
  const {
    schemaBase,
    config,
    bottomConfig,
  } = options;

  /**
   * 订单信息文案映射
   */
  const orderInfoTextMap: OrderInfoTextMap = {};
  // 转换配置
  (Object.keys(config) as (keyof typeof config)[]).forEach((key) => {
    const configItem = config[key];
    if (configItem) {
      const infoConfigList: InfoConfig[] = [];
      configItem.ids.forEach((id) => {
        const schemaItem = schemaBase.find(item => item.id === id);
        if (schemaItem) {
          // 深克隆配置
          const cloneData = JSON.parse(JSON.stringify(schemaItem)) as InfoConfigExpand;
          // 拼凑ui的数据
          const data: InfoConfig = {
            key: cloneData.key,
            label: cloneData.label,
            type: cloneData.type,
            ...configItem.override?.[id],
          };
          infoConfigList.push(data);
        } else {
          throw new Error(`id为${id}的配置未找到`);
        }
      });
      orderInfoTextMap[key] = infoConfigList;
    }
  });

  /**
   * 兜底配置
   */
  const orderInfoBaseConfig = bottomConfig.ids.map((id) => {
    const schemaItem = schemaBase.find(item => item.id === id);
    if (!schemaItem) {
      throw new Error(`id为${id}的配置未找到`);
    }
    return {
      key: schemaItem.key,
      label: schemaItem.label,
      type: schemaItem.type,
      ...bottomConfig.override?.[id],
    };
  });

  return {
    orderInfoTextMap,
    orderInfoBaseConfig,
  };
}

export interface GetOrderInfoTextMapOptions {
  /**
   *  schema基础配置覆盖
   */
  schemaBaseOverride?: PartialObject<Record<OrderInfoTextId, InfoConfig>>;
}

export function getOrderInfoTextMap(options: GetOrderInfoTextMapOptions): OrderInfoTextMapUI {
  const {
    schemaBaseOverride,
  } = options;
  return getOrderInfoTextMapBase({
    schemaBase: getOrderInfoListBaseSchema(schemaBaseOverride),
    config: {
      // 待支付
      [OrderState.UN_PAY]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 订单审核中
      [OrderState.ORDER_VERIFYING]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 汇出成功
      [OrderState.ORDER_REMIT_SUCCESS]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 审核失败
      [OrderState.AUDIT_FAIL]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 退款成功
      [OrderState.REFUND_SUCCESS]: {
        ids: [
          OrderInfoTextId.FORMAT_REFUND_AMT,
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 退款中
      [OrderState.REFUNDING]: {
        ids: [
          OrderInfoTextId.FORMAT_REFUND_AMT,
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 缴费失败
      [OrderState.REMIT_FAIL]: {
        ids: [
          OrderInfoTextId.FORMAT_REFUND_AMT,
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 银行处理中
      [OrderState.ORDER_AUDITED_BUT_NOT_SUCCESS]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
    },
    bottomConfig: {
      ids: [
        OrderInfoTextId.FORMAT_TUITION_AMT,
        OrderInfoTextId.SCHOOL_NAME_EN,
        OrderInfoTextId.STUDENT_NAME,
        OrderInfoTextId.LIST_ID,
        OrderInfoTextId.ORG_LIST_ID,
      ],
      override: {
        [OrderInfoTextId.FORMAT_TUITION_AMT]: {
          type: OrderInfoType.AMOUNT_BOLD,
        },
      },
    },
  });
}

/**
 * @description 获取标准模式订单详情配置
 * @param {GetOrderInfoTextMapOptions} options
 * @return {*}
 */
export function getStdOrderInfoTextMap(options: GetOrderInfoTextMapOptions): OrderInfoTextMapUI {
  const {
    schemaBaseOverride,
  } = options;
  return getOrderInfoTextMapBase({
    schemaBase: getOrderInfoListBaseSchema(schemaBaseOverride),
    config: {
      // 待支付
      [OrderState.UN_PAY]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 订单审核中
      [OrderState.ORDER_VERIFYING]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 汇出成功
      [OrderState.ORDER_REMIT_SUCCESS]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 审核失败
      [OrderState.AUDIT_FAIL]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_PAY_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 退款成功
      [OrderState.REFUND_SUCCESS]: {
        ids: [
          OrderInfoTextId.FORMAT_REFUND_AMT,
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 退款中
      [OrderState.REFUNDING]: {
        ids: [
          OrderInfoTextId.FORMAT_REFUND_AMT,
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 缴费失败
      [OrderState.REMIT_FAIL]: {
        ids: [
          OrderInfoTextId.FORMAT_REFUND_AMT,
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
      // 银行处理中
      [OrderState.ORDER_AUDITED_BUT_NOT_SUCCESS]: {
        ids: [
          OrderInfoTextId.FORMAT_PAY_AMT,
          OrderInfoTextId.FORMAT_TUITION_AMT,
          OrderInfoTextId.SCHOOL_NAME_EN,
          OrderInfoTextId.CHANNEL,
          OrderInfoTextId.STUDENT_NAME,
          OrderInfoTextId.LIST_ID,
          OrderInfoTextId.ORG_LIST_ID,
        ],
        override: {
          [OrderInfoTextId.FORMAT_REFUND_AMT]: {
            type: OrderInfoType.AMOUNT_BOLD,
          },
        },
      },
    },
    bottomConfig: {
      ids: [
        OrderInfoTextId.FORMAT_TUITION_AMT,
        OrderInfoTextId.SCHOOL_NAME_EN,
        OrderInfoTextId.CHANNEL,
        OrderInfoTextId.STUDENT_NAME,
        OrderInfoTextId.LIST_ID,
        OrderInfoTextId.ORG_LIST_ID,
      ],
      override: {
        [OrderInfoTextId.FORMAT_TUITION_AMT]: {
          type: OrderInfoType.AMOUNT_BOLD,
        },
      },
    },
  });
}

