import { PAGE } from '@/constant/route';
import { navigate, NavigateType } from '@/adapters/route';
/**
 * 跳转订单确认页的options类型
 */
export type BigPayOptions = {
  listid: string
};

/**
 * 大额选卡页面入参
 */
export type BigPayCardConfirmOptions = {
  leftTime: string; // 剩余倒计时
  bigPayTip: string;
  leftTimeType: string;
  endTime: string;
  listid: string;
  sign: string;
};


/**
 * 跳转到支付方式选择页
 */
export const navigateToBigPayWaiting = (
  bigPayOptions: BigPayOptions,
  navigateType: NavigateType =  NavigateType.navigateTo,
) => {
  navigate({
    url: PAGE.BIG_PAY_WAITING,
    params: bigPayOptions,
  }, navigateType);
};

/**
 * 跳转选卡页的options类型
 */
export type CardConfirmOptions = {
  /**
   * 订单号
   */
  listid: string,
  /**
   * 大额支付剩余支付时间
   * 值为pay_req接口的big_pay_left_time
   */
  leftTime: string,
  /**
   * 大额支付签名
   * 值为pay_req接口的big_pay_sign
   */
  sign: string,
  /**
   * 支付类型说明
   * 值为detail_qry接口的pay_type_tips
   */
  bigPayTips: string,
  /**
   * 大额支付剩余支付时间类型
   * 值为pay_req接口的big_pay_left_time_type
   */
  leftTimeType: string,
  /**
   * 大额支付转账截止时间，纯文本，如2021.05.05 24:00
   * 值为pay_req接口的big_pay_end_time
   */
  endTime: string,
};

/**
 * 跳转到选卡页面
 */
export const navigateToCardConfirm = (
  cardConfirmOptions: CardConfirmOptions,
  navigateType: NavigateType = NavigateType.navigateTo,
) => {
  navigate({
    url: PAGE.CARD_CONFIRM,
    params: cardConfirmOptions,
  }, navigateType);
};
