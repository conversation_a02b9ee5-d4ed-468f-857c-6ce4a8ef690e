import { CustomException } from '@/adapters/report';
import { showLoading, hideLoading, commonModal, commonErrModal, showStackLoading, hideStackLoading } from './adapters/uni-api-common';
import { createModuleApplication, useClass } from '@tencent/tuition-common';
import bankTransferModule from './modules/bank-transfer/bank-transfer.module';
import pageBaseModule from './modules/page-base/page-base.module';
import guideSubscribeModule from './modules/guide-subscribe/guide-subscribe.module';
import payCenterModule from './modules/pay-center/pay-center.module';
import realNameModule from './modules/real-name/real-name.module';

export default createModuleApplication({
  imports: [
    // 使用页面基础模块
    pageBaseModule,
    // 使用引导订阅模块
    guideSubscribeModule,
    // 使用银行转账模块
    bankTransferModule,
    // 使用支付中心模块
    payCenterModule,
    // 使用实名模块
    realNameModule,
  ],
  setup() {
    return {
      CustomException: useClass(CustomException),
      commonErrModal,
      commonModal,
      showLoading,
      hideLoading,
      showStackLoading,
      hideStackLoading,
    };
  },
});

