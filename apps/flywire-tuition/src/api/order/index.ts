import { request } from '@/adapters/request';
import { IOrder } from './type';
import { useRuntimeConfig } from '@/app-config/useRuntimeConfig';
import { useAppRuntimeConfigStore } from '@/store/appRuntimeConfig';
import { CustomException } from '@/adapters/report';

interface IQueryOrderReq{
  page_no: number;
  // 订单业务类型
  busi_type?: string;
}

export interface HistoryListResp{
  has_next_page: '0' | '1'; // 下一页标识，0：没有下一页；1：还有下一页
  his_list?: IOrder[]; // 订单列表
}

export async function queryOrderList(params: IQueryOrderReq) {
  try {
    const { runtimeConfig } = useRuntimeConfig();
    const response = await request<HistoryListResp>({
      url: '/tuition/his_qry.fcgi',
      data: {
        page_no: params.page_no,
        busi_type: runtimeConfig.value.pages.orderList.busiType,
      },
    });
    // 获取store实例
    const appRuntimeConfigStore = useAppRuntimeConfigStore();
    const { updateOrgConfigs } = appRuntimeConfigStore;
    // 更新机构配置
    await updateOrgConfigs();
    return response;
  } catch (error) {
    new CustomException(error, 'queryOrderList_err', '获取订单列表记录失败，可能是获取机构配置失败导致');
    // 处理错误情况
    throw error;
  }
}
