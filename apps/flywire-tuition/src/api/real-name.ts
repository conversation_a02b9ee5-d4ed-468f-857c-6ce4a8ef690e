import { LOGIN_CHANNEL } from '@/constant/app';
import { request } from '@/adapters/request';

/**
 * 实名签名接口返回
 */
export interface RealnameSignRes {
  /**
   * API接口版本号	固定1.0
   */
  api_version: string;
  /**
   * 微信支付分配的商户号
   */
  mch_id: string;
  /**
   * 发起授权小程序的appid
   */
  appid: string;
  /**
   * 返回type 值只能为“code”
   */
  response_type: string;
  /**
   * 授权scope pay_realname: 获取实名信息(不含影印件)
   * pay_realnamewithcrecopy: 获取实名信息（含影印件）
   */
  scope: string;
  /**
   * 用户openid
   */
  openid: string;
  /**
   * 签名
   */
  sign: string;
  /**
   * 随机串
   */
  nonce_str: string;
  /**
   * 获取证件类型，空表示不限制 当前仅支持：CREDENTIAL_TYPE_MAINLAND_ID
   */
  credential_type: string;
  /**
   * 签名类型，仅支持HMAC-SHA256
   */
  sign_type: string;
}

/**
 * 查询用户实名信息
 */
export interface KycQryReq {
  /**
   * 小程序来源
   * we_remit: 微汇款自有渠道
   * flywire: 飞汇渠道
   * 不填或者为空字符串时，默认为 we_remit
   */
  login_channel?: string;
  /**
   * 渠道类型
   * 固定传 BOS – 上海银行
   */
  channel_id: string;
  /**
   * 业务类型   400 – 全球汇入
   */
  business_type: number;
  /**
   * 用户授权code
   */
  auth_code: string;
}

export interface KycQryRes {
  /**
   * （二进制转十进制存储）
   * 按位数：0表示没有 1表示有
   * 第一位：身份信息（最低位）
   * 第二位：OCR信息
   * 第三位：补充信息
   */
  state_kyc: string;
  /**
   * 影像状态
   * 0 未获取/已失效
   * 1 审核中（预留状态）
   * 2 审核失败（预留状态）
   * 3审核成功（预留状态）
   * 4已获取
   */
  state_image: string;
  /**
   * 0 - 不缺失  1-缺失
   * 第一位表示有效时间是否缺失：如 “001”
   * 第二位表示发证机关是否缺失：如 “010”
   * 第三位表示地址是否缺失：如 “100”
   */
  ocr_miss_field: string;
  /**
   * 拼音列表加密串， 填写补充信息时回传
   */
  name_py_list_aes: string;
  /**
   * 是否为二代身份证，1-是；0-不是
   */
  is_second_gen_id: string;
  /**
   * 姓名拼音列表（包括多音字）
   */
  pinyin_array: [];
  /**
   * 名的拼音列表（包括多音字）
   */
  forename_pinyin_array: [];
  /**
   * 姓的拼音列表（包括多音字）
   */
  lastname_pinyin_array: [];
}

/**
 * 获取实名组件参数
 */
export const getRealnameSign = () => request<RealnameSignRes>({
  url: '/remitkyc/we_remit_kyc_realname_sign.fcgi',
  data: {
    login_channel: LOGIN_CHANNEL,
  },
});

/**
 * 用户实名信息查询
 */
export const kycQry = (params: KycQryReq) => request<KycQryRes>({
  url: '/remitkyc/we_remit_kyc_qry.fcgi',
  data: params,
});
