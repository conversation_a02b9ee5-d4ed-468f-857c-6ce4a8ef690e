import { request } from '@/adapters/request/request-config';
import { RuntimeConfig } from 'app-config/types';
import { useSingleRequest } from '@/adapters/request';
import { withRetry } from '@tencent/ppd-common/src/utils/retry';

// 使用通用的重试函数来请求机构配置接口
export const getAppConfigs = () => withRetry(() => request<Record<string, Partial<RuntimeConfig>>>({
  url: '/weremit/tuition/runtimeConfig.json',
}));

export const singleGetAppConfigs = useSingleRequest().createRequest({
  cacheKey: 'appCnfigs',
  requestFn: getAppConfigs,
});
