// 账号信息
export interface AccountInfo {
  /**
   * 银行名称
   */
  bank_name: string;
  /**
   * 账号id
   */
  account_code: string;
  /**
   * 银行名称
   */
  account_name: string;
  /**
   * 收款人账号
   */
  account: string;
  /**
   * swift银行编码
   */
  swift_code: string;
  /**
   * 辅助清算码类型
   * IBA：Iban Code(欧盟)
   * BSB：Bsb Code(澳大利亚)
   * CCC：CC Code(加拿大)
   * SOR：Sort Code(英国)
   * ABA：ABA Number(美国)
   */
  routing_type: string;
  /**
   * 银行清算代码
   */
  routing_code: string;
}

// 账户类型信息
export interface AccountTypeInfo{
  /**
   * 账户类型
   * 1：电汇类型账户(Swift)
   * 2：加拿大billpay类型账户
   * 3：澳大利亚BPay类型账户
   * 4：VCC类型账户
   */
  type: number,
  /**
   * crn_code类型，当type=2/3时该字段有一意义
   * 1：用户入账的crnno=studentid
   * 2：用户入账的crnno=某个规则得到
   */
  crn_type: number
}

// 院校信息
export interface SchoolInfo {
  /**
   * 学校英文名
   */
  school_name_en: string;
}
