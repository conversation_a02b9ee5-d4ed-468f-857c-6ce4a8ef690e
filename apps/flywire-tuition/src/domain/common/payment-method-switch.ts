import { isDateValid } from '@/adapters/time';
import { PayType } from '@/api/pay/type';
import { commonModal } from '@/adapters/uni-api-common';

export interface PaymentMethodSwitch {
  /**
   * 禁用提示文案
   */
  tips: string;
  /**
   * 按钮文字
   */
  buttonText: string;
  /**
   * 开始时间
   */
  startTime: string;
  /**
   * 结束时间
   */
  endTime: string;
}

export function getPaymentMethodSwitchConfig(): PaymentMethodSwitch {
  return {
    tips: '受国庆假期影响，北京时间9月28日17:00-10月6日23:59银行转账功能暂不可用，10月7日0:00后正常开放，入账时间将延迟至10月8日20:30，请提前规划缴费时间。给您带来不便，敬请谅解。',
    buttonText: '我知道了',
    startTime: '2023/09/28 17:00:00',
    endTime: '2023/10/06 23:59:59',
  };
}

export function usePaymentMethodSwitch() {
  const config = getPaymentMethodSwitchConfig();
  /**
   * 检查当前支付方式是否已禁用
   * @param payType
   */
  function checkPayTypeIsDisable(payType: PayType) {
    console.log('checkPayTypeIsDisable', config.startTime, config.endTime, payType);
    // 是否在禁用时间内且选择的支付方式为银行转账
    return isDateValid(config.startTime, config.endTime)
           && payType === PayType.BIGPAY;
  }

  function showTips(onClick?: () => void) {
    commonModal({
      content: config.tips,
      confirmColor: '#2E7BD9',
      confirmText: config.buttonText,
      success() {
        onClick?.();
      },
    });
  }

  return {
    checkPayTypeIsDisable,
    showTips,
  };
}
