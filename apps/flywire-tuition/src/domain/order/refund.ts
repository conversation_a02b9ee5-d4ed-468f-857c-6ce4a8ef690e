import { DetailQryResp } from '@/api/order/order';
// import { fenToRMBText, fenToYuan } from '@/adapters/currency';
import { checkIsRefunding, checkIsRefundProcess } from './state';

/**
 * 是否全额退款
 * @param record
 * @returns boolean
 */
export function checkIsFullRefund(data: Pick<DetailQryResp, 'pay_amt'|'refund_amt'>): boolean {
  return data.pay_amt === data.refund_amt;
}


/**
 * 获取退款金额文案描述
 * 区分退款状态，是否全退款情景
 * @param data
 * @returns
 */
export function getRefundText(data: DetailQryResp): string {
  let str = '';
  if (!checkIsRefundProcess(data.list_state)) return str;
  const isRefunding = checkIsRefunding(data.list_state);
  // 飞汇场景下掉手续费逻辑
  // // 退款外币
  // const backFcAmtText = fenToYuan({
  //   amount: data.back_fc_amt,
  //   pointNum: +data.currency_point_num,
  //   symbol: data.currency_symbol,
  // });
  // // 退款人民币
  // const refundRMBText = fenToRMBText(data.refund_fee);
  if (checkIsFullRefund(data)) {
    str = isRefunding ? '将全额原路退还' : '';
  } else {
    // 飞汇场景下掉手续费逻辑
    // str = `${isRefunding ? '将' : ''}以支付汇率退还 ${backFcAmtText} 手续费 ${refundRMBText}`;
  }
  return str;
}
