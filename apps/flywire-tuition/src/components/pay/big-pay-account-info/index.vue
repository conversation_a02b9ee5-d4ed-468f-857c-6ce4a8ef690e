<!-- 银行转账账号列表组件 -->
<template>
  <view
    v-if="bigPayInfoData"
    class="bg-bg-white p-32rpx b-rd-16rpx w-686rpx box-border relative pb-86rpx container pt-52rpx"
  >
    <template
      v-for="item in bigPayInfoData"
      :key="item.label"
    >
      <view
        class="mb-48rpx"
      >
        <view class="flex items-start ">
          <!-- 左侧标签 -->
          <view
            class="w-166rpx color-text-primary"
            :class="item.isAmt?['text5-sem', 'color-text-title', 'lh-48rpx']:['text5-reg', 'color-text5-reg']"
          >
            {{ item?.label }}
          </view>
          <!-- 中间内容 -->
          <view
            class="color-text-title"
            :class="item.isAmt?'number5':'text5-reg'"
            @click="onAreaTap(item)"
          >
            <view>{{ item?.value }}</view>
          </view>
          <!-- 右侧复制按钮 -->
          <view
            class="bg-bg-grey b-rd-4rpx caption1-reg ml-auto pl-13rpx pr-13rpx pt-4rpx pb-4rpx color-primary-normal"
            @click="onCopyTap(item)"
          >
            复制
          </view>
        </view>
        <view class="caption1-reg color-state-alert pl-165rpx mt-8rpx">
          {{ (item?.strongTips) }}
        </view>
      </view>
    </template>
    <!-- 下方背书说明 -->
    <view class="flex flex-row items-center bg-bg-grey w-686rpx h-86rpx absolute bottom-0 left-0 justify-center tips">
      <p-image
        width="36rpx"
        height="36rpx"
        src="https://weremit-static.tenpay.com/flywire-tuition/common/safety-2x.png"
      />
      <text class="color-text-auxiliary caption1-reg ml-16rpx">
        腾讯官方收款账户，受央行监管，资金安全有保障
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { BigPayAccountInfo } from '@/domain/order/bank-config';
import { biClick } from '@tencent/fit-bi-sdk';


type Props = {
  bigPayInfoData: BigPayAccountInfo[] | null;
};

withDefaults(defineProps<Props>(), {
  bigPayInfoData: null,
});

const onCopyTap = (item: BigPayAccountInfo) => {
  uni.setClipboardData({
    data: item.source ? item.source : item.value,
  });
  biClick('transferinfo_detail.copy_button', {
    label: item.label,
  });
};

const onAreaTap = (item) => {
  uni.setClipboardData({
    data: item.source ? item.source : item.value,
  });
  biClick('transferinfo_detail.area', {
    label: item.label,
  });
};

</script>

<style scoped lang="scss">
.container{
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.06);
}
.tips{
  border-radius: 0 0 16rpx 16rpx;
}
</style>

