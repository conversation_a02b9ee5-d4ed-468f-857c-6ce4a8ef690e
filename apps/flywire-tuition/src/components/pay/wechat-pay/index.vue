<!-- 微信支付组件 -->
<template>
  <payTypeItem
    :detail="detail"
    :is-selected="isSelected"
    :is-recommend="isRecommend"
  />
</template>

<script setup lang="ts">
import { IPayTypeItem } from '@/domain/pay';
import payTypeItem from '../pay-type-item/index.vue';


type Props = {
  detail: IPayTypeItem|null;
  isSelected: boolean;  // 是否选中
  isRecommend: boolean; // 是否推荐
};

withDefaults(defineProps<Props>(), {
  detail: null,
  isSelected: false,
  isRecommend: false,
});


</script>

<style scoped lang="scss">

</style>
