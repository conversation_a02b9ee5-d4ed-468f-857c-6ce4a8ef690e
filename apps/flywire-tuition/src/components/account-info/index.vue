<!-- 缴费院校/账号展示 -->
<template>
  <view
    v-if="accountInfo"
    class="color-text-title flex flex-col"
  >
    <!-- 缴费金额 -->
    <text class="text4-sem mb-20rpx">
      缴费金额
    </text>
    <text class="number3 color-text-title">
      {{ tuitionAmt }}
    </text>
    <!-- 学校名称 -->
    <text class="text4-sem mb-20rpx mt-48rpx">
      {{ runtimeConfig.pages.order.receiverTitleText }}
    </text>
    <text class="title3-sem">
      {{ accountInfo?.school_name_en }}
    </text>
  </view>
</template>

<script setup lang="ts">
import { IAccountInfoItem } from '@/domain/order/account-info';
import { useRuntimeConfig } from '@/app-config/useRuntimeConfig';
const { runtimeConfig } = useRuntimeConfig();

type Props = {
  accountInfo: IAccountInfoItem | null;
  tuitionAmt: string;
};

withDefaults(defineProps<Props>(), {
  accountInfo: null,
  tuitionAmt: '',
});

</script>

<style scoped lang="scss">

</style>
