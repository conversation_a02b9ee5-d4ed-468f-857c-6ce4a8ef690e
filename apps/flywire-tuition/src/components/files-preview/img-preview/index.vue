<!-- 图片预览组件 -->
<template>
  <BasePreview
    :status="status"
    @preview="preview"
    @retry="retry"
  >
    <p-image
      v-if="src"
      width="120rpx"
      height="160rpx"
      radius="8rpx"
      :src="src"
      mode="aspectFill"
    />
  </BasePreview>
</template>

<script setup lang="ts">
import BasePreview from '../base-preview/index.vue';
import { FileState } from '../types';

type Props = {
  // 预览资源
  src: string;
  // 当前文件状态
  status: FileState,
};

withDefaults(defineProps<Props>(), {
  src: '',
  status: FileState.downloading,
});

const emit = defineEmits<(e: 'preview'|'retry') => void>();

const preview = () => {
  emit('preview');
};

const retry = () => {
  emit('retry');
};
</script>

<style scoped lang="scss">

</style>
