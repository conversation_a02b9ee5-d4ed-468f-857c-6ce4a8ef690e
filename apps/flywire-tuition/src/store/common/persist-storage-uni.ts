import { EXPIRE_TIME_KEY, PersistedStateExpireTime, PersistedStateKey, PERSISTED_STATE_FOREVER_EXPIRE_TIME } from './storage-key';
import { StorageLike, PersistedStateOptions } from 'pinia-plugin-persistedstate';
import { piniaStorageInstance } from '@/adapters/storage';


/**
 * 修复uniapp下的pinia-plugin-persistedstate持久化存储介质
 */
export const storage: StorageLike = {
  getItem(getKey: PersistedStateKey) {
    const { key } = getExpireTime(getKey);
    return piniaStorageInstance.getItem(key);
  },
  setItem(setKey: PersistedStateKey, value) {
    const { key, expireTime } = getExpireTime(setKey);
    if (typeof expireTime === 'number') {
      piniaStorageInstance.setItem(key, value, expireTime);
      return;
    }
    piniaStorageInstance.setItem(key, value);
  },
};

/**
 * pinia-plugin-persistedstate插件的options类型
 */
export interface PersistedStateOptionsOverride extends PersistedStateOptions {
  key: PersistedStateKey;
  /**
   * 过期时间，单位ms
   */
  expire?: PersistedStateExpireTime;
}

/**
 * pinia-plugin-persistedstate插件的配置类型
 */
export type PersistConfig = boolean | PersistedStateOptionsOverride | PersistedStateOptionsOverride[];

/**
 * 判断配置是否为PersistedStateOptionsOverride的类型守卫
 * @param config
 * @returns
 */
function isPersistedStateOptionsObject(config: PersistConfig): config is PersistedStateOptionsOverride {
  return typeof config === 'object' && !Array.isArray(config);
}

/**
 * 判断配置是否为PersistedStateOptionsOverride[]的类型守卫
 * @param config
 * @returns
 */
function isPersistedStateOptionsArray(config: PersistConfig): config is PersistedStateOptionsOverride[] {
  return typeof config === 'object' && Array.isArray(config);
}

export function formatExpireTimeToString(expireTime?: PersistedStateExpireTime) {
  // 默认永久缓存
  if (!expireTime) {
    return `{{${EXPIRE_TIME_KEY}=${PERSISTED_STATE_FOREVER_EXPIRE_TIME}}}`;
  }
  return `{{${EXPIRE_TIME_KEY}=${expireTime}}}`;
}

export function getExpireTime(storageKey: string) {
  const result: Record<typeof EXPIRE_TIME_KEY, PersistedStateExpireTime> = {
    expireTime: 'PERSISTED_STATE_FOREVER_EXPIRE_TIME',
  };
  const matchArray = storageKey.match(/{{(.*?)}}/g);
  let key = storageKey;
  if (matchArray) {
    key = storageKey.replace(matchArray.join(''), '');
    matchArray.reduce((pre, currentValue) => {
      const [key, value] = currentValue.match(/{{(.*?)}}/)?.[1].split('=') || ['', ''];
      Object.assign(pre, {
        [key]: value,
      });
      return pre;
    }, result);
  }

  return {
    expireTime: result[EXPIRE_TIME_KEY] === 'PERSISTED_STATE_FOREVER_EXPIRE_TIME' ? undefined : result[EXPIRE_TIME_KEY],
    key: key as PersistedStateKey,
  };
}

/**
 * 创建pinia-plugin-persistedstate的配置（兼容uniapp）
 * @param config
 * @returns
 */
// eslint-disable-next-line max-len
export const createPersistConfig = (config: PersistConfig): boolean | PersistedStateOptions | PersistedStateOptions[] => {
  if (isPersistedStateOptionsObject(config)) {
    Object.assign(config, {
      key: `${config.key}${formatExpireTimeToString(config.expire)}`,
      storage,
    });
  }
  if (isPersistedStateOptionsArray(config)) {
    config.forEach((item) => {
      Object.assign(item, {
        key: `${item.key}${formatExpireTimeToString(item.expire)}`,
        storage,
      });
    });
  }
  return config;
};
