<template>
  <template v-if="info.type === OrderInfoType.AMOUNT_BOLD">
    <view class="flex justify-between items-center">
      <view
        class="color-text-title text5-sem order-item-label"
      >
        {{ info.label }}
      </view>
      <text
        class="w-534rpx text-right color-text-title number5"
        selectable="true"
        user-select="true"
      >
        {{ info.value }}
      </text>
    </view>
  </template>
  <!-- 默认直接显示文本类型 -->
  <template v-else>
    <view class="flex justify-between">
      <view
        class="color-text-title text5-reg order-item-label"
      >
        {{ info.label }}
      </view>
      <text
        class="w-534rpx text5-reg text-right color-text-primary"
        selectable="true"
        user-select="true"
      >
        {{ info.value }}
      </text>
    </view>
  </template>
</template>

<script setup lang="ts">
import { InfoItem, OrderInfoType } from './types';

type Props = {
  /**
   * 数据
   */
  info: InfoItem;
};

withDefaults(defineProps<Props>(), {
});

</script>

<style scoped lang="scss">

</style>
