// 文件类型，一组文件的上传标识
export enum FileMaterialsKey {
  INCOME_PROOF = 'INCOME_PROOF',
}

// // 文件状态枚举
// export enum FileState {
//   downloading = 'downloading',
//   uploading = 'uploading', // 加载中
//   success = 'success', // 成功
//   down_fail = 'down_fail', // 下载失败
//   upload_fail = 'upload_fail', // 上传失败
// }

// // 文件项数据结构
// export interface FileItem {
//   status: FileState; // 文件状态
//   indexes: number; // 文件索引，使用时间戳作为索引
//   src: string; // 本地图片临时路径
//   file_id: string; // 已上传成功的文件
// }

// // 上传文件结果
// export interface UploaderResult {
//   files: FileItem[];
//   filesId: string[];
//   success: boolean;
//   [propName: string]: any;
// }


