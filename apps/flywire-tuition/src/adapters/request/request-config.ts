
import { DEFAULT_CONFIG_BASE_URL } from './default-config';
import {
  RequestOptions,
  Request,
} from '@tencent/ppd-common/src/request-core';
import {
  UniRequestAdaptor,
  UniRequestConfig,
} from '@tencent/ppd-uni-common/src/request/adaptors';

const configRequestInstance = new Request(new UniRequestAdaptor());
configRequestInstance.setDefaultConfig({
  baseURL: DEFAULT_CONFIG_BASE_URL,
  config: {
    method: 'GET',
    header: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  },
});

/**
 * 请求配置方法
 */
export const request = <Response>(
  options: RequestOptions<UniRequestConfig>,
): Promise<Response> => configRequestInstance.request(options)
    .then(response => response.data as Response) as Promise<Response>;
