import type { Plugin } from 'vue';
import { plugin, setMapPageNameFun  } from '@tencent/fit-bi-sdk';
import report from '../report';
import BI_CONFIG  from '../../../mta-config-global';
import { getCurPageRoute } from '../route';

export const biReportPlugin: Plugin = (app) => {
  const options = {
    ...BI_CONFIG,
    closeCheckExtF: true,
    closePv: { h5: true, mp: true },
    route: { beforeEach: () => {} },
    // #ifdef MP-WEIXIN
    mp: true,
    // #endif
    // #ifdef H5
    enableRouteListen: true,
    // #endif
    beforeReport: (reportData) => {
      report.reportEvent({
        key: reportData.key,
        params: reportData.params,
      });
      return reportData;
    },
  };

  plugin.install(app, { ...options });

  /**
   * 通过路由获取页面名
   */
  setMapPageNameFun(() => {
    const route = getCurPageRoute();
    return route.replace('pages/', '').replaceAll('/', '-');
  });
};
