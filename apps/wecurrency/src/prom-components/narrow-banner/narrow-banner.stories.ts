/** 这里编写storybook文档*/

import Banner from './banner.vue';

export default {
  title: '业务组件/Banner',
  component: Banner,
  parameters: {
    design: {
      type: 'figma',
      url: '',
    },
  },
};

// @ts-ignore
const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Banner },
  template: '<Banner v-bind="$props"/>',
});

export const 默认 = Template.bind({});

// 这里写props参数的mock数据
(默认 as any).args = {};

