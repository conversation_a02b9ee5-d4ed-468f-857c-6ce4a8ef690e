import { ref, watch, Ref } from 'vue';
import { useCurrencyData, useInputData, useHistoryData, CurrencyType, currencyInfo, isAuthorizationReady } from './data';
import { RetryMode } from '@/components/retry-container/types';
import { onHide, onShow, onLoad } from '@dcloudio/uni-app';
import { UseStorageKeys, checkCurrency, useStorageInstance } from '@/adapters/storage';
import { CurrencyKey } from '@/components/biz-currency/composition';
import { TagServer } from '@/domain/tags/tagService';
import { IndicativeRateContainerRef } from '../type';
import { Currency } from '@tencent/ppd-common/src/currency';
import {  ReqType } from '@/data-source/rate';
import { TagData } from '@/domain/tags/entities/rateTag';
import { biClick } from '@tencent/fit-bi-sdk';
import { HomePathParam } from '@/adapters/route/switchTab';
import { getPageName } from '@/constant/route';
import { HistoryRateServer } from '@/domain/rate/historyRateService';
import BigNumber from 'bignumber.js';

// 币种切换hook
export function useCurrency(indicativeRateRef: Ref<IndicativeRateContainerRef>) {
  const { query, currencyInfo, queryStatus, currencyRateData } = useCurrencyData();
  // 由于实时汇率有可能不变化，为了给用户反馈，在实时汇率更新是，汇率进行闪动
  // const isFlash = ref(false);
  // 由于可能缓存的币种对与url上的币种对有可能不一样，会造成重复请求，加上标识位，必须等待onload（获取url参数）以后再请求接口
  let isLoaded = false;
  // 标签文案
  const rateTag = ref<TagData>();
  async function updateData() {
    if (!isAuthorizationReady.value) {
      return;
    }
    query().then(async () => {
      // #ifdef MP-WEIXIN
      // 除了微信小程序，其他情况下不展示标签

      if (!currencyRateData.value || currencyRateData.value?.isSameCurrency) {
        return;
      }
      // 缓存当前币种对
      const { srcCurrency } = currencyRateData.value;
      const { destCurrency } = currencyRateData.value;
      // 如果当前标签的币种对与当前选择的币种对不一致，则清空标签
      if (rateTag.value?.src !== currencyInfo.src
        || rateTag.value?.dest !== currencyInfo.dest) {
        rateTag.value = undefined;
      }

      const tagEntity = (await TagServer.getRateTag(
        currencyRateData.value.srcCurrency,
        currencyRateData.value.destCurrency,
        currencyRateData.value,
      ));
      // 如果非当前币种对的标签，则不进行更新，否则才更新
      if (srcCurrency === currencyInfo.src && destCurrency === currencyInfo.dest) {
        rateTag.value = tagEntity?.tagData;
      }
      // #endif
    });
  }
  // 币种变化时，重置实时汇率查询接口
  watch([() => currencyInfo.src, () => currencyInfo.dest, () => isAuthorizationReady.value], async () => {
    // 避免重复请求
    if (isLoaded) {
      updateData();
    }
  }, { deep: true });
  watch([() => queryStatus], () => {
    // 如果拉取了新的汇率，实时汇率闪动一下
    if (queryStatus.value === RetryMode.Success || queryStatus.value === RetryMode.NotAffectFailed) {
      indicativeRateRef.value?.update(queryStatus.value === RetryMode.Success);
    }
  }, { deep: true });
  // 根据url参数来设置页面数据
  onLoad((args) => {
    const params = args as HomePathParam;
    const src = params[CurrencyType.SRC] as CurrencyKey;
    const dest = params[CurrencyType.DEST] as CurrencyKey;
    currencyInfo.src = checkCurrency(src) ? src : currencyInfo.src;
    currencyInfo.dest = checkCurrency(dest) ? dest : currencyInfo.dest;
    updateData();
    isLoaded = true;
  });


  // 币种修改
  function onCurrencyChange(key: CurrencyType, currency: CurrencyKey) {
    currencyInfo[key] = currency;
  }

  async function onCountDown() {
    updateData();
  }

  function onRetry() {
    updateData();
  }

  return {
    queryStatus,
    currencyInfo,
    onCurrencyChange,
    onRetry,
    onCountDown,
    rateTag,
    currencyRateData,
  };
}

// 金额输入hook
export function useInputInfo() {
  const { currencyRateData } = useCurrencyData();
  const { inputData, formatText, reset, maxInputAmount } = useInputData();
  // 当前的focus输入框
  const focusInputKey = ref(CurrencyType.SRC);
  onLoad((args) => {
    const params = args as HomePathParam;
    // 根据参数决定当前输入框
    if (params.destAmount) {
      focusInputKey.value = CurrencyType.DEST;
    }
    // 参数中有金额时，重置输入框数据
    if (params.destAmount || params.srcAmount) {
      // 根据当前激活的输入框，决定使用参数中的哪个属性
      const amountKey = focusInputKey.value === 'src' ? 'srcAmount' : 'destAmount';
      // 如果不是数字，则清空
      if (isNaN(parseFloat(params[amountKey] ?? ''))) {
        inputData[focusInputKey.value].value = '';
      } else {
        // 如果大于最大值，则重置为空
        inputData[focusInputKey.value].value = new BigNumber(params[amountKey] ?? '').comparedTo(maxInputAmount) > 0
          ? '' :  parseFloat(params[amountKey] ?? '').toString() ;
      }
      const relativeKey = focusInputKey.value === CurrencyType.SRC ? CurrencyType.DEST : CurrencyType.SRC;
      // 清空另一个输入框的数据，避免闪动
      inputData[relativeKey].value = '';
      // 重置placeholder
      inputData[focusInputKey.value].placeholder = '100';
      inputData[relativeKey].placeholder = '';
    }
  });
  // 监听输入框变化和汇率变化
  watch(() => [inputData, currencyRateData, currencyInfo.src, currencyInfo.dest], () => {
    initData();
  }, { deep: true });
  function initData() {
    Object.keys(inputData).forEach((key) => {
      // 如果当前不是focus的输入框，则计算数字
      if (key !== focusInputKey.value) {
        // 如果汇率为0，说明没拉取到，则不进行计算
        if (!currencyRateData.value) {
          return;
        }
        // 忽略空计算
        if (inputData[focusInputKey.value].value !== '') {
          inputData[key as CurrencyType].value = formatText(
            inputData[focusInputKey.value].value,
            currencyInfo[key as CurrencyType] as Currency,
          );
        } else {
          inputData[key as CurrencyType].value = '';
        }

        inputData[key as CurrencyType].placeholder = formatText(
          inputData[focusInputKey.value].placeholder,
          currencyInfo[key as CurrencyType] as Currency,
        );
      }
      inputData[key].currency = currencyInfo[key];
    });
  }

  function onFocus(key: CurrencyType) {
    if (key === focusInputKey.value) {
      return;
    }
    focusInputKey.value = key;
    Object.keys(inputData).forEach((key) => {
      reset(key as CurrencyType, key !== focusInputKey.value);
    });
  }
  function onBlur(key: CurrencyType) {
    biClick('amount_input.blur', {
      currencyType: key,
      currency: currencyInfo[key],
      amount: inputData[key].value,
      srcAmount: inputData[CurrencyType.SRC].value,
      destAmount: inputData[CurrencyType.DEST].value,
      srcCurrency: inputData[CurrencyType.SRC].currency,
      destCurrency: inputData[CurrencyType.DEST].currency,
    });
  }
  // 交换币种
  function onSwitch() {
    // 互换币种
    [currencyInfo.dest, currencyInfo.src] = [currencyInfo.src, currencyInfo.dest];
    // 交换当前选中的输入框
    focusInputKey.value = focusInputKey.value === CurrencyType.SRC ? CurrencyType.DEST : CurrencyType.SRC;
    [
      inputData[CurrencyType.DEST],
      inputData[CurrencyType.SRC],
    ] = [
      inputData[CurrencyType.SRC],
      inputData[CurrencyType.DEST],
    ];
    biClick('amount_input.exchange', {
      srcCurrency: currencyInfo.src,
      destCurrency: currencyInfo.dest,
    });
  }
  initData();
  return { inputData, onFocus, onSwitch, onBlur, focusInputKey };
}

// 历史汇率hook
export function useHistoryRate() {
  const { currencyRateData } = useCurrencyData();
  const { xAxisData, yAxisData, intervalTask, reqType,
    updateIndicativeRate, queryStatus, fixedNumber, intervals } = useHistoryData();
  const isShowTooltip = ref(false);
  let srcCurrency = '';
  let destCurrency = '';
  // 切换时间间隔
  async function onIntervalChange(key: ReqType) {
    reqType.value = key;
    biClick('rate_chart.interval', {
      srcCurrency: currencyInfo.src,
      destCurrency: currencyInfo.dest,
      reqType: key,
    });
  }
  function onTriggerTooltip(options: {show: boolean}) {
    isShowTooltip.value = options.show;
  }

  onLoad((args) => {
    const params = args as HomePathParam;
    HistoryRateServer.getAvailableReqType(params).then((val) => {
      onIntervalChange(val.reqType);
      intervalTask.resume();
    });
  });
  onShow(() => {
    intervalTask.resume(false);
    useStorageInstance.setItem(UseStorageKeys.SelectedPage,  getPageName('HOME'));
  });
  onHide(() => {
    intervalTask.pause();
  });
  // 监听币种变更
  watch(() => [reqType], () => {
    // 两个都有数据的时候，才启动定时器
    // if (currencyInfo.rateSrcCurrency && currencyInfo.rateDestCurrency) {
    intervalTask.resume();
    // }
  }, { deep: true });

  // 实时汇率变化时，更新曲线数据
  watch(() => [currencyRateData], () => {
    updateIndicativeRate();
    // 如果买入卖出币种没有变更，不拉取
    if (
      !currencyRateData.value?.checkCurrency(srcCurrency, destCurrency)
    ) {
      intervalTask.resume();
      srcCurrency = currencyRateData.value?.srcCurrency as string;
      destCurrency = currencyRateData.value?.destCurrency as string;
    }
  }, { deep: true });
  // 点击曲线的重试按钮
  async function onRetry() {
    intervalTask.resume();
  }
  return {
    xAxisData,
    yAxisData,
    reqType,
    queryStatus,
    onIntervalChange,
    onRetry,
    onTriggerTooltip,
    isShowTooltip,
    fixedNumber,
    intervals,
  };
}
