
import { ref, Ref, watch, onMounted, nextTick, ComponentInternalInstance } from 'vue';
import { BigNumber } from '@tencent/ppd-common';
import { useClass } from '@/hooks/common/useClass';
import { Events } from './types';
// 当前focus的输入框id
const currentFocusInput = ref<undefined|number>(undefined);
export function useInputFormat({ modelValue, digit = 2, showThousandth = true,
  inputClassStep, max = ref(Number.MAX_SAFE_INTEGER) }: {
  modelValue: Ref<number|string>, digit: number, showThousandth: boolean,
  inputClassStep: Record<number, string>,
  max: Ref<number|string>,
}, instance: ComponentInternalInstance | null, emit: Events) {
  const formatVal = ref(formatNumber(modelValue.value, showThousandth, digit, true, max.value));
  const inputVal = ref(formatNumber(formatVal.value, false, digit, false, max.value));
  const inputClass = ref(getClassName(formatVal.value, inputClassStep));

  // 避免填入的数字与限制条件不一致
  emit('update:modelValue', formatVal.value.replace(/,/g, ''));
  const isFocus = ref(false);
  watch(modelValue, () => {
    if (isFocus.value) {
      formatVal.value = formatNumber(modelValue.value, showThousandth, digit, false, max.value);
    } else {
      formatVal.value = formatNumber(modelValue.value, showThousandth, digit, true, max.value);
    }

    inputClass.value = getClassName(formatVal.value, inputClassStep);
    const value = formatNumber(formatVal.value, false, digit, false, max.value);
    // 强制刷新，不然小程序无法更新
    inputVal.value = `${value} `;

    nextTick().then(() => {
      inputVal.value = value.toString();
    });
  });
  async function onBlur() {
    formatVal.value = formatNumber(formatVal.value, showThousandth, digit, true, max.value);
    inputClass.value = getClassName(formatVal.value, inputClassStep);
    isFocus.value = false;
    emit('blur', inputVal.value);
  }
  async function onInput() {
    // 仅小程序使用这种方式，h5的场景下，会对input输入的字符进行过滤，无法监听到.等字符的输入
    // #ifdef MP-WEIXIN
    const text = formatNumber(inputVal.value, false, digit, false, max.value);
    inputVal.value = text;
    if (text === inputVal.value) {
      inputVal.value = `${text} `;
    }
    nextTick().then(() => {
      inputVal.value = text;
    });
    formatVal.value = formatNumber(text, showThousandth, digit, false, max.value);
    inputClass.value = getClassName(formatVal.value, inputClassStep);

    emit('update:modelValue', formatVal.value.replace(/,/g, ''));
    return {
      value: text,
      cursor: text.length,
    };
    // #endif
  }

  onMounted(() => {
    // tips: uniapp的input组件（元素h5的number输入框）会对字符进行过滤，无法监听到.等字符的输入，所以采用原生h5的方式实现
    // #ifdef H5
    const inputElement = (instance?.refs?.input as any)?.$el?.querySelector?.('input');
    // android下，input事件有时候监听不到键盘输入，所以采用textInput事件
    // 但是textInput只能监听字符输入，对于删除等事件需要keyup事件监听
    inputElement.addEventListener('textInput', (e: any) => {
      if (/[0-9.]/.test(e.data)) {
        let text = formatVal.value;
        text = `${formatVal.value}${e.data}`;
        formatVal.value = formatNumber(text, showThousandth, digit, false, max.value);
        inputClass.value = getClassName(formatVal.value, inputClassStep);
        emit('update:modelValue', formatVal.value.replace(/,/g, ''));
      }
    });
    // android下，对于keyup事件，字符输入会获取到unidentified
    // 所以keyup只处理删除
    // https://github.com/facebook/react/issues/14512
    inputElement.addEventListener('keyup', (e: any) => {
      let text = formatVal.value;
      if (e.key === 'Backspace') {
        text = formatVal.value.substring(0, formatVal.value.length - 1);
        formatVal.value = formatNumber(text, showThousandth, digit, false, max.value);
        inputClass.value = getClassName(formatVal.value, inputClassStep);
        emit('update:modelValue', formatVal.value.replace(/,/g, ''));
      }
    });
    // #endif
  });
  function focus() {
    inputClass.value = getClassName(formatVal.value, inputClassStep);
    const value = +formatNumber(formatVal.value, false, digit, false, max.value);
    formatVal.value = formatNumber(value, showThousandth, digit, false, max.value);
    // 强制更新input，不然小程序无法更新
    inputVal.value = `${value} `;
    // iOS下快速切换输入框的时候，有可能键盘不弹出，但是isFocus已经是true了，就不能在弹出输入框，所以这里需要强制触发下弹出
    if (isFocus.value === true) {
      isFocus.value = false;
    }
    // 设置当前输入框为全局focus的输入框
    currentFocusInput.value = instance?.uid;
    nextTick().then(() => {
      isFocus.value = true;
      inputVal.value = value.toString();
    });
    emit('focus');
  }
  // iOS小程序下，如果键盘弹起过程中，重复在多个输入框中点击，有几率造成blur不调用，造成光标异常，所以这里监听全局focus参数来实时更新
  watch(currentFocusInput, () => {
    // 如果全局focus组件不是当前组件，则blur
    if (currentFocusInput.value !== instance?.uid && isFocus.value === true) {
      onBlur();
    }
    // 如果全局focus组件是当前组件，则focus
    if (currentFocusInput.value === instance?.uid && isFocus.value === false) {
      focus();
    }
  });

  return {
    inputVal,
    formatVal,
    isFocus,
    onBlur,
    onInput,
    inputClass,
    focus,
  };
};

// 金额格式化
export function formatNumber(
  value: string | number, showThousandth = true,
  digit = 2, toFixed = true, max: number|string =  Number.MAX_SAFE_INTEGER,
) {
  if (!value) {
    return '';
  }
  // 自动前置补0
  if (value === '.') {
    return '0.';
  }
  // 去除非数字和小数点
  const originText = (`${value}`).replace(/[^0-9.]/ig, '');
  // 整数部分
  const intPart = originText.split('.')[0];

  // 小数点部分，提取.后的第一组数字
  const digitPart = originText.split('.')?.[1]?.substring(0, digit);
  // 是否需要保留小数
  let finalValue: number | string = intPart;

  if (digitPart !== undefined) {
    finalValue = `${intPart}.${digitPart}`;
  }
  // 如果大于最大数字，则逐步去掉最后一个字符
  while (new BigNumber(finalValue).comparedTo(new BigNumber(max)) > 0) {
    finalValue = new BigNumber(finalValue).toFixed()
      .substring(0, new BigNumber(finalValue).toFixed().length - 1);
    // 避免最后一个是.
    finalValue = new BigNumber(finalValue).toFixed();
  }


  if (showThousandth && toFixed) {
    // 增加千分位和小数位
    finalValue = new BigNumber(finalValue).toFormat(digit);
  } else if (showThousandth) {
    // 仅千分位
    let [destInitPart] = finalValue.split('.');
    const [,destDigitPart] = finalValue.split('.');
    destInitPart = new BigNumber(destInitPart).toFormat();
    if (digitPart !== undefined) {
      finalValue = `${destInitPart}.${destDigitPart ?? ''}`;
    } else {
      finalValue = destInitPart;
    }
  } else if (toFixed) {
    // 小数位
    finalValue = new BigNumber(finalValue).toFixed(digit);
  } else {
    // 不需千分位，也不需fixed小数位
    const [destInitPart, destDigitPart] = finalValue.split('.');
    if (digitPart !== undefined) {
      finalValue = `${destInitPart}.${destDigitPart ?? ''}`;
    } else {
      finalValue = destInitPart;
    }
  }

  // 如果汇率不存在，finalValue 值回为 NaN，需展示为 '--'
  if (finalValue === 'NaN') return '--';

  return finalValue;
}


/**
 * 根据字符串字数，返回对应类名
 * @param str 字符串
 * @param classSteps 类名映射
 * @returns 类名
 */
export function getClassName(str: string | number, classSteps: Record<number, string>) {
  const { className } = useClass(str.toString().replace(/[,.]/g, ''), classSteps);
  return  className.value ?? className;
}
