<template>
  <view style="">
    <view
      class="letter-list-wrap"
      @touchend.stop="e=>emit('indexTouchEnd',e)"
      @touchmove.stop="e=>emit('indexTouchMove',e)"
    >
      <view
        v-for="(item, index) in indexList"
        :key="index"
        :data-letter="item"
        class="letter pr-15rpx pl-15rpx w-20rpx lh-28rpx mt-8rpx mb-8rpx"
        @click="e=>emit('itemClick', e)"
      >
        {{ item }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  indexList: string[]
}>(), {
  indexList: () => [],
});
const emit = defineEmits(['itemClick', 'indexTouchEnd', 'indexTouchMove']);
</script>
