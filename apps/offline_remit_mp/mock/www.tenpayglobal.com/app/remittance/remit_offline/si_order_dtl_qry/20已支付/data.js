const orderInfo = require('../orderInfo.json');

module.exports =  async function (ctx) {
  const { body } = ctx.request;

  orderInfo.data.orderInfo.status = 20;
  return {
    data: {
      data:{
        orderInfo: JSON.stringify(orderInfo.data.orderInfo),
        recptInfo: JSON.stringify(orderInfo.data.recptInfo),
        senderInfo: JSON.stringify(orderInfo.data.senderInfo),
      },
      retcode: '0',
      retmsg: 'ok',
    }
  };
};

