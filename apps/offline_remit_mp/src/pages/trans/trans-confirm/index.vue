<template>
  <g-page
    v-if="formData"
    no-padding
    class="observer-container block bg-white "
    page-class="bg-white"
  >
    <view class="pb-118rpx">
      <view class="px-47rpx">
        <FormTitle
          :title="orderDraft?.orderMode === 'remitAgain' ? '确认重汇订单' : '确认汇款订单'"
        >
          <template #desc>
            <view
              class="inline-flex items-center text-34rpx leading-48rpx text-black/64"
            >
              <store-bar
                show-cn-name
                :show-icon="false"
                prefix="汇款门店:"
                is-show-arrow
              />
            </view>
          </template>
        </FormTitle>
      </view>
      <div class="pt-56rpx">
        <OrderShow
          v-if="formData"
          v-model="formData"
        />
      </div>
      <!-- 汇率变更弹窗 -->
      <RateChangePopup
        v-if="rateChangeData"
        v-model="showRateChangePopup"
        :data="rateChangeData"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
      <p-safe-bottom />
    </view>
    <!-- 门店选择弹窗 -->
    <StorePopup
      v-model="siStore.isShowStorePopup"
      :closeable="!!siStore.curStoreInfo?.storeName"
      is-custom-navigation-bar
    />
    <template #footer>
      <view class="pt-80rpx w-100vwv g-bottom-fixed">
        <view
          class="btn-group flex justify-center"
        >
          <div
            class="flex items-center"
          >
            <biz-button
              class="ml-16rpx"
              type="primary"
              size="large"
              @click="onOrder"
            >
              确认下单
            </biz-button>
          </div>
        </view>
        <p-safe-bottom />
      </view>
    </template>
  </g-page>
</template>
<script setup lang="ts">
import { useForm } from './compositon/useForm';
import OrderShow from '@/components/order-show/index.vue';
import RateChangePopup from './components/rate-change-popup/index.vue';
import FormTitle from '@tencent/ppd-uni-component/packages/form/form-title.vue';

import StoreBar from '@/components/store-bar/index.vue';
import { TransConfirmParams } from './jump';
import { onLoad, onShow } from '@dcloudio/uni-app';
import StorePopup from '@/components/store-info/popup.vue';
import { useSiStore } from '@/store/modules/trans/use-si';
import { useRateStore } from '@/store/modules/rate';
const siStore = useSiStore();
const rateStore = useRateStore();

const {
  formData,
  onOrder,
  rateChangeData,
  showRateChangePopup,
  onConfirm,
  onCancel,
  orderDraft,
} =  useForm();
const options = ref<TransConfirmParams>({});


onShow(() => {
  console.log('yakir rateStore.refreshFeeRate', rateStore.refreshFeeRate);
  // 重新获取新金额的汇率
  if (rateStore.refreshFeeRate) {
    rateStore.refreshFeeRate = false;
    onChangeAmount();
  }
});
// 汇率变更
const onChangeAmount = () => {
  formData.value = {
    ...formData.value,
    tranInfo: {
      ...formData.value?.tranInfo,
      ...rateStore.rateFeeByAmount?.baseData,
    },
  } as any;
};

onLoad((opt) => {
  options.value = opt;
});
</script>

<style scoped lang="scss">

</style>
<style lang="scss">
.btn-group,
:deep(.safe-bottom) {
  background: white !important;
}
.u-button--auxillary {
  color: rgba(0, 0, 0, 0.88);
  background-color: rgba(245,245,245,1);
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  font-weight:600;
  font-size:34rpx;
  line-height:48rpx;
  width:208rpx;
  height:88rpx !important;
  padding: 20rpx 52rpx;
}

.u-button--modify {
  color: rgba(0, 0, 0, 0.88);
  background-color: rgba(245,245,245,1);
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  font-weight:600;
  font-size:34rpx;
  line-height:48rpx;
  width:432rpx;
  height:88rpx !important;
  padding: 20rpx 64rpx;
  border-radius: 8rpx;
}
.title-container{
  @apply text4-sem;
  padding: 64rpx 47rpx 16rpx;
}
.title{
  display:flex;
  padding: 64rpx 0rpx 16rpx;
}
.modify{
  color: theme('token.state.info');
  margin-left: 16rpx;
}
.del{
  text-decoration: line-through;
  color: theme('token.text.auxiliary');
  font-size: 28rpx;
  margin-right: 8rpx;
}
</style>
