<template>
  <g-page
    no-padding
    class="observer-container block h-full"
    page-class="bg-transparent h-full"
  >
    <transition>
      <!-- 非成功态 -->
      <view
        v-if="orderDetail && orderDetail?.orderInfo.status !== EOrderStatus.SUCCESS"
        class="text-center mt-48rpx"
      >
        <div class="flex items-center justify-center w-172rpx h-172rpx rounded-full mx-auto">
          <img
            class="w-172rpx h-172rpx "
            :src="currentStatusConfig.orderResultIcon || currentStatusConfig.icon"
            alt=""
          >
        </div>
        <view class="title3-sem text-48rpx mt-56rpx flex-1">
          {{ currentStatusConfig.orderResultTitle || currentStatusConfig.title }}
        </view>
        <!-- <view class="number1 text-92rpx mt-16rpx">
          {{ orderDetail?.orderInfo.remitAmtShow }}
        </view> -->
        <view class="mt-40rpx text4-reg text-black/64 px-64rpx text-center">
          {{ currentStatusConfig.orderResultDesc || currentStatusConfig.desc }}
        </view>
      </view>

      <!-- 成功态 -->
      <view
        v-else-if="orderDetail && orderDetail?.orderInfo.status === EOrderStatus.SUCCESS"
        class="text-center mt-48rpx"
      >
        <div
          class="flex items-center justify-center gap-16rpx success-header"
        >
          <img
            class="w-48rpx h-48rpx"
            src="https://weremit-static.tenpay.com/upload-static/remit/U1NingJMpYRcfyw4M2vmJe-component179.png"
            alt=""
          >
          <div class="text-34rpx leading-48rpx font-bold text-black/88">
            {{ currentStatusConfig.orderResultTitle }}
          </div>
        </div>
        <view
          class="title3-sem text-48rpx mt-96rpx success-title"
        >
          到账金额（元)
        </view>
        <view
          class="number1 text-92rpx mt-16rpx success-amount"
        >
          {{ orderDetail?.orderInfo.remitAmtShow }}
        </view>
        <view
          class="mt-40rpx text4-reg text-black/64 px-64rpx text-center success-tip"
        >
          {{ currentStatusConfig.orderResultDesc }}
        </view>
      </view>
    </transition>
    <template #footer>
      <view
        v-if="currentStatusConfig.orderResultButtonConfig"
        class="mb-40rpx"
      >
        <biz-button
          type="primary"
          size="large"
          custom-style="width:432rpx;background:rgba(245, 245, 245, 1);margin:auto;border:none"
          @click="currentStatusConfig.orderResultButtonConfig.onClick"
        >
          {{ currentStatusConfig.orderResultButtonConfig.text }}
        </biz-button>
      </view>
      <view>
        <biz-button
          type="secondary"
          custom-style="width:432rpx;background:rgba(245, 245, 245, 1);margin:auto;border:none"
          @click="onBack"
        >
          完成
        </biz-button>
      </view>
    </template>
  </g-page>
</template>
<script setup lang="ts">
// const orderService
import { useOrderDetail } from '../../record/record-detail/composition';
import { EOrderStatus, getStatusConfig } from '@/domain/order/record/entity/base';
import { usePolling, PollingNextStep } from '@/hooks/use-polling';
import { reLaunchToHome } from '@/pages/index/jump';
import { onLoad } from '@dcloudio/uni-app';
import { biBrow } from '@tencent/fit-bi-sdk';

const { fetchOrderDetail, orderDetail } = useOrderDetail();

const currentStatusConfig = computed(() => {
  if (orderDetail.value) {
    return getStatusConfig(orderDetail.value);
  }
  return {
    title: '',
    desc: '',
    icon: '',
    orderResultTitle: '',
    orderResultDesc: '',
    buttonConfig: {
      text: '',
      onClick: () => {},
    },
  };
});

const { startPolling } = usePolling(5000);

onLoad((data) => {
  if (!data.listid) {
    wx.showModal({
      title: '无订单号',
      showCancel: false,
    });
    return;
  }
  wx.showLoading({
    title: '加载中',
  });
  // 开始轮询
  startPolling(async () => {
    const listid = data?.listid as string;
    const orderDetail = await fetchOrderDetail(listid);

    biBrow('order-result.brow', {
      status: String(orderDetail?.orderInfo.status),
      purpose: orderDetail?.orderInfo.purpose,
    });

    wx.hideLoading();

    if (!orderDetail || [
      EOrderStatus.SUCCESS,
      EOrderStatus.FAILED,
      EOrderStatus.CANCELLED,
      EOrderStatus.EXPIRED,
      EOrderStatus.REFUNDED,
    ].includes(orderDetail.orderInfo.status)) {
      return PollingNextStep.Stop;
    }
    return PollingNextStep.Continue;
  });
});

const onBack = () => {
  reLaunchToHome();
};

</script>
