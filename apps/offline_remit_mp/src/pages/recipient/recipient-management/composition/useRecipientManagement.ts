import { onLoad, onShow } from '@dcloudio/uni-app';
import { useRecipientList } from './useList';
import { jumpToRecipientForm } from '@/pages/recipient/recipient-form/jump';
import { EmitEvent, PageFunctionType } from '../jump';
import { useRecipientStore } from '@/store/modules/user/recipient';
import { useTransStore } from '@/store/modules/trans';
import { jumpToTransConfirm } from '@/pages/trans/trans-confirm/jump';
import { getCurPage } from '@tencent/ppd-common/src/route';
import { biBrow, biClick } from '@tencent/fit-bi-sdk';
// import { getCurPage } from '@tencent/weremit-common';
// import { getCurPage } from '@tencent/ppd-uni-common/src/';

export const useRecipientManagement = () => {
  /**
   * 根据列表的数据进行路由
   * 如果列表数据为空，则跳转到收款人表单页
   * 否则停留当前页面
  */
  /**
     * 页面可视标志位
    */
  const pageShow = ref(false);

  const fromPage = ref('');

  let isFirstLoad = true;

  const pageFunctionType = ref<PageFunctionType>(PageFunctionType.RECIPIENT_MANAGEMENT);

  // 收款人列表容器高度
  const recipientPListHeight = ref(0);

  const titleText = computed(() => {
    if (pageFunctionType.value === PageFunctionType.REMITTANCE_FLOW) {
      return '选择收款人';
    }
    return '我的收款人';
  });

  const recipientStore = useRecipientStore();

  const { saveOrderDraft } = useTransStore();

  const {
    list,
    isEmpty,
    // isLoading,
    // isNoMore,
    loadData,
    clear } = useRecipientList({
    recipientPListHeight,
    screen: {
      containerSelector: '#recipient-list-container',
      widgetSelector: '#scroll-list',
    },
  });

  onLoad((data) => {
    let from = '';
    const pages = getCurrentPages();
    // 获取前一页的页面路径
    if (pages.length > 1) {
      from = pages[pages.length - 2]?.route;
    }
    fromPage.value = from;
    biBrow('recipient-management.brow', {
      from,
    });

    pageFunctionType.value = data.pageFunctionType as PageFunctionType;

    (async () => {
      // 首次加载数据
      await loadData();
      isFirstLoad = false;

      /**
    * 处理汇出自动路由
    * !注意：当前页面首次加载没有筛选器逻辑
    * 后续如果新增
   */
      const isRemitAutoRoute = dealWithRemitAutoRoute(pageFunctionType.value);
      if (isRemitAutoRoute) {
        return;
      }

      // 页面显示
      pageShow.value = true;
    })();
  });

  onShow(() => {
    /**
     * 刷新标志位为true代表有新增或删除收款人操作
     * 此时需要刷新列表
    */
    if (
      recipientStore.refreshRecipientListFlag
      && !isFirstLoad
    ) {
      clear();
      loadData();
      recipientStore.refreshRecipientListFlag = false;
    }
  });

  /**
  * 处理汇出自动路由
  * 汇款链路中，如果收款人列表为空，将自动跳转到收款人表单页
 */
  const dealWithRemitAutoRoute = (pageFunctionType: PageFunctionType) => {
    /**
    * 1、options中有草稿id（代表在汇款流程中）
    * 2、收款人list为空
   */
    if (
      pageFunctionType === PageFunctionType.REMITTANCE_FLOW
      && list.value.length === 0
    ) {
      jumpToRecipientForm({
        pageFunctions: ['add'],
        pageScene: 'remittance_flow',
      });
      return true;
    }
    return false;
  };

  /**
   * 点击收款人卡片
  */
  const onCardClick = async (recipientId: string) => {
    biClick('recipient-management.click_card', {
      from: fromPage.value,
    });

    switch (pageFunctionType.value) {
      /**
       * 汇款流程中
       * 点击下一步，跳转汇款确认页
      */
      case PageFunctionType.REMITTANCE_FLOW:{
        await saveOrderDraft({
          recptId: recipientId,
        });
        jumpToTransConfirm();
        return;
      }

      /**
       * 管理状态下
       * 点击下一步，跳转收款人表单管理状态
      */
      case PageFunctionType.RECIPIENT_MANAGEMENT:{
        jumpToRecipientForm({
          recipientId,
          pageFunctions: ['update', 'delete'],
          pageScene: 'recipient_management',
        });
        return;
      }

      /**
       * 选择收款人模式
       * 点击收款人卡片，跳转收款人表单页
      */
      case PageFunctionType.CHOOSE_RECIPIENT:{
        // await saveOrderDraft({
        //   recptId: recipientId,
        // });
        const page = getCurPage();
        const eventChannel = page?.getOpenerEventChannel();
        // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
        eventChannel?.emit(EmitEvent.CHOOSE_RECIPIENT, recipientId);
        /**
         * TODO 想下要不要容错处理
        */
        wx.navigateBack();
        return;
      }
    }
  };

  /**
   * 点击添加收款人按钮
  */
  const onAddRecipientBtnClick = () => {
    jumpToRecipientForm({
      pageFunctions: ['add'],
      pageScene: 'recipient_management',
    });
  };

  return {
    list,
    pageShow,
    titleText,
    recipientPListHeight,
    loadData,
    clear,
    onAddRecipientBtnClick,
    onCardClick,
    isEmpty,
  };
};
