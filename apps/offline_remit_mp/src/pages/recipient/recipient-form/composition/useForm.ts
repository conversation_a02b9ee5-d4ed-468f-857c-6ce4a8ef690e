import { ref, watch, Ref } from 'vue';
import FormList from '@tencent/ppd-uni-component/packages/form/form-list.vue';
import { onLoad } from '@dcloudio/uni-app';
import { biBrow, biClick } from '@tencent/fit-bi-sdk';
import { useThrottleFn } from '@vueuse/shared';
import {
  useRemitToSelfFormConf,
  useRemitToFamilyFormConf,
  useBaseFormConf,
  FormElement,
} from './form-config/index';
import { useRecipientStore } from '@/store/modules/user/recipient';
import { useTransStore } from '@/store/modules/trans';
import { commonModal, hideLoading, showLoading } from '@/adapters/uni-api-common';
import { useWeRemitConnectHook, WeRemitReturnedData } from '@/hooks/mp-connect-hooks/index';
import { to } from '@tencent/ppd-common/src/utils';
import { CustomException } from '@/adapters/report';
import { jumpToTransConfirm } from '@/pages/trans/trans-confirm/jump';
import { JumpToRecipientFormParams, PageFunctions, PageScene } from '../jump';
import NameSelectPopup from '@/components/name-select/index.vue';
import { ReceiveType, Relationship } from '@/domain/user';
import { usePageStayTime } from '@/hooks/common/use-page-stay-time';
import { removeSpace } from '@/adapters/validator/element';

interface BaseFormData {
  relationship?: Relationship,
}

interface DetailFormData{
// 中文姓名
  fullnameCn?: string,

  /**
   * 英文姓名(姓名拼音)
   * 姓名拼音和英文姓名还是两个概念
   * 英文姓名lastName在前，firstName在后
   * 拼音firstName在前，lastName在后
   * 最好改下命名
  */
  _enName?: string,

  // 英文姓氏（对应中文拼音姓）(family_name)
  lastName?: string,

  // 英文名（对应中文拼音名）(given_name)
  firstName?: string,

  // 手机区号
  phoneCode?: string,

  // 手机号（不含区号）
  phoneNumber?: string,

  // 手机号（含区号）
  _phoneNumberWithCode?: string,

  receiveType?: ReceiveType,
  bankCode?: string,
  bankName?: string,
  bankAct?: string,
  // 微汇款openid
  weremitOpenid?: string,
}

// 默认表单数据
const DEFAULT_FORM_DATA: DetailFormData = {
  phoneCode: '+86',
  fullnameCn: '',
  _enName: '',
  lastName: '',
  firstName: '',
  phoneNumber: '',
  _phoneNumberWithCode: '',
};

/* 字段相关工具方法 */
// #region

const getEnName = (lastName = '', firstName = '') => `${lastName} ${firstName}`;

const getPhoneNumberWithCode = (phoneCode = '', phoneNumber = '') => `${phoneCode} ${phoneNumber}`;

const getPhoneNumberAndCode = (phoneNumberWithCode = '') => {
  const [phoneCode, phoneNumber] = phoneNumberWithCode.split(' ');
  return {
    phoneCode,
    phoneNumber,
  };
};

// #endregion

/**
 * 表单相关hook，包括表单校验，表单事件的封装
 * @param remitToSelfFormRef 汇款给本人表单对象
 * @param remitToFamilyFormRef 汇款给他人表单对象
 */
export const useForm = (
  remitToSelfFormRef: Ref<InstanceType<typeof FormList> | null>,
  remitToFamilyFormRef: Ref<InstanceType<typeof FormList> | null>,
  nameSelectRef: Ref<InstanceType<typeof NameSelectPopup> | null>,
) => {
  const {
    stayTime,
  } = usePageStayTime((duration) => {
    biBrow('recipient-form.stay_time', {
      duration,
    });
  });

  const fromPage = ref('');

  // 从缓存里面获取表单数据，将写入缓存的数据删除phone字段

  /**
   * 公共表单数据
  */
  const baseFormData = ref<BaseFormData>({});

  /**
   * 汇款给本人表单数据
  */
  const remitToSelfFormData = ref<DetailFormData>({
    ...DEFAULT_FORM_DATA,
  });

  /**
   * 汇款给他人表单数据
  */
  const remitToFamilyFormData = ref<DetailFormData>({
    ...DEFAULT_FORM_DATA,
  });

  /**
   * 当前的表单数据
  */
  const currentFormData = computed(() => {
    if (baseFormData.value.relationship === Relationship.SELF) {
      return remitToSelfFormData.value;
    }
    return remitToFamilyFormData.value;
  });

  const currentFormRef = computed(() => {
    if (baseFormData.value.relationship === Relationship.SELF) {
      return remitToSelfFormRef.value;
    }
    return remitToFamilyFormRef.value;
  });

  // 手机区号change事件初始化，在onAreaCodeSelectShow事件触发时会赋值
  let areaCodeChange: (value: string) => void;
  const validateResult = ref(false);

  /**
   * 页面功能
  */
  const pageFunctions = ref<PageFunctions[]>([]);

  /**
   * 页面场景
  */
  const pageScene = ref<PageScene>();

  // 编辑场景的收款人id
  const editRecipientId = ref<string | undefined>(undefined);

  // 是否允许添加本人
  const isAllowAddSelf = ref(true);
  const recipientStore = useRecipientStore();
  const transStore = useTransStore();

  /**
   * 用户输入标志位
   * 用户主动输入、历史草稿数据、后台表单数据都算用户输入
  */
  let userInputFlag = false;

  const {
    connectToWeRemit,
  } = useWeRemitConnectHook();

  /**
   * 公共表单配置
  */
  const baseFormConf: Ref<FormElement[]> = ref([]);

  /**
   * 汇款给他人表单配置
  */
  const remitToFamilyFormConf: Ref<FormElement[]> = ref<FormElement[]>([]);

  /**
   * 汇款给本人表单配置
  */
  const remitToSelfFormConf: Ref<FormElement[]> = ref<FormElement[]>([]);

  // 表单是否ready
  const isFormReady = computed(() => validateResult.value);

  // 是否显示区号选择器
  const showAreaCodeSelect = ref(false);

  // 是否显示收款人选择器
  const showRecipientSelectorPopup = ref(false);


  /**
   * 校验表单
   * @param showError 是否页面上显示错误信息
   * @param isValidateDefaultData 是否是校验默认数据，为true时不校验表单数据，避免出现红字提示
   * @returns 是否校验成功
   */
  async function validateForm(showError = true) {
    console.log('【线下汇款】validateForm', showError);
    /**
     * 填入默认数据时，不校验表单数据
    */
    if (!userInputFlag) {
      return Promise.resolve(true);
    }
    return new Promise<boolean>((resolve) => {
      // 避免表单数据未更新
      nextTick(() => {
        currentFormRef.value?.validateForm({
          showError,
        }).then((res) => {
          console.log('validateForm', res);
          // 设置校验结果
          validateResult.value = !!res.result;
          resolve(validateResult.value);
        });
      });
    });
  }

  onLoad((options) => {
    let from = '';
    const pages = getCurrentPages();
    // 获取前两页的页面路径
    if (pages.length > 2) {
      from = pages[pages.length - 3]?.route;
    }
    fromPage.value = from;
    biBrow('recipient-form.brow', {
      from,
    });
    const formattedOptions: JumpToRecipientFormParams = {
      recipientId: options.recipientId,
      pageFunctions: (options as any).pageFunctions.split('_') as PageFunctions[],
      pageScene: options.pageScene as PageScene,
    };

    pageFunctions.value = formattedOptions.pageFunctions;
    pageScene.value = formattedOptions.pageScene;
    const { recipientId } = formattedOptions;
    editRecipientId.value = recipientId;
    baseFormConf.value = useBaseFormConf({
      onReceipientSelectorPopupShow: () => {
        showRecipientSelectorPopup.value = true;
      },
      readonly: !pageFunctions.value.includes('add'),
    });

    /**
     * 汇款给本人表单配置
    */
    remitToSelfFormConf.value = useRemitToSelfFormConf({
      jumpToWeRemit: connectWeRemit,
      formData: remitToSelfFormData,
    });

    /**
     * 汇款给他人表单配置
    */
    remitToFamilyFormConf.value = useRemitToFamilyFormConf({
      onAreaCodeSelectShow: (change) => {
        showAreaCodeSelect.value = true;
        // 中间变量存储 change函数，用于在onAreaCodeChange中调用
        areaCodeChange = change;
      },
      formData: remitToFamilyFormData,
    });

    (async () => {
      /**
     * 新增收款人场景，需要查询是否已添加本人
    */

      /**
       * 页面初始化逻辑
       * 并行两个初始化动作：
       * 1、初始化启用本人选项标志位
       * 2、初始化收款人信息（从草稿填入或者从详情接口获取）
      */
      showLoading();
      const [err] = await to(Promise.all([
        initAllowAddSelf(),
        initCurrentRecipientInfo(),
      ]));
      hideLoading();

      if (err) {
        new CustomException(err, 'initCurrentRecipientInfo_error', '初始化收款人信息失败');
        return;
      }
      /** 初始状态触发校验 */
      validateForm(true);
    })();
  });

  /**
   * 选择收款方式后，更新表单项的显隐
  */
  watch(() => remitToSelfFormData.value.receiveType, (newVal) => {
    console.log('【线下汇款】手动修改表单项的显隐', remitToSelfFormData.value.receiveType);
    /**
     * 手动修改表单项的显隐
    */
    if (!!newVal && baseFormData.value.relationship === Relationship.SELF) {
      const formConfDup = remitToSelfFormConf.value;
      formConfDup.forEach((item) => {
        // eslint-disable-next-line no-param-reassign
        item.hidden = false;
      });
      remitToSelfFormConf.value = formConfDup;
    }
  }, {
    immediate: true,
    deep: true,
  });


  watch(() => baseFormData.value.relationship, (newVal) => {
    if (newVal) {
      biBrow('recipient-form.change_relationship', {
        relationship: newVal,
      });
    }
  });

  /* 页面初始化方法 开始 */
  // #region

  /**
   * 初始化启用本人选项标志位
   * 此方法不会抛错
  */
  const initAllowAddSelf = async () => {
    if (pageFunctions.value.includes('add')) {
      // 查询单条记录，确认是否已添加本人
      /**
       * TODO 入参添加relationship
      */
      const [err, res] = await to(recipientStore.doesExistSelf());
      if (err) {
        new CustomException(err, 'initAllowAddSelf_error', '初始化本人选项标志位失败');
        return;
      }
      if (res) {
        isAllowAddSelf.value = false;
      }
    }
  };

  /**
   * 初始化收款人信息
   * 分两类case：
   * 1、新增场景，基于草稿和默认数据填入
   * 2、编辑场景，基于收款人详情接口填入
   * 若调接口填入收款人失败，此方法将报错
  */
  const initCurrentRecipientInfo = async () => {
    console.log('【线下汇款】initCurrentRecipientInfo', pageFunctions.value, editRecipientId.value);
    // 新增收款人情况，基于草稿填入
    if (pageFunctions.value.includes('add')) {
      const { recipientDraft } = recipientStore;
      // 填充表单数据
      baseFormData.value.relationship = recipientDraft?.relationship;
      const draftFormData: DetailFormData = {
        ...recipientDraft,
        _enName: getEnName(recipientDraft?.lastName, recipientDraft?.firstName),
        _phoneNumberWithCode: getPhoneNumberWithCode(recipientDraft?.phoneCode, recipientDraft?.phoneNumber),
      };

      /**
       * 草稿中有relationship字段，代表草稿有值
       * 基于草稿填入
      */
      if (recipientDraft?.relationship) {
        userInputFlag = true;
        switch (recipientDraft?.relationship) {
          case (Relationship.SELF):
            remitToSelfFormData.value = {
              ...draftFormData,
            };
            break;
          case (Relationship.FAMILY):
            remitToFamilyFormData.value = {
              ...draftFormData,
            };
        }
        return;
      }
      /**
       * 草稿中没有relationship字段，代表草稿无值
       * 基于默认数据填入
      */
      remitToFamilyFormData.value = {
        ...DEFAULT_FORM_DATA,
      };
      return;
    }

    // 修改与管理场景，基于收款人详情接口填入
    if (
      (
        pageFunctions.value.includes('update')
        || pageFunctions.value.includes('delete')
      )
      && editRecipientId.value
    ) {
      const [err, res] = await to(recipientStore.queryRecipientById(editRecipientId.value));
      if (err) {
        throw new CustomException(err, 'queryRecipientDtlInfo_error', '查询收款人详情失败');
      }
      // 填充表单数据
      baseFormData.value.relationship = res?.relationship;

      const qryData: DetailFormData = {
        ...res,
        _enName: getEnName(res?.lastName, res?.firstName),
        _phoneNumberWithCode: getPhoneNumberWithCode(res?.phoneCode, res?.phoneNumber),
      };
      console.log('【线下汇款】initCurrentRecipientInfo', res);
      userInputFlag = true;
      switch (res?.relationship) {
        case Relationship.SELF:
          remitToSelfFormData.value = {
            ...qryData,
          };
          break;
        case Relationship.FAMILY:
          remitToFamilyFormData.value = {
            ...qryData,
          };
      }
    }
  };

  // #endregion
  /* 页面初始化方法 结束 */

  /* 链接微汇款 开始 */
  // #region

  /**
   * 链接微汇款
   * 通过拉起微汇款的方式获取收款人信息
  */
  const connectWeRemit = async () => {
    const [err, res] = await to(connectToWeRemit());
    if (err) {
      new CustomException(err, 'connectToWeRemit_error', '微汇款链接收款失败');
      return;
    }
    console.log('【链接微汇款】UI层接受数据', err, res);
    if (res) {
      biBrow('recipient-form.get_we_remit_info');
      setTimeout(() => {
        commonModal({
          content: '已获取到你的收款信息，将自动填入',
          showCancel: false,
          success: () => {
            biClick('recipient-form.confirm_we_remit_info');
            fillWeRemitReturnData(res);
          },
        });
      }, 300);
    }
  };

  /**
     * 使用微汇款返回的数据填写表单
    */
  const fillWeRemitReturnData = (weRemitReturnedData: WeRemitReturnedData) => {
    const newFormData = {
    } as any;

    // 如果没有 family_name_en 和given_name_en ,则报错，阻断
    if (!weRemitReturnedData.family_name_en || !weRemitReturnedData.given_name_en) {
      new CustomException(null, 'fillWeRemitReturnData_error', '微汇款返回数据中没有 family_name_en 和given_name_en');
      return;
    }

    if (weRemitReturnedData.name_cn) {
      newFormData.fullnameCn = weRemitReturnedData.name_cn;
    }

    if (weRemitReturnedData.family_name_en) {
      newFormData.lastName = weRemitReturnedData.family_name_en;
    }
    if (weRemitReturnedData.given_name_en) {
      // firstname 去掉空格
      newFormData.firstName = removeSpace(weRemitReturnedData.given_name_en);
    }

    if (weRemitReturnedData.name_en) {
      // 优先使用 lastName + firstName
      if (newFormData.lastName && newFormData.firstName) {
        newFormData._enName = `${newFormData.lastName} ${newFormData.firstName}`;
      } else {
        newFormData._enName = weRemitReturnedData.name_en;
      }
    }

    if (weRemitReturnedData.phone_with_code) {
      newFormData._phoneNumberWithCode = weRemitReturnedData.phone_with_code;
      const {
        phoneCode,
        phoneNumber,
      } = getPhoneNumberAndCode(weRemitReturnedData.phone_with_code);
      newFormData.phoneCode = phoneCode;
      newFormData.phoneNumber = phoneNumber;
      console.log('【线下汇款】phone_with_code', phoneCode, phoneNumber);
    }
    if (weRemitReturnedData.account_type) {
      newFormData.receiveType = weRemitReturnedData.account_type.toUpperCase() === 'CFT' ? ReceiveType.WX_BALANCE : ReceiveType.BANK_CARD;
      if (newFormData.receiveType === ReceiveType.WX_BALANCE) {
        newFormData.bankCode = undefined;
        newFormData.bankName = undefined;
        newFormData.bankAct = undefined;
      } else {
        newFormData.bankCode = weRemitReturnedData.account_type?.split('_')[0];
        newFormData.bankName = weRemitReturnedData.bank_name;
        newFormData.bankAct = weRemitReturnedData.bank_account;
      }
    }
    if (weRemitReturnedData.openid) {
      newFormData.weremitOpenid = weRemitReturnedData.openid;
    }
    remitToSelfFormData.value = {
      ...remitToSelfFormData.value,
      ...newFormData,
    };
    onFormChange();
  };

  // #endregion
  /* 链接微汇款 结束 */

  /**
   * 保存表单数据到草稿
  */
  const saveFormDataToDraft = () => {
    if (!pageFunctions.value.includes('add')) {
      return;
    }
    const formData = {
      ...currentFormData.value,
      ...baseFormData.value,
    };
    console.log('【线下汇款】saveFormDataToDraft', formData);

    // 如果有中文名，但是没有英文名则不保存中文名
    if (formData.fullnameCn && (!formData.lastName || !formData.firstName)) {
      formData.fullnameCn = '';
    }

    // 如果没有中文名，则清空掉英文名
    if (!formData.fullnameCn) {
      formData.lastName = '';
      formData.firstName = '';
      formData._enName = '';
    }

    recipientStore.saveRecipientDraft(formData);
  };

  const onFormMounted = () => {
    console.log('【线下汇款】表单挂载');
    validateForm(true);
  };

  /**
   * 数据变化也触发校验，但错误不显示在页面上，用于决定是否将按钮设置为可点击
   */
  function onFormChange() {
    userInputFlag = true;
    /**
     * 保存草稿
    */
    saveFormDataToDraft();
    validateForm(true);
  }

  /**
   * 汇款给他人表单触发事件
  */
  const onRemitToFamilyFormTriggerEvent = async (data: {
    event: string,
    payload: {
      key: string,
      val: unknown
    }
  }) => {
    console.log('【线下汇款】onRemitToFamilyFormTriggerEvent', data);
    // 中文名change
    if (data.event === 'blur' && data.payload.key === 'fullnameCn') {
      const res = await remitToFamilyFormRef.value?.validateForm({
        triggerKey: data.payload.key,
        key: [data.payload.key],
        showError: true,
      });
      if (res?.result) {
        const result = await nameSelectRef.value?.trigger(data.payload.val as string);
        console.log('【线下汇款】onRemitToFamilyFormTriggerEvent', result);
        if (result) {
          remitToFamilyFormData.value = {
            ...remitToFamilyFormData.value,
            firstName: result.firstName,
            lastName: result.lastName,
            _enName: getEnName(result.lastName, result.firstName),
          };
          // 保存草稿
          saveFormDataToDraft();
        }
      }
    }
  };

  /* 表单联动组件相关事件 开始 */
  // #region

  const onRemitToItemClick = (relationship: Relationship) => {
    biClick('recipient-form.click_relationship', {
      relationship,
      from: fromPage.value,
    });
    baseFormData.value.relationship = relationship;
    saveFormDataToDraft();
  };

  /**
   * 区号选择器change事件
  */
  function onAreaCodeChange(newArea) {
    areaCodeChange?.(newArea);
  }

  /**
   * 收款人选择器change事件
  */
  function onRecipientSelectorPopupChange(newVal: Relationship) {
    baseFormData.value.relationship = newVal;
  }

  const onWeRemitAction = useThrottleFn(() => {
    biClick('recipient-form.click_receive_type');
    console.log('【线下汇款】onWeRemitAction');
    connectWeRemit();
  }, 1000);

  // #endregion
  /* 表单联动组件相关事件 结束 */


  /* 表单功能 开始 */
  // #region

  /**
   * 提交表单
   */
  async function onSubmit() {
    biClick('recipient-form.submit_button', {
      relationship: String(baseFormData.value.relationship),
      stayTime: new Date().getTime() - stayTime.value,
    });
    showLoading();
    console.log('【线下汇款】onSubmitonSubmitonSubmit', currentFormData.value, currentFormData.value?.weremitOpenid);
    // 创建/更新收款人
    const [err, recptId] = await to(recipientStore.createRecipient({
      recptInfo: getRecipientInfo(),
    }).finally(() => {
      hideLoading();
    }));

    if (err) {
      new CustomException(err, 'createRecipient_error', '创建收款人失败');
      return;
    }

    uni.showToast({
      title: '已保存',
    });

    if (pageScene.value === 'remittance_flow') {
    // 则跳转到汇款卡片页
      setTimeout(() => {
        transStore.saveOrderDraft({
          recptId,
        });
        jumpToTransConfirm();
      }, 500);
    } else {
      wx.navigateBack({});
    }
  }

  const getRecipientInfo = () => ({
    relationship: baseFormData.value.relationship as Relationship,
    recptId: editRecipientId.value,
    lastName: currentFormData.value.lastName as string,
    firstName: currentFormData.value.firstName as string,
    fullnameCn: currentFormData.value.fullnameCn as string,
    phoneCode: currentFormData.value.phoneCode as string,
    phoneNumber: currentFormData.value.phoneNumber as string,

    receiveType: currentFormData.value.receiveType as ReceiveType,
    bankCode: currentFormData.value.bankCode as string,
    bankName: currentFormData.value.bankName as string,
    bankAct: currentFormData.value.bankAct as string,
    recptOpenid: currentFormData.value?.weremitOpenid,
  });

  /**
   * 保存收款人信息
   */
  async function onSave() {
    showLoading();
    await recipientStore.updateRecipientInfo({
      recptInfo: getRecipientInfo(),
    }).finally(() => {
      hideLoading();
    });

    /**
     * 保存草稿
     * 依赖收款人信息唯一key结论
    */
    saveFormDataToDraft();


    uni.showToast({
      title: '已保存',
    });
    wx.navigateBack({});
  }

  /**
   * 删除收款人
  */
  async function onDelete() {
    if (!editRecipientId.value) {
      new CustomException(null, 'deleteRecipient_withoutId', '删除收款人时无收款人id');
      return;
    }
    await recipientStore.deleteRecipient({
      recptInfo: getRecipientInfo(),
    });
    wx.navigateBack({});
  }

  // #endregion
  /* 表单功能 结束 */

  return {
    pageFunctions,

    baseFormConf,
    remitToFamilyFormConf,
    remitToSelfFormConf,

    baseFormData,
    remitToSelfFormData,
    remitToFamilyFormData,

    showAreaCodeSelect,
    showRecipientSelectorPopup,

    isFormReady,
    isAllowAddSelf,

    editRecipientId,

    onFormMounted,
    onFormChange,
    onRemitToItemClick,
    onAreaCodeChange,
    onWeRemitAction,
    onRecipientSelectorPopupChange,
    onRemitToFamilyFormTriggerEvent,

    onSubmit,
    onSave,
    onDelete,
  };
};
