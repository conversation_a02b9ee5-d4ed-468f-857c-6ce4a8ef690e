

declare interface AppShareData{
  /**
   * 分享标题
   */
  title: string,
  /**
   * 分享的key，如果path中不带adtag，会自动将key带到分享path的adtag参数中
   */
  key: string,
  /**
   * 图片路径，默认会分享当前页面截图
   */
  imageUrl?: string,
  /**
   * 图片路径，默认会分享当前页面路径
   */
  path?: string,
}
declare interface ShareAppMessageOption {
  from: 'button' | 'menu' | string;
  target: any;
  webViewUrl?: string;
}
declare namespace Page {
  export interface PageInstance {
    $page: { fullPath: string; [propName: string]: any }
  }
}
declare namespace UniApp {
  interface Uni {
    /**
     * uview工具链
     */
    $u: {
      /**
       * uview配置
       */
      config: {
        /**
         * 单位
         */
        unit: 'rpx' | 'rem' | 'px';
      },
      /**
       * 格式化时间
       * @param time 任何合法的时间格式、秒或毫秒的时间戳
       * @param format 时间格式，可选。默认为yyyy-mm-dd，年为"yyyy"，月为"mm"，日为"dd"，时为"hh"，分为"MM"，秒为"ss"，格式可以自由搭配，
       * 如： yyyy:mm:dd，yyyy-mm-dd，yyyy年mm月dd日，yyyy年mm月dd日 hh时MM分ss秒，yyyy/mm/dd/，MM:ss等组合
       */
      timeFormat: (time: string, format: string) => string;
      props: {
        loadingIcon: {
          gradientColor: string
        }
      }
    }
  }
}
declare namespace WechatMiniprogram {
  import { GlobalData } from './adapters/global-data';
  interface Wx {
    $$wecurrency_global: GlobalData
    onAppRoute: Function
  }
}
