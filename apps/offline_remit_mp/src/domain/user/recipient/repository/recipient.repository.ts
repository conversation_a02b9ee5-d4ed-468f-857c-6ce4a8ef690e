import { Service } from 'typedi';
import { request } from '@/adapters/request';
import type { BaseResponse } from '@/api/base/type';
import { RecipientDetail, RecipientInfo } from '../entity/recipient.entity';
import { BaseRepository, TargetType } from '@tencent/ppd-uni-domain/src/base/repository/base.repository';


export enum RecipientInfoOpt {
  Add = 1,
  Update = 2,
  Delete = 3,
}

export enum Relationship {
  SELF = 'Self',
  FAMILY = 'Family',
}

export enum ReceiveType {
  // 微信零钱
  WX_BALANCE = 1,
  // 银行卡
  BANK_CARD = 2,
}


/**
 * 收款人信息登记请求参数
 */
export type RecipientInfoRegRequest = Omit<RecipientDetail, 'recptInfo' | 'recptId' | 'relationship' | 'country'> & {
  opt: RecipientInfoOpt;
  siMemberId: string;
  siStoreId: string;
  recptInfo?: RecipientInfo;
};

export type RecipientInfoDeleteRequest = Omit<RecipientInfoRegRequest, 'recptInfo'> & {
  recptInfo?: {
    recptId: string;
  }
};

/**
 * 收款人信息登记返回信息
 */
export interface RecipientInfoRegResponse extends BaseResponse {
  data: {
    /**
     * 收款人id
     */
    recptInfo?: RecipientInfo;
  }
}

/**
 * 收款人信息查询请求参数
 */
export interface RecipientInfoQryRequest {
  /**
   * 汇出机构id
   */
  siMemberId: string;
  /**
   * 汇出门店id
   */
  siStoreId: string;
  /**
   * 分页页码（从0开始）
   */
  offset: number;
  /**
   * 分页大小（最大值30）
   */
  limit: number;

  /**
   * 收款人关系
  */
  relationship: string;
}

/**
 * 收款人详情查询接口请求参数
*/
export interface RecipientDtlInfoQryRequest {
  // 收款人id
  recptId: string;
  /**
   * 汇出机构id
   */
  siMemberId: string;
  /**
     * 汇出门店id
     */
  siStoreId: string;
}

/**
 * 收款人信息列表项
*/
export type RecipientInfoListItem = Pick<RecipientInfo,
| 'fullnameCn' | 'firstName' |'lastName'
| 'phoneCode' | 'phoneNumber'
| 'relationship'
>&Required<Pick<RecipientInfo, 'recptId'>> & {
  recptInfo: string;
};

/**
 * 收款人信息查询返回参数
 */
export interface RecipientInfoQryResponse extends BaseResponse {
  /**
   * 收款人信息列表
   */
  data: {
    recptInfoList: RecipientInfoListItem[];
    recptListCount: number;
  }
}

/**
 * 收款人详情查询返回参数
 */
export interface RecipientDtlInfoQryResponse extends BaseResponse {
  data: RecipientDetail;
}

@Service()
export class RecipientRepository extends BaseRepository {
  /**
   * 收款人信息登记
   * @param data
   * @returns
   */
  async recipientInfoReq(data: RecipientInfoRegRequest|RecipientInfoDeleteRequest) {
    if (data.recptInfo) {
      /* eslint-disable no-param-reassign */
      (data.recptInfo as RecipientInfo).country = 'CN';
    }
    const res = await request<RecipientInfoRegResponse>({
      url: '/app/remittance/remit_offline/recipient_info_reg',
      data: {
        ...data,
        recptInfo: JSON.stringify(this.handleFormatObj({
          target: data.recptInfo as unknown as object,
          targetType: TargetType.Snake,
        })),
      },
    });
    res.data.recptInfo = this.handleFormatObj({
      target: this.tryJsonParse(res.data.recptInfo as unknown as string || '{}'),
      targetType: TargetType.Camel,
    }) as RecipientInfo;

    return res;
  }

  /**
   * 收款人信息查询
   * @param data
   * @returns
   */
  async queryRecipientInfo(data: RecipientInfoQryRequest) {
    const res = await request<RecipientInfoQryResponse>({
      url: '/app/remittance/remit_offline/recipient_info_qry',
      data,
    });
    // 解析recptInfoList
    res.data.recptInfoList = res.data.recptInfoList.map((item) => {
      const data =  this.handleFormatObj({
        target: this.tryJsonParse(item.recptInfo || '{}'),
        targetType: TargetType.Camel,
      }) as RecipientInfoListItem;
      return data;
    });
    return res;
  }

  /**
   * 收款人详情查询
   * @param data
   * @returns
   */
  async queryRecipientDtlInfo(data: RecipientDtlInfoQryRequest) {
    const res = await request<RecipientDtlInfoQryResponse>({
      url: '/app/remittance/remit_offline/recipient_dtl_info_qry',
      data,
    });
    // 解析recptInfo
    res.data.recptInfo = this.handleFormatObj({
      target: this.tryJsonParse(res.data.recptInfo as unknown as string || '{}'),
      targetType: TargetType.Camel,
    }) as RecipientInfo;

    return res;
  }
}

