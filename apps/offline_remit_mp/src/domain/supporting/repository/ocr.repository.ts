import { BaseRepository } from '@tencent/ppd-uni-domain';
import { Service } from 'typedi';
import { IVisitPassOCR, OcrEntity } from '../entity/ocr.entity';
import { request } from '@/adapters/request';
import { CustomException } from '@tencent/ppd-uni-common/src/report';


interface IGetOcrResultParams {
  cosPathEn: string;
}

interface IGetOcrResultResponse {
  data: {
    ocrInfo: string;
  };
}

export interface IGetUploadFileUrlParams {
  // 文件类型
  fileType: string;
}

export interface IGetUploadFileUrlResponse {
  data: {
    // 上传文件到COS的url
    uploadUrl: string;
    // 后端签名
    cosPathEn: string;
  };
}

@Service()
export class OcrRepository extends BaseRepository {
  /**
   * 获取OCR结果
   * @param params
   * @returns
   */
  async getOcrResult(params: IGetOcrResultParams) {
    const res = await request<IGetOcrResultResponse>({
      url: '/app/remittance/remit_offline/certificate_ocr_qry',
      data: params,
    });
    try {
      return new OcrEntity(JSON.parse(res.data.ocrInfo).Response as unknown as IVisitPassOCR);
    } catch (error) {
      throw new CustomException(res, 'OCR_RESULT_PARSE_ERROR', '解析OCR结果失败');
    }
  }
  /**
   * 获取上传文件到COS的url
   * @param params
   * @returns
   */
  async getUploadFileUrl(params: IGetUploadFileUrlParams) {
    const res = await request<IGetUploadFileUrlResponse>({
      url: '/app/remittance/remit_offline/get_upload_file_url',
      data: params,
    });
    return res.data;
  }

  /**
   * 上传图片到 COS
   * @param uploadUrl 上传文件到COS的url
   * @param filePath 本地图片路径
   * @returns
   */
  async uploadToCos(uploadUrl: string, filePath: string) {
    return new Promise((resolve, reject) => {
      const fs = wx.getFileSystemManager();
      // 拿到二进制流
      const paylload = fs.readFileSync(filePath);
      /**
       * 这里使用 request 的原因是 uploadFile 不支持PUT请求 非常难受
       */
      wx.request({
        url: uploadUrl,
        method: 'PUT',
        header: {
          'Content-Type': 'multipart/form-data',
        },
        dataType: '其他',
        data: paylload,
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        },
      });
    });
  }
}
