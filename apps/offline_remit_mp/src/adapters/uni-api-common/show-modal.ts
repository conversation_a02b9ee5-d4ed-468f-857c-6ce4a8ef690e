import { to } from '@tencent/ppd-common/src/utils';
import { BusinessExceptions, CommonResponse } from '../request';

let showModalStatus = false;

export const clearShowModalStatus = () => {
  showModalStatus = false;
};
/**
 * 判断是否为接口错误
 * @param err
 * @returns
 */
function isApiError(err?: unknown): err is CommonResponse {
  // 信息可能为undefined
  const apiError = err as CommonResponse | undefined;
  // 错误码
  const retCode = apiError?.retcode;
  // 错误信息
  const retMsg = apiError?.retmsg;
  if (
    // 存在错误码
    retCode
    // 存在错误文案
    && retMsg
    /**
     * 错误码和错误文案不一致，说明错误码转译正常
     * 未转义成功的错误返回：{"retcode":"1610762012","retmsg":"[1610762012]System busy!"}
     */
    && (retMsg.indexOf(retCode) === -1)
  ) {
    return true;
  }
  return false;
}
export const commonModal = (options: UniApp.ShowModalOptions) => {
  if (showModalStatus) {
    return;
  }
  showModalStatus = true;
  uni.showModal({
    ...options,
    success(res) {
      console.log('showModal', showModalStatus, res, options);
      options.success?.(res);
    },
    fail(err) {
      options.fail?.(err);
    },
    complete(res) {
      showModalStatus = false;
      options.complete?.(res);
    },
  });
};
// 错误类型
type ApiError = {
  type: 'business';
  code: string;
  retcode: string;
  retmsg: string;
} | {
  type: 'system';
  errorMsg: string;
};
/**
 * api调用辅助函数，自动处理错误弹窗
 * @param promise 请求promise
 * @param options 配置
 * @returns
 */
export async function toApi<T, U = ApiError>(callback: () => Promise<T>, options?: {
  // 是否可以重试
  canRetry?: boolean,
  // 弹窗配置
  modalConfig?: UniApp.ShowModalOptions,
  // 错误处理，每次callback失败时均会调用
  onError?: (err: U) => void,
  // 是否阻断业务错误
  blockBusinessError?: boolean,
  // 无需阻断的retcode，不传则所有均阻断
  excludeRetcode?: string[],
  // 是否显示弹窗
  erroeMsgMapping?: Record<string, string>,
}): Promise<[U, undefined] | [null, T]> {
  const defaultOptions: Parameters<typeof toApi>[1] = {
    modalConfig: {
      content: '系统繁忙，请稍后重试',
      confirmText: options?.canRetry ? '重试' : '我知道了',
      cancelText: '取消',
      showCancel: !!options?.canRetry,
    },
    blockBusinessError: true,
    excludeRetcode: [],
  };
  const config = {
    ...defaultOptions,
    ...options,
    modalConfig: {
      ...defaultOptions.modalConfig,
      ...options?.modalConfig,
    },
  };

  return new Promise((resolve) => {
    (async () => {
      const [err, res] = await to(callback());
      if (err || !res) {
        // 是否显示弹窗
        let showModal = false;
        // 错误信息，默认为系统错误
        let error: ApiError = {
          type: 'system',
          errorMsg: err?.toString?.() ?? '系统错误',
        };
        // 业务错误
        if (err instanceof BusinessExceptions) {
          error = {
            type: 'business',
            code: err.retcode,
            retcode: err.retcode,
            retmsg: err.retmsg,
          };
          // 如果错误信息为已识别的接口错误，则使用错误信息
          if (isApiError(err)) {
            config.modalConfig.content = options?.modalConfig?.content ?? err.retmsg;
          }
          // 如果有错误文案映射，则使用映射后的错误文案
          if (options?.erroeMsgMapping) {
            const message = options.erroeMsgMapping[error.retcode] ?? config.modalConfig.content;
            config.modalConfig.content = message;
          }
        }
        // 执行错误回调
        options?.onError?.(error as U);
        // 系统错误必然显示弹窗
        if (error.type === 'system') {
          showModal = true;
        }
        // 业务错误，如果需要阻断，则显示弹窗
        if (error.type === 'business' && config.blockBusinessError) {
          showModal = true;
          if (config.excludeRetcode) {
            showModal = !config.excludeRetcode.includes(error.retcode);
          }
        }

        if (showModal) {
          commonModal({
            content: config.modalConfig.content,
            confirmText: config.modalConfig.confirmText,
            showCancel: config.modalConfig.showCancel,
            cancelText: config.modalConfig.cancelText,
            success: (res) => {
              config.modalConfig.success?.(res);
              if (res.confirm && config.canRetry) {
                resolve(toApi(callback, options) as any);
              } else {
                resolve([error as U, res as any]);
              }
            },
            fail: (err) => {
              config.modalConfig.success?.(err);
              resolve([{ type: 'system', error: err } as any, undefined]);
            },
          });
        } else {
          resolve([error as U, res as any]);
        }
      } else {
        resolve([null, res as T]);
      }
    })();
  });
}
