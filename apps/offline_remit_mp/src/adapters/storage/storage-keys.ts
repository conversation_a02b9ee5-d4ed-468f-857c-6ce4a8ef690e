/**
 * useStorage函数使用的缓存key
 */
export enum UseStorageKeys {
  /** 汇款人信息草稿 */
  SenderInfoDraft = 'SenderInfoDraft',
  /** 收款人信息草稿 */
  RecipientInfoDraft = 'RecipientInfoDraft',

}

/**
 * pinia持久化使用缓存key
 */
export enum PersistedStateKey {
  /**
   * 登陆信息的持久化key
   */
  LOGIN_DATA = 'LOGIN_DATA',
  TIME_DIFF = 'TIME_DIFF',
  LAST_REQUEST_TIME = 'LAST_REQUEST_TIME',
  /** 订单信息草稿 */
  OrderDraft = 'OrderDraft',
  /** 门店ID */
  SI_STORE_ID = 'SI_STORE_ID',
  /** 汇款人信息草稿 */
  SenderInfoDraft = 'SenderInfoDraft',
  /** 收款人信息草稿 */
  RecipientInfoDraft = 'RecipientInfoDraft',
  /** 首页信息 */
  HomeInfo = 'HomeInfo',
}


/**
 * 全部缓存key的类型
 */
export enum GlobalStorageKeys {
  NAME = 'NAME'
}
