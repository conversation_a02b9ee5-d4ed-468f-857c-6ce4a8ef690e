import { RecordDetail } from '@/domain/order/record/entity/recordDetail.entity';
import { TranInfo } from '@/domain/trans/entity/tranInfo.entity';
import { RecipientInfo, SenderInfo } from '@/domain/user';

export interface OrderShowData {
  /** 交易信息 */
  tranInfo?: TranInfo;
  /** 订单信息 */
  orderInfo?: RecordDetail;
  /** 汇款人信息 */
  senderInfo: SenderInfo;
  /** 收款人信息 */
  recptInfo: RecipientInfo;
};
