<template>
  <view class="flex items-center">
    <p-popup
      :show="showPop"
      :title="title"
      :duration="duration"
      :closeable="true"
      :safe-area-inset-bottom="false"
      @close="close"
    >
      <AreaCodeSelect
        :index-list="indexList"
        :is-search-empty="!isSearching && searchList.length === 0"
        :is-show-area-code="isShowAreaCode"
        :show="showPop"
        :show-list="showList"
        :value-name="valueName"
        :current-area="currentArea"
        :is-ready="isReady"
        :sub-height="subHeight"
        @search="handleSearch"
        @choose="choose"
      >
        <!-- 搜索结果 start -->
        <template #search>
          <view
            v-for="item in searchList"
            :key="item.key"
            :data-code="item.key"
            class="relative"
            @click="choose(item, SelectArea.Search)"
          >
            <!-- 取名 Area会报错/不显示 -->
            <CountryList
              :area-info="item"
              :is-show-area-code="isShowAreaCode"
            />
            <p-icon
              v-if="currentArea === item[valueName]"
              name="success"
              size="48"
              class="color-primary-normal absolute right-0 top-50% translate-y--50%"
            />
          </view>
        </template>
        <!-- 搜索结果 end -->

        <!-- 热门国家及地区 start -->
        <view
          v-if="(quickSelectList && quickSelectList.length > 0)"
          class="hots pb-32rpx"
        >
          <view
            class="color-text-auxiliary text-28rpx lh-32rpx mb-32rpx"
          >
            快速选择
          </view>
          <view class="hot-wrap flex flex-wrap justify-start gap-x-24rpx">
            <view
              v-for="area in quickSelectList"
              :key="area.key"
              class="mb-16rpx"
              :data-code="area.key"
              @click="choose(area, SelectArea.Quick)"
            >
              <QuickSelect
                :area-info="area"
                :is-show-area-code="isShowAreaCode"
              />
            </view>
            <template v-if="(quickSelectList.length%row > 0)">
              <view
                v-for="item in (3 - quickSelectList.length%row)"
                :key="item"
                class="hot-item"
              />
            </template>
          </view>
        </view>
        <!-- 热门国家及地区 end -->
      </AreaCodeSelect>
    </p-popup>
  </view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import PPopup from '@tencent/fui-component-library/src/components/p-popup/index.vue';
import { useProps } from './composition';
import AreaCodeSelect from '@/components/area-code-select/index.vue';
import CountryList from '@/components/area-code-select/components/area/index.vue';
import QuickSelect from '@/components/area-code-select/components/area/quick-select.vue';
/** 选择的区域 */
enum SelectArea {
  /** 快速选择 */
  Quick = 'quick',
  /** 搜索 */
  Search = 'search',
  /** 默认列表 */
  Default = 'default'
}
const props = defineProps({
  title: {
    type: String,
    default: '国家',
  },
  valueName: {
    type: String,
    default: 'key',
  },
  currentArea: {
    type: String,
    default: '',
  },
  isShowAreaCode: {
    type: Boolean,
    default: false,
  },
  show: {
    type: Boolean,
    default: false,
  },
});
const isReady = ref(false);
const duration = 300;

const emit = defineEmits(['change', 'close']);
const row = 3; // 热门国家一行数量
const {
  title,
  isShowAreaCode,
  isSearching,
  indexList,
  searchList,
  quickSelectList,
  showPop,
  showList,
  subHeight,
  close, initData, choose, handleSearch,
} = useProps(props, emit);

onMounted(() => {
  initData();
});
watch(showPop, () => {
  if (showPop.value) {
    // 避免动画还未执行完
    setTimeout(() => {
      isReady.value = true;
    }, duration + 500);
  }
});

</script>

<style scoped lang="scss">
</style>
