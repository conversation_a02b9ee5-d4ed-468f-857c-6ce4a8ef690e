<template>
  <view
    v-if="storeInfo"
    class="flex-1 flex flex-col"
  >
    <view>
      <view class="text-40rpx leading-56rpx font-bold color-text-title">
        {{ storeInfo?.storeCnName }} {{ storeInfo?.storeName }}
      </view>
      <view>
        <view
          v-for="item in showInfo"
          :key="item.key"
          class="flex text5-reg color-text-primary mt-32rpx"
        >
          <view class="w-152rpx">
            {{ item.label }}
          </view>
          <view class="flex-1">
            {{ item.value }}
          </view>
        </view>
      </view>
      <view
        v-for="url in storeInfo.urlList"
        :key="url"
        class="w-full mt-32rpx"
      >
        <image
          :src="url"
          mode="aspectFill"
          class="object-cover rounded-8rpx w-full h-280rpx"
        />
      </view>
    </view>
    <view
      v-if="isCurStore"
      class="text4-sem color-text-primary text-center mt-96rpx"
      @click="onJumpToOtherStore"
    >
      去其他门店
    </view>
    <view
      v-else
      class="text-center mt-96rpx"
    >
      <biz-button
        type="primary"
        @click="onSelectStore"
      >
        去这家
      </biz-button>
    </view>
  </view>
</template>
<script setup lang="ts">
import { useSiStore } from '@/store/modules/trans/use-si';
import { storeToRefs } from 'pinia';
import { STORE_INFO_LIST } from '@/constant/store-info-list';
const siStore = useSiStore();
const props = defineProps({
  storeId: {
    type: String,
    required: true,
  },
});
const { storeId } = toRefs(props);
const { curStoreInfo } = storeToRefs(siStore);
const storeInfo = computed(() => STORE_INFO_LIST.find(item => item.siStoreId === storeId.value));
const showInfo = computed(() => siStore.getStoreShowInfo(storeId.value ?? ''));
const isCurStore = computed(() => storeId.value === curStoreInfo.value?.siStoreId);
const emit = defineEmits(['selectStore', 'jumpToOtherStore']);
const onSelectStore = () => {
  emit('selectStore', storeId.value);
  siStore.setSiStoreId(storeId.value);
};
const onJumpToOtherStore = () => {
  emit('jumpToOtherStore');
};
</script>

<style lang="scss">
page {
  background-color: #f1f1f1;
}
</style>
<style lang="scss">
</style>
