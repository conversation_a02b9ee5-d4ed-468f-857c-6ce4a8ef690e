import { Emits } from './types';
import { Props } from './index.vue';

export function useProps(props: Props, emit: Emits, componentInstance) {
  const windowTop = ref(0);
  uni.getSystemInfo({
    success(res) {
      windowTop.value = res.windowTop || 0;
    },
  });
  const { indexList } = props;

  const searchValue = ref('');
  const isShowSearch = ref(false);
  const isSearching = ref(false);
  const isShowAreaCode = ref(props.isShowAreaCode);

  const onSearchInput = (e) => {
    const { value } = e.detail;
    searchValue.value = value;
    isSearching.value = true;
    emit('search', { value });
    if (!value) {
      isSearching.value = false;
      return;
    };
    isSearching.value =  false;
  };

  const clearSearchResult = () => {
    emit('search', { value: '' });
    searchValue.value = '';
  };

  const hideSearch = () => {
    clearSearchResult();
    isShowSearch.value = false;
  };

  const onClearSearchValue = () => {
    clearSearchResult();
  };

  watch(() => props.show, (show) => {
    if (!show) {
      hideSearch();
    }
  });

  const onCancelSearch = () => {
    onClearSearchValue();
    setTimeout(() => {
      hideSearch();
    }, 100);
  };

  const toView = ref('');

  /**
   * 索引 toast 计时器
   */
  let toastTimer: NodeJS.Timeout | null = null;
  /**
   * 隐藏索引 toast
   */
  const hideToast = () => {
    if (toastTimer) clearTimeout(toastTimer);
    toastTimer = setTimeout(() => {
      toView.value = '';
    }, 1000);
  };

  /**
   * 处理索引点击事件
   */
  const onClickLetter = (e) => {
    toView.value = e.currentTarget.dataset.letter;
    setTimeout(() => {
      hideToast();
    }, 500);
  };

  /**
   * 索引列表
   */
  const letterBox = ref(null);
  /**
   * 索引列表顶部坐标
   */
  const lettersTop = ref(0);
  /**
   * 索引列表高度
   */
  const lettersHeight = ref(0);
  /**
   * 索引每个高度
   */
  const letterLineHeight = ref(0);
  /**
   * 索引列表长度
   */
  const len = ref(indexList.length);
  /**
   * 初始化，计算索引各项数据（位置信息）
   */
  const initLetterData = () => {
    const query = uni.createSelectorQuery().in(componentInstance);
    query.select('#letterBox').boundingClientRect((data) => {
      if (!data) {
        return;
      }
      lettersHeight.value = data.height || 0;
      lettersTop.value = data.top || 0;
      letterLineHeight.value = lettersHeight.value / (len.value || 1);
      if (lettersTop.value <= 0) {
        emit('error', {
          key: 'initLetterData_error',
          error: {
            lettersTop: lettersTop.value,
            lettersHeight: lettersHeight.value,
            windowTop: windowTop.value,
          },
        });
        return;
      }
    })
      .exec();
  };

  /**
   * 处理索引列表拖动事件
   */
  const onLetterMove = (e) => {
    if (lettersTop.value <= 0) return;
    const y = e.touches[0].clientY;
    if (y > lettersTop.value && y < lettersHeight.value + lettersTop.value) {
      const index = Math.floor((y - lettersTop.value) / letterLineHeight.value);
      const letter = indexList[index];
      if (letter) {
        toView.value = letter;
      }
    }
  };

  /**
   * 处理 touchend 事件，隐藏索引 toast
   */
  const onLetterEnd = () => {
    setTimeout(() => {
      hideToast();
    }, 500);
  };

  /**
   * 记录滚动高度
   */
  const scrollTop = ref(0);
  const onScroll = (e) => {
    if (e.detail.scrollTop === 0 || scrollTop.value === 0) {
      scrollTop.value = e.detail.scrollTop;
    }
  };

  return {
    isShowAreaCode,
    isSearching,
    isShowSearch,
    searchValue,
    toView,
    letterBox,
    scrollTop,
    initLetterData,
    onClickLetter,
    onLetterMove,
    onLetterEnd,
    onSearchInput,
    onClearSearchValue,
    onCancelSearch,
    onScroll,
  };
}
