// eslint-disable-next-line @typescript-eslint/no-require-imports, @typescript-eslint/no-var-requires
const automator = require('miniprogram-automator');
const JEST_TIMEOUT = 1000000;

describe('api', () => {
  let miniProgram;
  let page;

  beforeAll(async () => {
    miniProgram = await automator.launch({
      cliPath: 'D:/software/weixindevtool/微信web开发者工具/cli.bat',
      projectPath: 'E:/project/GITPORJECT/mp_standard_frame_global/src',
    });
  }, JEST_TIMEOUT);

  afterAll(async (done) => {
    // await miniProgram.close()
    done();
  }, JEST_TIMEOUT);
  it(
    'test wx api',
    async () => {
      page = await miniProgram.reLaunch('/test/request/index');
      await page.waitFor(500);
      console.log(await page.data());
    },
    JEST_TIMEOUT,
  );
});
