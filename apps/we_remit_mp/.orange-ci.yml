master:
  merge_request:
    - stages: 
      - name: remove fcd reviewer
        type: git:review
        options:
          type: remove-reviewer
          reviewers: xmagicwu
      - name: add reviewer
        type: git:review
        options:
          type: add-reviewer-from-group-members
          exclude: 
            - xmagicwu
            - doublezhang
            - zijianwang
            - eupholin
            - tenpaytdg
            - ppdmojogit
          count: 3
        exports:
          reviewersForAt: CURR_REVIEWER_FOR_AT
      - name: notify
        type: wework:message
        options:
          robot: a63e44f1-d95b-47d5-8a34-0121130ca47f
          message: |
            🎨 MR 来啦！！！
            > 发起者：${ORANGE_BUILD_USER}
            > 项目：${ORANGE_REPO_SLUG}
            > MR 内容：[${ORANGE_MERGE_REQUEST_TITLE}](${ORANGE_EVENT_URL})
            > review 指派：${CURR_REVIEWER_FOR_AT}
  review:
    - stages:
      - name: CR 通过后自动合并
        type: git:automerge
        options:
          mergeType: squash
          mergeCommitMessage: $ORANGE_LATEST_COMMIT_MESSAGE
        exports:
          reviewers: REVIEWERS
      - name: notify
        type: wework:message
        options:
          robot: a63e44f1-d95b-47d5-8a34-0121130ca47f
          message: |
            ⭕️ MR 通过啦！！！<@${ORANGE_BUILD_USER}>
            > 项目：${ORANGE_REPO_SLUG}
            > MR 内容：[${ORANGE_MERGE_REQUEST_TITLE}](${ORANGE_EVENT_URL})
            > reviewer: ${REVIEWERS}
            
            
