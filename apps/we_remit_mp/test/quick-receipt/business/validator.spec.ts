import { to } from '../../../src/common/utils/util-v2';
import { checkAddress, checkIdentifyCardDate, checkRegion } from '../../../src/package-quick-receipt/business/validator';
describe('小鹅快收表单校验规则', () => {
  describe('校验地址', () => {
    test('地址只有一个中文字符', async () => {
      const [err] = await to(checkAddress('测3d') as Promise<string>);
      expect(err).toBe('要求长度在2-40个字符之间，且中文字符大于2个');
    })

    test('地址有2个中文字符，41个字符', async () => {
      const [err] = await to(checkAddress('测测3d测3d测3sdfsddgfhjgkhjghg2grhytjuyjturt4') as Promise<string>);
      expect(err).toBe('要求长度在2-40个字符之间，且中文字符大于2个');
    })

    test('40个字符，有特殊符号', async () => {
      const [err] = await to(checkAddress('测测3d测3d测3sdfsddgfhjgkhjghgr34546787[p,te') as Promise<string>);
      expect(err).toBe('不支持特殊字符、标点符号');
    })

    test('有emoji字符', async () => {
      const [err] = await to(checkAddress('测测3d测3d测3s😄') as Promise<string>);
      expect(err).toBe('不支持特殊字符、标点符号');
    })

    test('地址里有间隔的2个中文', () => {
      expect(checkAddress('DS的ddf风')).toEqual(true);
    })
  })

  describe('校验省市区', () => {
    test('值为空', () => {
      //@ts-ignore
      checkRegion([]).catch(err=>{
        expect(err).toBe('请选择地区')
      })
    })

    test('值正确', () => {
      expect(checkRegion({value: ['广东省', '深圳市', '南山区'], code: ['12','213','23']})).toEqual(true);
    })
    
  })

  describe('校验证件有效期', () => {
    test('测试有3个闰年的情况', () => {
      expect(checkIdentifyCardDate('2016-02-29', '2026-02-29', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2016-01-29', '2026-01-29', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2014-11-04', '2024-11-04', '', true)).toEqual(true);
    })

    test('测试有2个闰年', () => {
      expect(checkIdentifyCardDate('2016-05-29', '2026-05-29', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2014-02-29', '2024-02-29', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2014-01-20', '2024-01-20', '', true)).toEqual(true);
    })

    test('测试起止日期为10年零1天', () => {
      const isValid = checkIdentifyCardDate('2014-01-20', '2024-01-21', '', true);
      expect(isValid).toEqual(true);
    })

    test('校验日期改成15年', () => {
      expect(checkIdentifyCardDate('2012-02-29', '2027-02-29', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2012-03-29', '2027-03-29', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2013-04-1', '2028-04-1', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2013-02-29', '2028-02-29', '', true)).toEqual(true);
      expect(checkIdentifyCardDate('2013-02-29', '2028-02-29', '', true)).toEqual(true);
    })

  })
})