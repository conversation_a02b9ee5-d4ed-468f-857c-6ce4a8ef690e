import { posListToDemical } from '../../../src/common/utils/util-v2';
import { collectInfo, CollectInfoBitNum, CollectInfoForm, CollectInfoPageConf, COLLECT_INFO_FORM_ORDER, filterAddressValue, FormCollectInfoObj, FormItem, getCollectPageConf, getNeedInfoType, getOrderedCollectInfoForm } from '../../../src/package-quick-receipt/business/collect-info';
import { SEX } from '../../../src/package-quick-receipt/cgi/card';

const cachedcollectInfo: FormCollectInfoObj = {
  region: {
    code: ['1232', '2345', '5433'],
    value: ['广东省', '深圳市', '南山区']
  },
  address: '测试地址',
  birth: '1993-09-10',
  cre_begin_date: '2020-10-21',
  cre_end_date: '2029-04-30',
  sex: SEX.FEMALE
};

describe('测试信息采集的缓存逻辑', async () => {
  test('测试采集信息缓存', () => {
    // 初始化获取缓存地址
    expect(collectInfo.getCachedCollectInfo()).toEqual(null);
    // 缓存信息
    collectInfo.cacheCollectInfo(cachedcollectInfo);
    expect(collectInfo.data).toEqual(cachedcollectInfo);

    // 清除地址
    collectInfo.clearCachedCollectInfo();
    expect(collectInfo.data).toEqual(null);
  });

  test('获取缓存中的采集信息，并转换成后台接口参数', () => {
    // 缓存信息
    collectInfo.cacheCollectInfo(Object.assign({ isValid: true}, cachedcollectInfo));

    const params = collectInfo.getFormatParamsFromCache(31);
    expect(params?.element_object?.address_info?.address_detail).toEqual(`${cachedcollectInfo.address}`);
  })
})

describe('测试获取采集信息页面配置信息', () => {
  test('需要采集出生日期', () => {
    const data = getNeedInfoType(2);
    expect(data).toEqual([CollectInfoBitNum.BIRTH]);
  })
  
  test('需要采集出生日期、性别，证件生效和失效期', () => {
    const addEle = posListToDemical([
      CollectInfoBitNum.BIRTH, 
      CollectInfoBitNum.SEX, 
      CollectInfoBitNum.CRE_END_DATE, 
      CollectInfoBitNum.CRE_BEGIN_DATE], COLLECT_INFO_FORM_ORDER.length)
    const data = getNeedInfoType(addEle);
    expect(data).toEqual([
      CollectInfoBitNum.BIRTH,
      CollectInfoBitNum.CRE_BEGIN_DATE, 
      CollectInfoBitNum.CRE_END_DATE, 
      CollectInfoBitNum.SEX,
    ]);
  });

  test('需要采集地址、性别、出生日期，获取表单信息', () => {
    const addEle = posListToDemical([
      CollectInfoBitNum.ADDRESS, 
      CollectInfoBitNum.SEX, 
      CollectInfoBitNum.BIRTH], COLLECT_INFO_FORM_ORDER.length);
    const forms = getOrderedCollectInfoForm(addEle, {});
    console.log(forms);
    expect(forms.length).toEqual(4); // 地址一项有两条表单配置
    expect(forms[0].prop).toEqual('region');
    expect(forms[1].prop).toEqual('address');
    expect(forms[2].prop).toEqual('birth');
    expect(forms[3].prop).toEqual('sex');
  })

  test('获取回乡证信息采集页面配置信息', () => {
    const addEle = posListToDemical([
      CollectInfoBitNum.ADDRESS,
      CollectInfoBitNum.SEX,
      CollectInfoBitNum.BIRTH], COLLECT_INFO_FORM_ORDER.length);
    const {title} = getCollectPageConf(addEle, {});
    expect(title).toEqual('完善个人信息');
  })

  test('获取身份证信息采集页面配置信息', () => {
    const addEle = posListToDemical([
      CollectInfoBitNum.ADDRESS], COLLECT_INFO_FORM_ORDER.length);
    const {title} = getCollectPageConf(addEle, {});
    expect(title).toEqual('完善个人信息');
  })

  test('测试获取回乡证页面信息', () => {
    // 缓存信息
    collectInfo.cacheCollectInfo(cachedcollectInfo);

    const addEle = posListToDemical([
      CollectInfoBitNum.ADDRESS, CollectInfoBitNum.BIRTH, CollectInfoBitNum.SEX
    ], COLLECT_INFO_FORM_ORDER.length);
    const { title, desc, formConf, form } = getCollectPageConf(addEle, {});
    expect(title).toEqual(CollectInfoPageConf.title);
    expect(desc).toEqual(CollectInfoPageConf.desc);
    expect(formConf[3].prop).toEqual((CollectInfoForm[CollectInfoBitNum.SEX] as FormItem).prop); // addresss有两个表单项
    expect(Object.keys(form).length).toEqual(4);
    expect(form[(CollectInfoForm[CollectInfoBitNum.ADDRESS] as FormItem[])[1].prop]).toEqual(cachedcollectInfo.address);
  })

  test('测试获取身份证页面信息', () => {
    // 缓存信息
    collectInfo.cacheCollectInfo(cachedcollectInfo);

    const addEle = posListToDemical([CollectInfoBitNum.ADDRESS], COLLECT_INFO_FORM_ORDER.length);
    const {title, desc, formConf} = getCollectPageConf(addEle, {});
    expect(title).toEqual(CollectInfoPageConf.title);
    expect(desc).toEqual(CollectInfoPageConf.desc);
    expect(formConf.length).toEqual(2);
  })

})


describe('测试详细地址特殊字符过滤逻辑', () => {
  test('竖线和繁体東过滤', () => {
    expect(filterAddressValue('竖线和繁体東过滤df|dfgd｜fg东繁体東')).toBe('竖线和繁体东过滤dfdfgdfg东繁体东');
  });
});