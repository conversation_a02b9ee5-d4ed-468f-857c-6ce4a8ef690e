import { checkYearUnhandleOrders, getRecordShareData, getYearList, isHasPendingRecord } from "../../../src/package-quick-receipt/business/record";
import * as RecordCgi from "../../../src/package-quick-receipt/cgi/record";
const recordSuc = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-收款成功/data.json');
const recordList = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/tranlist_qry.fcgi/收款记录-有数据/data.json');
const noRecords = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/tranlist_qry.fcgi/收款记录-空/data.json');

describe('测试订单处理', async () => {
  beforeAll(async () => {

  })

  test('测试获取年列表', async () => {
    // @ts-ignore
    wx.getStorageInfoSync = jest.fn().mockImplementation(async () => {
      return 100;
    });
    const years = getYearList();
    const year = new Date().getFullYear();
    expect(years[0]).toEqual(`${year}年`);
    expect(years[years.length - 1]).toEqual(`2020年`);
  });

  test('获取订单分享数据', async () => {
    const data = getRecordShareData(recordSuc);
    expect(data.remitterName).toEqual(recordSuc.remitter_name);
    expect(data.tranUnit).toEqual('¥');
    expect(data.remitCountry).toEqual('美国');
  });

  test('查询是否有未达终态的订单', async () => {
    // @ts-ignore
    RecordCgi.recordListQry = jest.fn().mockImplementation(async () => {
      return recordList;
    });
    const isHasPendingRe = await isHasPendingRecord();
    expect(isHasPendingRe).toEqual(true);
  });

  test('查询今年有待签署承诺函的订单', async () => {
    // @ts-ignore
    RecordCgi.recordListQry = jest.fn().mockImplementation(async () => {
      return recordList;
    });
    const yearWaitSignOrders = await checkYearUnhandleOrders(2020);
    expect(yearWaitSignOrders).toEqual(recordList.tran_items);
  })

  test('查询今年无待签署承诺函的订单', async () => {
    // @ts-ignore
    RecordCgi.recordListQry = jest.fn().mockImplementation(async () => {
      return noRecords;
    });
    const yearWaitSignOrders = await checkYearUnhandleOrders(2020);
    expect(yearWaitSignOrders).toEqual(null);
  })
});