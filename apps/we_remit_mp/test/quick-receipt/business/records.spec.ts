import { formateRecordDatas, dealRecordDetailData } from '../../../src/package-quick-receipt/business/record';
import { recordsData } from '../mock_data/record-list';
const WAIT_RECEIVE = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-发起汇款/data.json');
const CHANGE_BANK_CARD = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-换绑卡/data.json');
const UPLOAD_MATERIALS_AGAIN = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-补充材料，二次补充/data.json');
const FAIL_FOR_UPLOAD_TIMEOUT = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-收款失败-前置状态待提交材料/data.json');
const FAIL_FOR_VERIFY_FAIL = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-收款失败-补充过材料/data.json');
const VERIFYING_UPLOADED = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-银行处理中，上传过材料/data.json');
const VERIFYING_UNUPLOADED = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-银行处理中-稠州渠道/data.json');
const SUCCESS_UPLOADED = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-收款成功-上传过材料/data.json');
const SUCCESS_UNUPLOADED = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-收款成功/data.json');
const BANK_PROCESSING_SIGNED = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-银行处理中-签署过承诺函/data.json');
const CHANGE_BANK_ACCOUNT = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-开户失败更换银行卡/data.json');
const FAIL_STATE_ERR = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/trandtl_qry.fcgi/详情-订单失败前置状态失败的兼容订单/data.json');

describe('测试订单列表数据处理方法 formateRecordDatas', () => {
  beforeAll(() => {
    console.log(2);
  });
  test('测试多种订单状态处理', () => {
    // @ts-ignore
    const formatedData = formateRecordDatas(recordsData);
    expect(formatedData[0].status).toEqual('发起收款');
    expect(formatedData[1].status).toEqual('审核中');
    expect(formatedData[2].status).toEqual('处理中');
    expect(formatedData[3].status).toEqual('审核成功');
    expect(formatedData[4].status).toEqual('银行处理中');
    expect(formatedData[5].status).toEqual('银行处理中');
    expect(formatedData[6].status).toEqual('请更换收款方式');
    expect(formatedData[7].status).toEqual('收款成功');
    expect(formatedData[8].status).toEqual('收款失败');
    expect(formatedData[9].status).toEqual('请提交证明材料');
    expect(formatedData[10].status).toEqual('请提交证明材料');
    expect(formatedData[11].status).toEqual('请确认亲属关系');
    expect(formatedData[12].status).toEqual('请提交证明材料');
    expect(formatedData[13].status).toEqual('请提交材料');
  });
});

describe('测试订单详情数据处理方法 dealRecordDetailData', () => {
  beforeAll(() => {

  });

  test('测试发起收款状态', () => {
    // @ts-ignore
    const { status, progress, button } = dealRecordDetailData('0000', WAIT_RECEIVE);
    expect(status.title).toEqual('发起收款');
    expect(progress.length).toEqual(5);
    expect(button).toEqual(undefined);
  });

  test('测试更换银行卡状态', () => {
    // @ts-ignore
    const { status, progress, button } = dealRecordDetailData('0000', CHANGE_BANK_CARD);
    // @ts-ignore
    expect(button.text).toEqual('前往更换');
    expect(status.title).toEqual('请更换收款方式');
    expect(progress[3].desc).toEqual('收款卡不支持');
    expect(progress[3].icon).toEqual('warn');
  });

  test('测试二次补充材料状态', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', UPLOAD_MATERIALS_AGAIN);
    // 预期：发起汇款-请提交证明材料-审核失败-请提交证明材料-审核成功-银行处理中-收款成功
    expect(status.title).toEqual('请提交证明材料');
    expect(progress[1].tit).toEqual('请提交证明材料');
    expect(progress[2].tit).toEqual('审核失败');
    expect(progress[3].tit).toEqual('请提交证明材料');
    expect(progress[3].icon).toEqual('warn');
    expect(progress[4].tit).toEqual('审核成功');
    expect(progress[4].icon).toEqual('hold');
  });

  test('测试银行处理中状态-补充过材料', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', VERIFYING_UPLOADED);
    // 预期：发起汇款-请提交证明材料-审核中-审核成功-银行处理中-收款成功
    expect(status.title).toEqual('银行处理中');
    expect(progress.length).toEqual(6);
    expect(progress[1].tit).toEqual('请提交证明材料');
    expect(progress[2].tit).toEqual('审核中');
    expect(progress[3].tit).toEqual('审核成功');
    expect(progress[3].icon).toEqual('success');
    expect(progress[4].tit).toEqual('银行处理中');
    expect(progress[4].icon).toEqual('waiting');
    expect(progress[5].tit).toEqual('收款成功');
    expect(progress[5].icon).toEqual('hold');
  });

  test('测试银行处理中状态-未补充过材料', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', VERIFYING_UNUPLOADED);
    // 预期：发起汇款-请提交证明材料-审核中-审核成功-银行处理中-收款成功
    expect(status.title).toEqual('银行处理中');
    expect(progress.length).toEqual(5);
    expect(progress[1].tit).toEqual('审核中');
    expect(progress[2].tit).toEqual('审核成功');
    expect(progress[2].icon).toEqual('success');
    expect(progress[3].tit).toEqual('银行处理中');
    expect(progress[3].icon).toEqual('waiting');
    expect(progress[4].tit).toEqual('收款成功');
    expect(progress[4].icon).toEqual('hold');
  });

  test('测试收款成功-补充过材料', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', SUCCESS_UPLOADED);
    // 预期：发起汇款-请提交证明材料-审核中-审核成功-银行处理中-收款成功
    expect(status.title).toEqual('收款成功');
    expect(progress.length).toEqual(6);
    expect(progress[1].tit).toEqual('请提交证明材料');
    expect(progress[2].tit).toEqual('审核中');
    expect(progress[3].tit).toEqual('审核成功');
    expect(progress[3].icon).toEqual('success');
    expect(progress[4].tit).toEqual('银行处理中');
    expect(progress[4].icon).toEqual('success');
    expect(progress[5].tit).toEqual('收款成功');
    expect(progress[5].icon).toEqual('success');
  });

  test('测试收款成功-未补充过材料', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', SUCCESS_UNUPLOADED);
    // 预期：发起汇款-请提交证明材料-审核中-审核成功-银行处理中-收款成功
    expect(status.title).toEqual('收款成功');
    expect(progress.length).toEqual(5);
    expect(progress[1].tit).toEqual('审核中');
    expect(progress[2].tit).toEqual('审核成功');
    expect(progress[2].icon).toEqual('success');
    expect(progress[3].tit).toEqual('银行处理中');
    expect(progress[3].icon).toEqual('success');
    expect(progress[4].tit).toEqual('收款成功');
    expect(progress[4].icon).toEqual('success');
  });

  test('测试收款失败-因材料超时未提交失败', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('11111', FAIL_FOR_UPLOAD_TIMEOUT);
    expect(status.title).toEqual('收款失败');
    // 预期：发起汇款-请提交证明材料-收款失败
    expect(progress[1].tit).toEqual('请提交证明材料');
    expect(progress[2].tit).toEqual('收款失败');
  });

  test('测试收款失败-因材料审核失败', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', FAIL_FOR_VERIFY_FAIL);
    // 预期：发起汇款-请提交证明材料-审核中-审核失败-收款失败
    expect(status.title).toEqual('收款失败');
    expect(progress[1].tit).toEqual('请提交证明材料');
    expect(progress[2].tit).toEqual('审核中');
    expect(progress[3].tit).toEqual('审核失败');
    expect(progress[3].icon).toEqual('fail');
    expect(progress[4].tit).toEqual('收款失败');
    expect(progress[4].icon).toEqual('fail');
  });

  test('测试银行处理中状态-签署过承诺函', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', BANK_PROCESSING_SIGNED);
    // 预期：发起汇款-审核中-审核成功-银行处理中-收款成功
    expect(progress.length).toEqual(5);
    expect(progress[1].tit).toEqual('审核中');
    expect(progress[2].tit).toEqual('审核成功');
    expect(progress[4].tit).toEqual('收款成功');
    expect(progress[4].icon).toEqual('hold');
  });

  test('测试银行处理中状态-提交过材料', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', BANK_PROCESSING_SIGNED);
    // 预期：发起汇款-审核中-审核成功-银行处理中-收款成功
    expect(progress.length).toEqual(5);
    expect(progress[1].tit).toEqual('审核中');
    expect(progress[2].tit).toEqual('审核成功');
    expect(progress[4].tit).toEqual('收款成功');
    expect(progress[4].icon).toEqual('hold');
  });

  test('测试开户失败需换卡状态', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', CHANGE_BANK_ACCOUNT);
    // 预期：发起汇款-更换银行卡-审核成功-银行处理中-收款成功
    expect(progress.length).toEqual(5);
    expect(progress[1].tit).toEqual('请更换收款方式');
    expect(progress[3].tit).toEqual('银行处理中');
    expect(progress[4].tit).toEqual('收款成功');
    expect(progress[2].icon).toEqual('hold');
  });

  test('测试因后台bug导致的失败订单的前置状态为60的兼容处理', () => {
    // @ts-ignore
    const { status, progress } = dealRecordDetailData('0000', FAIL_STATE_ERR);
    // 预期：发起汇款-审核中-审核成功-银行处理中-收款失败
    expect(progress.length).toEqual(5);
    expect(progress[3].tit).toEqual('银行处理中');
    expect(progress[4].tit).toEqual('收款失败');
    expect(progress[4].icon).toEqual('fail');
  });
});
