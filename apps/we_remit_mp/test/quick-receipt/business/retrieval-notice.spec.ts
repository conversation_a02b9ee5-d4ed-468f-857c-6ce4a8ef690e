import { getRetrievalNoticeText } from '../../../src/package-quick-receipt/business/retrieval-notice';
import * as RetrievalCgi from '../../../src/package-quick-receipt/cgi/retrieval';
import { PAGE } from '../../../src/package-quick-receipt/configs/constant';
const ONE_RETRIEVAL = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/retrievallist_qry.fcgi/一笔待提交尽调单/data.json');
const MORE_THAN_ONE_RETRIEVAL = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/retrievallist_qry.fcgi/多笔中有大于1笔的待提交单/data.json');
const ONE_AUTH_PROCESS = require('../../../mock/weremit.tenpay.com/cgi-bin/remit_gpc/retrievallist_qry.fcgi/一笔审核中尽调单/data.json');

describe('事后调单小黄条逻辑', () => {
    beforeAll(() => {
    });
    test('只有一笔待提交材料状态', async () => {
        // @ts-ignore
        RetrievalCgi.retrievalListQry = jest.fn().mockImplementation(async () => {
            return ONE_RETRIEVAL;
        });
        const noticeConf = await getRetrievalNoticeText();
        expect(noticeConf.linkText).toEqual('去提交');
        expect(noticeConf.linkUrl.indexOf(PAGE.RetrievalUploadMaterial) > -1).toEqual(true);
    });

    test('大于1笔待提交材料状态', async () => {
        // @ts-ignore
        RetrievalCgi.retrievalListQry = jest.fn().mockImplementation(async () => {
            return MORE_THAN_ONE_RETRIEVAL;
        });
        const noticeConf = await getRetrievalNoticeText();
        expect(noticeConf.linkText).toEqual('去提交');
        expect(noticeConf.linkUrl.indexOf(PAGE.RetrievalList) > -1).toEqual(true);
        expect(noticeConf.content.indexOf('位汇款人') > -1).toEqual(true);
    });

    test('只有一笔在审核中', async () => {
        // @ts-ignore
        RetrievalCgi.retrievalListQry = jest.fn().mockImplementation(async () => {
            return ONE_AUTH_PROCESS;
        });
        const noticeConf = await getRetrievalNoticeText();
        expect(noticeConf.linkText).toEqual(undefined);
        expect(noticeConf.linkUrl).toEqual(undefined);
        expect(noticeConf.content.indexOf('正在审核中') > -1).toEqual(true);
    });
   
});