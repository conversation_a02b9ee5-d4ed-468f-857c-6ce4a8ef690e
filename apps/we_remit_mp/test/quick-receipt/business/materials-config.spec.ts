import { getCurYear } from '../../../src/common/business/remit';
import { getMaterialTextByType } from '../../../src/package-quick-receipt/business/materials-config';
import { PageType } from '../../../src/package-quick-receipt/pages/materials/material';
describe('测试根据材料type获取材料相关文案信息', () => {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  beforeAll(() => {

  });

  test('需要上传材料 并 签署承诺函', () => {
    const text = getMaterialTextByType(5, 'ilene');
    expect(text.hk2cnResult.signDesc).toEqual('根据合作银行要求，接收ilene的赡家款，需提交相关材料，并签署亲属关系承诺函。请于48小时内完成，避免因超时导致款项被退回');
    expect(text.noticeSign.title).toEqual('你有一笔来自ilene的收款，接收该汇款人的汇款需提交相关材料，并签署亲属关系承诺函');
    expect(text.lastYearSign.getContent(String(getCurYear() - 1))).toEqual(`你有一笔${getCurYear() - 1}年的收款需提交材料并签署亲属关系承诺函，请前往处理`);
    expect(text.materialPage[PageType.PROOF].pageTitle[0]).toEqual('提交证明材料');
  });

  test('只需要上传材料', () => {
    const text = getMaterialTextByType(6, 'ilene2');
    expect(text.hk2cnResult.signDesc).toEqual('根据合作银行要求，接收ilene2的汇款，需提交相关材料。请于48小时内完成，避免因超时导致款项被退回');
    expect(text.noticeSign.title).toEqual('你有一笔来自ilene2的收款，接收该汇款人的汇款需提交相关材料');
    expect(text.lastYearSign.getContent(String(getCurYear() - 1))).toEqual(`你有一笔${getCurYear() - 1}年的收款需提交证明材料，请前往提交`);
    expect(text.materialPage[PageType.PROOF].pageTitle[0]).toEqual('提交证明材料，完成收款');
  });

  test('只需要签署承诺函', () => {
    const text = getMaterialTextByType(1, 'ilene3');
    expect(text.hk2cnResult.signDesc).toEqual('根据合作银行要求，首次接收ilene3的赡家款需签署亲属关系承诺函，承诺与汇款人的亲属关系，才能办理收款');
    expect(text.noticeSign.title).toEqual('你有一笔来自ilene3的赡家款，首次接收该汇款人的汇款需签署亲属关系承诺函');
    expect(text.lastYearSign.getContent(String(getCurYear() - 1))).toEqual(`你有一笔${getCurYear() - 1}年的收款需签署亲属关系承诺函，请前往签署`);
    expect(text.materialPage[PageType.PROOF]).toEqual(undefined);
    expect(text.materialPage[PageType.SIGN].pageTitle[0]).toEqual('确认亲属关系');
  });
});
