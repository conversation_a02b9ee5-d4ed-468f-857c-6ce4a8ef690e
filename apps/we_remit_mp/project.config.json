{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "rainbowAppid": "df6acb21-a316-4fcb-924c-645d41b2e2c4", "rainbowGroup": "key", "setting": {"localPlugins": false, "urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": false}, "compileType": "miniprogram", "appid": "wx5b7cda9d14819945", "projectname": "we_remit_mp", "miniprogramRoot": "dist/", "cloudfunctionRoot": "cloudfunctions/", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "srcMiniprogramRoot": "dist/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "condition": {"miniprogram": {"list": [{"name": "订单列表", "pathName": "package-receipt/pages/records/record-list/record-list", "query": "", "scene": null}, {"name": "pages/intro/intro", "pathName": "pages/intro/intro", "query": "", "scene": null}, {"name": "选择城市列表", "pathName": "pages/country/country", "query": "", "scene": null}, {"name": "订单详情页", "pathName": "package-tuition/pages/records/record-detail/record-detail", "query": "tuitionChannel=ysh_001", "scene": null}, {"name": "分享成功信息", "pathName": "package-receipt/pages/records/record-share/record-share", "query": "remitter_name=yali&exch_amt=300.00&exch_unit=US$&tran_amt=100&tran_unit=CNY&rate=USD/CNY：6.9983&from=share", "scene": null}, {"name": "收汇查询", "pathName": "package-receipt/pages/code/code", "query": "", "scene": null}, {"name": "收汇成功页", "pathName": "package-receipt/pages/result/result", "query": "listid=4001800008695202011143280000008&status=SUCCESS&recCh=MoneyGram_boc&cardNo=1234&bankName=建设银行", "scene": null}, {"name": "腾讯留学缴费测试支付", "pathName": "package-tuition/pages/index/index", "query": "", "scene": null}, {"name": "院校搜索页", "pathName": "package-tuition/pages/search/search", "query": "", "scene": null}, {"name": "学校账户添加页", "pathName": "package-tuition/pages/account-add/index", "query": "schoolCode=xx20200318113811000226&routingType=BSB", "scene": null}, {"name": "缴费 - 计算金额", "pathName": "package-tuition/pages/account-pay-step1/index", "query": "accountCode=zh20200318113811000226&schoolCode=112233test&tuitionChannel=ysh_001", "scene": null}, {"name": "学生信息页", "pathName": "package-tuition/pages/student/student", "query": "school_code=xx20200318113811000226&account_code=zh20200318113811000226&entrance=add", "scene": null}, {"name": "订单确认页", "pathName": "package-tuition/pages/order/order", "query": "listid=5001800008742202010162070001801", "scene": null}, {"name": "留学缴费", "pathName": "package-tuition/pages/index/index", "query": "scene=tuiCh%3Dysh_link_00001", "scene": null}, {"name": "留学订单列表", "pathName": "package-tuition/pages/records/record-list/record-list", "query": "listid=234234", "scene": null}, {"name": "安全校验", "pathName": "package-tuition/pages/account-verify/index", "query": "entrance=quick&studentCode=STU_202005262070000812&schoolCode=112233test&accountInfo={\"account\":\"A*3*FB**12\",\"account_code\":\"qasdfq2test\",\"account_name\":\"test name\",\"routing_code\":\"123111\",\"routing_type\":\"BSB\",\"swift_code\":\"WVG55\",\"total_count\":\"0\"}", "scene": null}, {"name": "安全校验-字体demo", "pathName": "package-tuition/pages/account-verify/demo", "query": "entrance=quick&studentCode=STU_202005262070000812&schoolCode=112233test&accountInfo={\"account\":\"12*************9123412\",\"account_code\":\"qasdfq2test\",\"account_name\":\"test name\",\"routing_code\":\"123111\",\"routing_type\":\"BSB\",\"swift_code\":\"WVG55\",\"total_count\":\"0\"}", "scene": null}, {"name": "国家列表", "pathName": "package-tuition/pages/country-list/country-list", "query": "", "scene": null}, {"name": "用户协议", "pathName": "package-tuition/pages/protocol/user-agreement", "query": "", "scene": null}, {"name": "我的收款名片", "pathName": "package-quick-receipt/pages/receipt-card/receipt-card", "query": "", "scene": null}, {"name": "我的银行卡", "pathName": "package-quick-receipt/pages/bank-card/bank-card", "query": "listId=1322323&prom_channel=test&delayText=测试", "scene": null}, {"name": "分享名片", "pathName": "package-quick-receipt/pages/share-card/share-card", "query": "", "scene": null}, {"name": "平台收款记录", "pathName": "package-quick-receipt/pages/records/record-list", "query": "", "scene": null}, {"name": "pages/entry/index", "pathName": "pages/entry/index", "query": "", "scene": null}, {"name": "选卡刷脸联调", "pathName": "package-receipt/pages/demo/selectcard", "query": "", "scene": null}, {"name": "港陆收款", "pathName": "package-quick-receipt/pages/bank-card/bank-card", "query": "listId=**********&quickReceiptChannel=hk2cn", "scene": null}, {"name": "测试港陆收款", "pathName": "package-quick-receipt/pages/demo/index", "query": "", "scene": null}, {"name": "全球收款路由页", "pathName": "pages/entry/receipt-entry", "query": "", "scene": null}, {"name": "大额支付转账指引", "pathName": "pages/web-view/index", "query": "url=https%3A%2F%2Fweremit.tenpay.com%2Fhtdocs%2Fmojo%2Fweremit%2Fapp_8_656%2Findex.shtml%23%2F&title=腾讯留学缴费", "scene": null}, {"name": "学校账户列表", "pathName": "package-tuition/pages/school-account-list/index", "query": "schoolCode=10000&schoolName=National&studentCode=234233&entrance=quick&channel_id=ysh_111", "scene": null}, {"name": "代付人确认信息", "pathName": "package-tuition/pages/another-pay-confirm/index", "query": "", "scene": null}, {"name": "代付人付款状态", "pathName": "package-tuition/pages/another-pay-order/index", "query": "", "scene": null}, {"name": "路由页收款记录", "pathName": "pages/entry/record-list/record-list", "query": "", "scene": null}, {"name": "补充信息", "pathName": "package-receipt/pages/other/other", "query": "", "scene": null}, {"name": "签约", "pathName": "package-receipt/pages/sign/sign", "query": "", "scene": null}, {"name": "实名信息", "pathName": "package-receipt/pages/verify/verify", "query": "", "scene": null}, {"name": "pages/entry/index", "pathName": "pages/entry/index", "query": "sourceChannel=ddd", "scene": null}, {"name": "活动入口", "pathName": "pages/prom/webview", "query": "prom_channel=test&url=https%3A%2F%2Fweremit.tenpay.com%2Fhtdocs%2Fmojo%2Fweremit%2Fapp_8_921%2Findex_debug.shtml%3FnoAdtagTransfer%3D1%23%2F", "scene": null}, {"name": "带渠道的首页", "pathName": "pages/entry/entry", "query": "prom_channel=test1", "scene": null}, {"name": "凭证", "pathName": "package-tuition/pages/upload-proof/upload-proof", "query": "scene=tuiCh%3Dysh_link_00001", "scene": null}, {"name": "package-tuition/pages/example/example", "pathName": "package-tuition/pages/example/example", "query": "scene=tuiCh%3Dysh_link_00001", "scene": null}, {"name": "重新收款", "pathName": "package-receipt/pages/records/record-detail/record-detail", "query": "listId=4001800008695202101123280030252&recCh=MoneyGram_boc", "scene": null}, {"name": "小鹅快收-订单详情", "pathName": "package-quick-receipt/pages/records/record-detail", "query": "listId=**********&quickReceiptChannel=hk2cn", "scene": null}, {"name": "速汇金-确认收款页", "pathName": "package-receipt/pages/confirm/confirm", "query": "listId=4001800008695202101123280030252&recCh=MoneyGram_boc", "scene": null}, {"name": "中行服务签约", "pathName": "package-receipt/pages/sign/sign", "query": "listId=4001800008695202101123280030252&recCh=MoneyGram_boc", "scene": null}, {"name": "package-receipt/pages/verify/verify", "pathName": "package-receipt/pages/verify/verify", "query": "", "scene": null}, {"name": "package-receipt/pages/verify/verify", "pathName": "package-receipt/pages/verify/verify", "query": "listId=**********&quickReceiptChannel=hk2cn", "scene": null}, {"name": "亲属承诺函", "pathName": "package-quick-receipt/pages/relatives-commitment/relatives-commitment", "query": "", "scene": null}, {"name": "package-quick-receipt/pages/canvas/canvas", "pathName": "package-quick-receipt/pages/canvas/canvas", "query": "", "scene": null}, {"name": "小鹅快收-港陆入口", "pathName": "package-quick-receipt/pages/bank-card/bank-card", "query": "quickReceiptChannel=hk2cn&listId=AW8BAIj5PK2a7gsyoKstavp8DZzJh6Vsu8wI7lLdcV_rb18-UoRV3w", "scene": null}, {"name": "小鹅快收-自然入口", "pathName": "package-quick-receipt/pages/receipt-card/receipt-card", "query": "", "scene": null}, {"name": "小鹅-互联收款", "pathName": "package-quick-receipt/pages/receive-entry/receive-entry", "query": "listid=2040021000000001202110275330050660", "scene": null}, {"name": "小鹅快收-收款协议-单入口", "pathName": "package-quick-receipt/pages/protocol/receipt", "query": "branchType=4638&hasOrder=1", "scene": null}, {"name": "小鹅快收-收款协议-多入口", "pathName": "package-quick-receipt/pages/protocol/receipt", "query": "branchType=4639|4638&hasOrder=1", "scene": null}, {"name": "小鹅快收-收款协议-无指定入口", "pathName": "package-quick-receipt/pages/protocol/receipt", "query": "", "scene": null}, {"name": "小鹅快收-订单详情", "pathName": "package-quick-receipt/pages/records/record-detail", "query": "listid=2040021000000001202110225330025155&entrance=3", "scene": null}, {"name": "订单列表", "pathName": "package-quick-receipt/pages/records/record-list", "query": "", "scene": null}, {"name": "小鹅快收-订单详情-承诺函/材料返回入口", "pathName": "package-quick-receipt/pages/records/record-detail", "query": "listId=**********&quickReceiptChannel=hk2cn&entrance=8", "scene": null}]}}}