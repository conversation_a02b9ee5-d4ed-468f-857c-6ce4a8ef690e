const through = require('through2');
const path = require('path');
const fs = require('fs-extra');
const glob = require('glob');
const mineType = require('mime-types');

function getOffset(name) {
  const topText = name.split('|')[1];
  if (topText) {
    const top = parseInt(topText, 10);
    if (top) {
      return top;
    }
  }
  return -175;
}
function setWxss(file, image, top) {
  let xsscontent = fs.readFileSync(file, 'utf-8');
  const base64 = imgToBase64(image);
  const reg = /.__uidebug:after{[\s\S.]*}/g;
  xsscontent = xsscontent.replace(reg, '');
  const uidebugstyle = `
    .__uidebug:after{
      width:100%;
      position:fixed;
      z-index:9999;
      opacity:0.5;
      top:${top}rpx;
      bottom:0px;
      left:0px;
      content:'';
      display:block;
      background:no-repeat 0 0;
      background-size:100%;
      pointer-events: none;
      background-image:url("${base64}");
    }
  `;
  fs.writeFileSync(file, xsscontent + uidebugstyle, 'utf-8');
}
function resetWxss(file) {
  let xsscontent = fs.readFileSync(file, 'utf-8');
  const reg = /\.__uidebug:after\{[^}]*\}/g;
  xsscontent = xsscontent.replace(/\.__uidebug:after{.*}/g, '');
  fs.writeFileSync(file, xsscontent, 'utf-8');
}
function imgToBase64(url) {
  try {
    const imageData = fs.readFileSync(url);
    if (!imageData) return '';
    const bufferData = Buffer.from(imageData).toString('base64');
    const base64 = `data:${mineType.lookup(url)};base64,${bufferData}`;
    return base64;
  } catch (error) {
    return '';
  }
}

module.exports = async function uidebug() {
  const stream = through.obj(function (fileSrc, encoding, callback) {
    if (!fileSrc.isBuffer()) {
      return callback();
    }
    const file = fileSrc;
    const dir = path.dirname(file.path);
    const extname = path.extname(file.path);
    const filename = path.basename(file.path, extname);
    const wxssfile = path.join(dir, `${filename}.wxss`);

    if (!fs.existsSync(wxssfile)) {
      return callback();
    }
    const pngfiles = glob.sync(
      `${path.join(dir.replace('dist', 'src'))}/__*.png`
    );

    let offsetTop = 0;
    let pngfile = '';
    for (let i = 0; i < pngfiles.length; i++) {
      const name = path.basename(pngfiles[i], path.extname(pngfiles[i]));

      if (name.indexOf(`__${filename}`) === 0) {
        pngfile = pngfiles[i];
        offsetTop = getOffset(pngfiles[i]);
      }
    }
    if (pngfile === '') {
      resetWxss(wxssfile);
      return callback();
    }

    let content = file.contents.toString();
    const classReg = /<[a-zA-Z]+.*?class="([^'"<>]*)".*>/;
    const reg = /^[\s\S]?<[a-zA-Z]+(.*)?>/;
    if (content.match(classReg)) {
      const match = content.match(classReg);
      content = content.replace(classReg, () =>
        match[0].replace(match[1], `${match[1]} __uidebug`)
      );
    } else {
      const match = content.match(reg);
      content = content.replace(reg, () =>
        match[0].replace('>', ' class="__uidebug">')
      );
    }
    file.contents = Buffer.from(content);
    // 添加样式
    setWxss(wxssfile, pngfile, offsetTop);
    this.push(file);
    return callback();
  });

  return stream;
};
