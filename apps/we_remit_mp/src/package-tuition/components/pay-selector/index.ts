import { BigPayInfo, BigPayFlag } from '../../cgi/bigpay';
import { PayType } from '../../cgi/pay';
import { fenToYuan } from '../../../common/business/currency';
import ICONS from './svg-icons/*.svg';
/**
 * 所支持的所有支付方式
 */
const TypeConfig: {
  [key: string]: {
    name: string;
    icon: string;
    order: number;
  };
} = {
  [PayType.BIGPAY]: {
    name: '银行转账',
    icon: ICONS.bigpay,
    // 顺序，小的在前，大的在后
    order: 0,
  },
  [PayType.WECHAT]: {
    name: '微信支付',
    icon: ICONS.wechat,
    order: 1,
  },
};
/**
 * 支付方式所需的所有字段信息
 */
interface PayTypeInfo {
  type: PayType;
  disabled: boolean;
  reason?: string;
  name: string;
  icon: string;
  payTips?: string;
}
/**
 * 排序所有支付方式
 */
function buildTypeInfos(): PayTypeInfo[] {
  const payTypeinfo: PayTypeInfo[] = [];
  Object.keys(TypeConfig).forEach((key) => {
    const item = TypeConfig[key];
    payTypeinfo[item.order] = {
      type: key as PayType,
      disabled: false,
      name: item.name,
      icon: item.icon,
    };
  });
  return payTypeinfo;
}
/**
 * @description 支付选择组件，从底部弹出，可根据传入订单信息
 * @emits:
 *   - close: 关闭弹层时触发
 *   - pay: 支付按钮点击时触发
 */
Component({
  options: {
    styleIsolation: 'apply-shared',
  },
  properties: {
    amount: {
      type: Object,
      value: {},
    },
    show: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    // 是否显示mask
    maskShow: false,
    // 是否移出支付弹窗
    payShow: true,
    // 是否移出支付方式弹窗
    selectorShow: false,
    // 默认是微信支付
    currentPayType: PayType.WECHAT,
    selectPayType: PayType.WECHAT,
    // 是否有大额支付相关信息
    hasBigpayInfo: true,
    // 主要给模板使用
    payType: PayType,
    typeConfig: TypeConfig,
    // 支付方式相关信息
    payTypeInfos: buildTypeInfos(),
    // 如果只有一种支付方式的时候，是否显示大额提示
    shouldShowPaynotice: false,
    amount: {},
    isBigPay: false,
  },

  observers: {
    amount() {
      this.formatPayTypes();
    },
    show(val) {
      this.setData({ selectorShow: false });
      if (val) {
        this.setData({ payShow: true });
      } else {
        this.setData({ payShow: false });
      }
    },
  },
  ready() {},
  methods: {
    // 支付弹窗关闭动画结束，触发close事件
    onPayClose() {
      // 选择弹窗和支付弹窗都关闭的时候
      if (!this.data.selectorShow) {
        this.triggerEvent('close');
      }
    },
    // 支付方式弹窗动画结束，去除它，并且重置选中的支付方式
    onSelectorClose() {
      this.setData({ selectorShow: false });
      this.setData({ selectPayType: this.data.currentPayType });
    },
    // 支付选择弹窗关闭动画开始时，开始执行支付弹窗的动画
    onSelectorBeforeClose() {
      this.setData({ payShow: true });
    },
    // 支付方式选择点击
    onSelectorTap() {
      if (this.data.hasBigpayInfo) {
        this.setData({ selectorShow: true });
        this.setData({ payShow: false });
        this.triggerEvent('changePayType');
      }
    },
    // 支付按钮点击
    onPayTap() {
      this.triggerEvent('pay', { payType: this.data.currentPayType });
    },
    // 支付方式选择按钮点击
    onPayTypeTap(event) {
      const { item } = event.currentTarget.dataset;
      if (!item.disabled) {
        this.setData({ selectPayType: item.type });
      }
    },
    // 选择完成按钮点击
    onWayCompleteTap() {
      this.setData({ currentPayType: this.data.selectPayType });
      this.setData({ selectorShow: false });
    },
    // 格式化支付方式数据
    formatPayTypes() {
      const bigPayInfo = this.data.amount as BigPayInfo;
      const { payTypeInfos, typeConfig } = this.data;
      // 判断是否需要大额支付
      if (
        bigPayInfo.default_pay_type?.toString() === PayType.BIGPAY
        || +fenToYuan(this.data.amount.pay_amt, 2) > 50000
      ) {
        this.setData({ isBigPay: true });
      }
      // 如果不返回大额支付相关信息，则只有微信支付的方式（一般非白名单用户会有这种情况）
      if (bigPayInfo.big_pay_flag === undefined) {
        this.setData({ currentPayType: PayType.WECHAT });
        this.setData({ selectPayType: PayType.WECHAT });
        this.setData({ hasBigpayInfo: false });
        if (this.data.isBigPay) {
          this.setData({ shouldShowPaynotice: true });
        }
        return;
      }
      // 如果大额支付可用，并且是默认支付方式
      if (
        bigPayInfo.default_pay_type?.toString() === PayType.BIGPAY
        && bigPayInfo.big_pay_flag?.toString() !== BigPayFlag.DISABLE
      ) {
        this.setData({ currentPayType: PayType.BIGPAY });
        this.setData({ selectPayType: PayType.BIGPAY });
      }
      // 通过对应支付方式的order，找到数组索引进行赋值
      payTypeInfos[typeConfig[PayType.BIGPAY].order].disabled = (
        bigPayInfo.big_pay_flag?.toString() === BigPayFlag.DISABLE
      );
      // 大额支付周末等延迟到账提示
      payTypeInfos[typeConfig[PayType.BIGPAY].order].payTips =  bigPayInfo.pay_type_tips || '';

      this.setData({ payTypeInfos });
      this.resetPayStatus(bigPayInfo);
    },
    /**
     * 根据后台返回的状态扭转时间来重置支付状态
     * 大额支付周末也放开，永远返回可用，该方法没用了
     *
     * @param {BigPayInfo} bigPayInfo
     */
    resetPayStatus(bigPayInfo: BigPayInfo) {
      const time = +bigPayInfo.big_pay_not_in_use_left_time;
      // 如果时间大于一天，就不扭转大额支付的状态
      if (
        time
        && time < 86400
        && bigPayInfo.big_pay_flag.toString() === BigPayFlag.DISABLE
      ) {
        setTimeout(() => {
          const { payTypeInfos, typeConfig } = this.data;
          // 直接将大额支付状态变为可用
          payTypeInfos[typeConfig[PayType.BIGPAY].order].disabled = false;
          if (this.data.isBigPay) {
            this.setData({
              payTypeInfos,
              currentPayType: PayType.BIGPAY,
              selectPayType: PayType.BIGPAY,
            });
          } else {
            this.setData({
              payTypeInfos,
            });
          }
        }, time * 1000);
      }
    },
  },
});
