.pay-selector-container {
    position: fixed;
    left: 0;
    bottom: 0;
    top:0;
    width: 100%;
    background-color:rgba(0,0,0,0);
    transition:all .3s;
    display: none;
}
.pay-selector-show{
    display:block;
}
.pay-selector-mask-show{
  background-color:rgba(0,0,0,.72);
}
.pay-way{
    
    &-btn{
        padding: 64rpx 32rpx 32rpx;
        text-align: center;
    }
    &-amount{
        margin-top:64rpx;
    }
}
// 支付方式选择
.way-selector{
    margin:88rpx 0 0rpx;
    &-now{
        display:flex;
        align-items: center;
    }
    &-item{
        padding:23rpx 32rpx;
    }
    &-item:after{
        border-color:rgba(0,0,0,.08);
    }
    &-item:before{
        content: '';
        position: absolute;
        height: 1rpx;
        right: 0;
        transform-origin: 0 0;
        bottom: 0;
        left: 30rpx;
        border-top: 1rpx solid rgba(0,0,0,.08);
    }
    &-label{
        color:rgba(0,0,0,.56);
        font-size:28rpx;
    }
    &-now-text{
        color:#000;
        font-size:28rpx;
    }
    &-tips{
        color:rgba(0,0,0,.24);
        font-size:24rpx;
        padding:0 32rpx;
        text-align: left;
        line-height: 32rpx;
        margin-top:16rpx;
        word-break: break-all;
    }
    &-icon{
        width:36rpx;
        height:36rpx;
        margin-right:16rpx;
    }
}

// 支付方式选择列表

.way-item{
    margin:0 32rpx;
    padding: 36rpx 0;
    display: flex;
    border-bottom:1rpx solid rgba(0,0,0,.1);
    position: relative;
    color:#000;
    &-disabled{
        color:rgba(0,0,0,.24);
    }
    &-icon{
        width:36rpx;
        height:36rpx;
        margin-right:16px;
        margin-top:6rpx;
    }
    &-title{
        font-size:34rpx;
        &-tips{
            font-size:28rpx;
            color:#FFA60F;
        }
    }
    &-desc{
        font-size:28rpx;
    }
    &-check{
        position: absolute;
        top:50%;
        right:0px;
        margin-top:-24rpx;
    }
}
.way-select-btn{
    margin:64rpx auto;
    width:368rpx;
}

.pay-way-tips{
    font-size:24rpx;
    color:rgba(0,0,0,.24);
    margin:10rpx 0  40rpx;
}
.pay-selector-container .remit-button-green{
    background-color: #07C160;
    border-color: #07C160;
    color: #fff;
}