<view class="pay-selector-container {{maskShow?'pay-selector-mask-show':''}} {{show?'pay-selector-show':''}}">
	<fit-popup bind:close="onPayClose" hasMask="{{true}}" show="{{payShow}}">
		<view class="pay-way-amount">
			<pay-info wx:if="{{amount.currency}}" amount="{{amount}}" />
		</view>
		<view class="way-selector" wx:if="{{hasBigpayInfo}}">
			<fit-form-item
			 label="支付方式"
			 labelWidth="168"
			 showTriangle="{{true}}"
			 fit-class="way-selector-item"
			 fit-label-class="way-selector-label"
			 bindtap="onSelectorTap"
			>
				<view class="way-selector-now">
					<fit-icon fit-class="way-selector-icon" url="{{payTypeInfos[typeConfig[currentPayType].order].icon}}" color="#09BB07" />
					<text class="way-selector-now-text">{{payTypeInfos[typeConfig[currentPayType].order].name}}</text>
				</view>
			</fit-form-item>
			<view class="way-selector-tips big-pay-tips" 
				wx:if="{{payTypeInfos[typeConfig[payType.BIGPAY].order].payTips && currentPayType === payType.BIGPAY}}">
				<text>{{payTypeInfos[typeConfig[payType.BIGPAY].order].payTips}}</text>
			</view>
		</view>
		<view class="pay-way-btn">
			<view class="pay-way-tips" wx:if="{{shouldShowPaynotice}}">
				<text>若超过银行卡限额，请提前充值“零钱通”完成支付</text>
			</view>
			<fit-button
			 ext-class="remit-button-green"
			 theme="green"
			 size="big"
			 value="确认支付"
			 bindclk="onPayTap"
			 data-type="nextBtn"
			/>
		</view>
	</fit-popup>
	<fit-popup
	 hasMask="{{true}}"
	 hasTitle="{{true}}"
	 show="{{selectorShow}}"
	 title="请选择支付方式"
	 bind:close="onSelectorClose"
	 bind:beforeclose="onSelectorBeforeClose"
	>
		<view class="way-container">
			<view
			 class="way-item  {{item.disabled?'way-item-disabled':''}}"
			 wx:for="{{payTypeInfos}}"
			 wx:key="type"
			 wx:for-index="index"
			 wx:for-item="item"
			 bindtap="onPayTypeTap"
			 data-item="{{item}}"
			>
				<fit-icon fit-class="way-item-icon" url="{{item.icon}}" color="{{item.disabled?'rgba(0,0,0,.15)':'#09BB07'}}" />
				<view class="way-item-content">
					<view class="way-item-title">
						<text>{{item.name}}</text>
						<text  class="way-item-title-tips" wx:if="{{item.reason && item.disabled}}">（{{item.reason}}）</text>
					</view>
					<!-- <text class="way-item-desc" wx:if="{{item.type === payType.BIGPAY}}">金额5万以上推荐使用</text> -->
					<fit-icon
					 name="check"
					 class="way-item-check"
					 wx:if="{{selectPayType === item.type}}"
					 color="#58be6a"
					/>
				</view>
			</view>
		</view>
		<view class="way-select-btn">
			<fit-button
			 ext-class="remit-button-green"
			 size="big"
			 theme="green"
			 value="完成"
			 bindclk="onWayCompleteTap"
			/>
		</view>
	</fit-popup>
</view>

