<fit-popup class="result-check" bind:close="onConfirmClose" hasMask="{{true}}" show="{{popupShow}}">
  <view class="result-check-container">
    <fit-icon name="{{name}}" fit-class="warning-icon" color="{{color}}"/>
    <view class="notice-title">{{notice.title}}</view>
    <view wx:for="{{notice.contents}}" class="notice-content">
      <view class="notice-item">{{item}}</view>
    </view>
    <fit-button 
			 ext-class="confirm-button"
			 size="big"
			 theme="green"
			 value="确定"
			 bindclk="onConfirm"
			/>
  </view>
</fit-popup>