/**
 * 原生对接uni的活动适配组件
 * 可实现在原生小程序里插入uni的活动运营位效果
*/


interface UniPromAdapterDataOptions2 {
  cmpId: string;

  /**
   * 活动数据
   * 包含运营位配置以及业务层传入的拓展字段
  */
  promData: {
    pageId: string,
    positionId: string,
    promStyle: string,
    extInfo?: {
      orderState?: string,
    },
  }|undefined
}

type UniPromAdapterPropsOptions2 = {

  /**
   * 页面id
  */
  pageId: WechatMiniprogram.Component.FullProperty<StringConstructor>;

  /**
   * 位置id
  */
  positionId: WechatMiniprogram.Component.FullProperty<StringConstructor>;

  /**
   * 活动组件自定义样式
  */
  promStyle: WechatMiniprogram.Component.FullProperty<StringConstructor>;

  /**
   * 订单主状态
   */
  orderState: WechatMiniprogram.Component.FullProperty<StringConstructor>;
};

type UniPromAdapterMethodOptions2 = {
  setPromData: () => void;
  onPromCmpMounted: () => void;
  setPromCmpProps: () => void;
};

Component<UniPromAdapterDataOptions2, UniPromAdapterPropsOptions2, UniPromAdapterMethodOptions2>({
  data: {
    /**
     * 组件id，用于后续获取组件进行setData操作
     * 此处有一个随机数逻辑，是为了应对一个页面多个活动组件的场景
    */
    cmpId: `promCmp_${Math.floor(Math.random() * 10000).toString()
      .padStart(4, '0')}`,
    promData: undefined,
  },
  properties: {
    pageId: {
      type: String,
      value: '',
    },
    positionId: {
      type: String,
      value: '',
    },
    promStyle: {
      type: String,
      value: '',
    },
    orderState: {
      type: String,
      value: '',
    },
  },
  lifetimes: {
    attached() {
      this.setPromData();
    },
  },
  methods: {

    /**
     * 组件内部设置活动数据方法
     * 在此组件挂载后调用，是一个简单的格式化过程
    */
    setPromData() {
      this.setData({
        promData: {
          pageId: this.data.pageId,
          positionId: this.data.positionId,
          promStyle: this.data.promStyle,
          extInfo: {
            orderState: this.data.orderState,
          },
        },
      });
    },

    /**
     * 活动组件挂载方法
     * 在uni的活动组件mounted后，设置活动组件的props
    */
    onPromCmpMounted() {
      this.setPromCmpProps();
    },

    /**
     * 【核心】设置活动组件props，实现数据从mp传递到uni
    */
    setPromCmpProps() {
      // 获取活动组件元素
      const promCmp = this.selectComponent(`#${this.data.cmpId}`);
      /**
       * 调用活动组件对外抛出的事件进行setProps
       * uni-vue组件编译后从$vm上可获取到对外抛出的事件
      */
      promCmp.$vm.setPromData(this.data.promData);
    },
  },
});
