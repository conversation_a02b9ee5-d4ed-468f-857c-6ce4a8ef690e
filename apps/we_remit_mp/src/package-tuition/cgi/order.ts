import { request, BaseRes } from './base';
import { StudentFormData } from '../pages/student/student-mode';
import { fileListItem, DetailWaitingPayResp } from './records';
import { StudentQryRes } from './student';
import { BigPayInfoRes, BigPayWatingRes, BigPayFinishFlag } from './bigpay';
import { Router, ChannelKey } from '../../common/business/router';
import { LimitFlag } from './account';
export type OrderUpdReq = {
  /**
   * 1-修改学生信息 2-修改文件
   */
  update_type: '1' | '2';
  listid: string;
  file_list?: fileListItem[];
} & Partial<StudentFormData>;

/**
 * @description 是否优于银行汇率
 * 1-是 2-不是
 */
export enum IsRateBetter{
  YES='1',
  NO='2'
}

export async function orderUpd(params: OrderUpdReq): Promise<BaseRes> {
  return request({
    url: 'tuition/order_upd.fcgi',
    data: formateOrderData(Object.assign(params, { channel_id: Router.getChannel(ChannelKey.tuition) })),
  });
}

/**
 * 业务下单
 */
export const orderReq = (params: OrderQeq): Promise<DetailWaitingPayResp> => request({
  url: 'tuition/order_req.fcgi',
  data: formateOrderData(Object.assign(params, { channel_id: Router.getChannel(ChannelKey.tuition), fee_version: '1' })),
});

/**
 * 文件列表数据需要stringify
 */
const formateOrderData = (params: OrderQeq | OrderUpdReq) => {
  if (!params.file_list) {
    return params;
  }
  const file = {
    file_list: JSON.stringify(params.file_list),
  };
  return Object.assign(params, file);
};

export interface OrderQeq {
  school_code: string;
  student_code: string;
  account_code: string;
  tuition_amt: string;
  amt: string;  // 用于后端校验学费金额是否大于30w
  file_list: fileListItem[];
  /**
   * cps活动新增字段，字符串格式为"cps_id=xxx"
   */
  extra_info?: string;
}
export interface AccountInfo {
  /**
   * 收款人姓名
   */
  account_name: string;
  /**
   * 收款人账号
   */
  account: string;
  /**
   * swift银行编码
   */
  swift_code: string;
  /**
   * 辅助清算码类型
   */
  routing_type: string;
  /**
   * 银行清算代码
   */
  routing_code: string;
  /**
   * 银行
   */
  bank_name: string;
}

interface FileListItem {
  file_id: string;
  file_type: string;
  file_suffix: 'png' | 'pdf'
}

/**
 * 完成大额转账未完成时
 */

export type DetailQryResCommon = BaseRes &
StudentQryRes & {
  /**
       * 学校英文名
       */
  school_name_en: string;
  /**
       * 学费
       */
  tuition_amt: string;
  /**
       * 币种
       */
  currency: string;
  /**
       * 币种小数位
       */
  currency_point_num: string;
  /**
       * 手续费
       */
  total_fee: string;
  /**
       * 支付金额
       */
  pay_amt: string;
  /**
       * 订单id
       */
  listid: string;
  rate: string;
  sign: string;
  /**
         * 订单状态
         * 10 订单失效                000 订单关闭

            20 等待支付                010 待支付

            30 订单审核                020 支付成功*

            30 订单审核                030 审核中*

            60 缴费失败                040 审核失败

            40 银行处理                050 审核成功*

            40 银行处理                060 归集中

            40 银行处理                070 已归集

            40 银行处理                080 已充值

            40 银行处理                090 已购汇申报

            40 银行处理                100 购汇受理*

            40 银行处理                110 购汇失败

            40 银行处理                120 已购汇

            40 银行处理                130 付汇受理*

            60 缴费失败                140 付汇失败

            50 汇出成功                150 付汇成功*

            60 缴费失败                160 已退票

            60 缴费失败                 170 已结汇申报

            60 缴费失败                 180 结汇受理

            60 缴费失败                 190 结汇失败

            60 缴费失败                 200 结汇成功

            60 缴费失败                 210 已提现

            60 缴费失败                 220 汇款失败

            70 退款中                    230 退款受理

            70 退款中                    240 退款失败

            80 退款成功                250 退款成功
         */
  list_state: string;
  /**
         * 大额支付标识，
         * 0 未完成，
         * 1 完成
         */
  big_pay_finish_flag: BigPayFinishFlag;
  /**
       * 订单来源渠道
       */
  channel_id?: string;
  /**
       * 订单是否可修改
       * 0 不可修改
       * 1 可修改
       */
  edit_flag: '1' | '0';
  /**
       * 是否需要做实名
       * 0 不需要
       * 1 需要
       */
  kyc_flag: '1' | '0';
  /**
       * 订单审核失败原因
       * 多个失败原因之间用换行符隔开，示例："原因1\n原因2\n原因3"
       */
  last_audit_fail_reason: string;
  /**
       * 订单修改失效时间
       * 该批次审核失败时间 + 24 小时（当前再次审核有效时间为24小时）
       */
  last_audit_fail_time: string;
  /**
       * 是否为最后一次修改机会
       * 0 本次修改完成提交审核后，审核失败不可再次修改
       * 1 本次修改完成提交审核后，审核失败还可再次修改
       */
  can_edit_more_again: '1' | '0';
  /**
   * 入账账户号
   * account_code为paybill类型专用
   */
  crn_code: string;
  /**
   * 账户类型
   * 1：电汇类型账户(Swift)
   * 2：加拿大billpay类型账户
   * 3: 澳洲Bpay类型账户
   */
  type: '1' | '2' | '3';
  /**
   * crn_code类型
   * 当type=2时该字段有一意义
   * 1：用户入账的crnno=studentid
   * 2：用户入账的crnno=其它信息+studentid
   */
  crn_type: '1' | '2';
  /**
   * paybill账户的crn_code_check需要传入account_code校验，故detail_qry接口新增返回字段account_code
   */
  account_code: string;
  /**
   * 是否是本人订单
   * 1: 是
   * 2: 不是
   */
  is_user_list: '1'|'2';
  /**
    * 订单归属微信号
    * 掩码展示
    * is_user_list=2时返回
    */
  list_user_name: string;
  /**
   * 原始手续费
   */
  original_total_fee?: string;
  /**
   * 订单代付flag
   * 0：展示其它该展示的页面
   * 1：展示等待家人代付(锁单前)
   * 2：展示家人代付(锁单后)
   */
  pay_share_flag: '0'|'1'|'2';
  /**
   * 分享参数
   */
  share_param?: string;
  /**
   * 是否打开家长代付开关
   */
  pay_share_switch: string;
  /**
   * 是否优于银行汇率
   */
  is_better: IsRateBetter;
  /**
   * 渠道限额标识
   */
  tuition_limit_flag: LimitFlag;

} & AccountInfo & { file_list: FileListItem[] };

export type DetailQryRes = DetailQryResCommon & BigPayWatingRes & BigPayInfoRes;
export async function detailQry(listId: string): Promise<DetailQryRes> {
  return request({
    url: 'tuition/detail_qry.fcgi',
    data: {
      listid: listId,
    },
  });
}
