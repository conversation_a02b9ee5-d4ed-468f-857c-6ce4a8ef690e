import { request } from '../../common/utils/request';
import { wxPromisify } from '../../common/utils/util';
import { G_CGI } from '../../configs/index';
import { KycQryReq, KycQryRes } from './cgi.td';

const Remit = {
  /**
   * @description 用户实名授权信息查询
   */
  kycQry(options: KycQryReq) {
    const promisifyRequest: any = wxPromisify(request);
    let reqCfg = {
      url: (G_CGI as any).KYC_QRY,
      data: options,
    };
    reqCfg = Object.assign(reqCfg, options);
    return promisifyRequest(reqCfg)
      .then((res: KycQryRes) => res)
      .catch((reason: any) => Promise.reject(reason));
  },
};

export default Remit;
