import { Router, ChannelKey } from '../../common/business/router';
// const channelKey = 'channel_id'; // url参数的渠道号对应key
/**
 * 渠道号枚举
 *
 * @export
 * @enum {number}
 */
export enum Channel{
  YSH='ysh',
  DEFAULT='default'
}
/**
 * 获取当前页面渠道号，包括从其他页面透传过来的渠道号
 *
 * @export
 * @returns {Channel}
 */
export function getChannel(): Channel {
  let channel: Channel = Router.getChannel(ChannelKey.tuition) as Channel;

  // 对取到的去掉进行处理，传入的渠道号形如 xxx_111 ，截取前面的主渠道号
  channel = channel.split('_')[0] as Channel;

  if (!Object.values(Channel).includes(channel)) {
    return Channel.DEFAULT;
  }
  return channel;
}
