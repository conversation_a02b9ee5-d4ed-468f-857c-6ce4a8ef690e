import { CustomException } from '../../common/utils/error';

/**
 * ABA Number
英国学校：Sort Code
加拿大学校：CC Code
澳大利亚学校：BSB Code
香港学校：Bank Code
 */
export function getRoutingTypeLabel(routingType: string) {
  const RoutingMap: Record<string, string> = {
    ABA: 'ABA Number',
    SOR: 'Sort Code',
    CCC: 'CC Code',
    BSB: 'BSB Code',
  };
  if (!routingType || routingType === '-') return '';
  if (!RoutingMap[routingType]) {
    new CustomException(null, 'tuition_biz_routingType_info_noShcoolCode', `缺乏${routingType}的学校code`);
    return '';
  }
  return RoutingMap[routingType];
}
