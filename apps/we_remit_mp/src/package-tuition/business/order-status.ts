import { DetailQryRes } from '../cgi/order';
import { PAGE } from '../configs/constant';
import { isAuditFail, isBigPayWating, isOrderUnPay } from './order';
import log from '../../common/utils/log';
import { handleJumpToDetail } from './record';
import { commonToast } from '../../common/utils/util-v2';
import { JumpType } from './big-pay';

/**
 * @description 订单变化自动跳转前展示toast
 * @return {*}
 */
const showRedirectToast = () => {
  setTimeout(() => {
    // hideLoading有一个30ms的延时，这里不加掩饰，hideLoading会直接把toast弹窗给hide
    commonToast({
      title: '当前订单状态发生变化，请等待页面刷新',
    });
  }, 40);
};

/**
 * @description 判断是否为未支付订单，非未支付订单需代码主动跳转
 * @param {DetailQryRes} data
 * @return {*}
 */
export function checkIsNotUnpayOrder(data: DetailQryRes) {
  if (!isOrderUnPay(data) && !isAuditFail(data.list_state, data.edit_flag)) {
    // 非未支付订单且非审核批次失败修改信息订单 需要跳转详情
    log.info('非未支付订单且非审核批次失败修改信息订单 需要跳转详情,this.listId:', data.listid);
    showRedirectToast();
    setTimeout(() => {
      // 跳转订单详情
      handleJumpToDetail(data.listid);
    }, 2000);

    return true;
  }
  if (isBigPayWating(data)) {
    // 未完成大额支付 但是有账号信息，跳转支付等待页
    log.info('未完成大额支付 但是有账号信息，跳转支付等待页,this.listId:', data.listid);
    showRedirectToast();
    setTimeout(() => {
      wx.redirectTo({
        url: `${PAGE.bigPayWaiting}?listid=${data.listid}`,
        jumpType: JumpType.SELF,
      });
    }, 2000);
    // 从订单列表进入，正常流程，无需告警
    log.info(data, 'tuition_order_user_bigPayWaitingOrder', '大额支付等待支付');
    return true;
  }
  return false;
}
