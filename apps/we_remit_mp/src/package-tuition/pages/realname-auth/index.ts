import { navigateBack } from '../../../common/utils/util-v2';
import { tuiBiReport } from '../../business/bi-report';
import log from '../../../common/utils/log';
import { RealNameEntrace } from '../../business/process';

Page({
  data: {
    shouldGoRealname: true,
  },
  onLoad(options: Record<string, string>) {
    if (options?.mode === RealNameEntrace.ONLY_AGREE_PROTOCOL) {
      // 只需要签署协议，不跳转实名小程序
      this.setData({
        shouldGoRealname: false,
      });
    }
    tuiBiReport('wetuition.miniapp.realname.show');
  },
  onShow() {
    this.selectComponent('#realNameComp');
  },
  handleRealNameSuc() {
    log.info('实名认证成功');
    navigateBack(1);
  },
  onTipClose() {
    // 阴影件缺失提醒浮层关闭事件
  },
  protocolChecked(e: WechatMiniprogram.CustomEvent) {
    // 协议勾选事件
    const { checked } = e.detail;
    tuiBiReport('wetuition.miniapp.infoconfirm.user_semilayer_know_click');
    // 自定义分析数据上报
    tuiBiReport('weremit.wetuition.pay.agreement_click', {
      checkboxValue: checked ? 'true' : 'false',
    });
  },
  onSignProtocol() {
    log.info('协议签署成功');
    navigateBack(1);
  },
})
;
