import { to } from '../../../common/utils/util-v2';
import { CheckMtcnSourceResponseBody, ReceivePreference, checkMtcnSource } from '../../datas/cgi/check-mtcn';

export class OrderPreValidationService {
  /**
   * 使用mtcn编码查询互联在途订单
   * @param code 编号收款码
   * @returns 如果命中互联在途订单，则返回对应互联订单号。否则返回undefined
  */
  static async getWlListidByMtcn(mtcn: string): Promise<string|undefined> {
    const [err, checkMtcnSourceRes] = await to<CheckMtcnSourceResponseBody>(checkMtcnSource({
      mtcn,
    }));
    // 如果后台接口报错，视为无互联订单号
    if (err) {
      return;
    }
    if (checkMtcnSourceRes.mtcn_source === ReceivePreference.PREFER_WL) {
      return checkMtcnSourceRes.listid;
    }
    return;
  }
}
