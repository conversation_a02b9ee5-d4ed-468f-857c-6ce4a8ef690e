/**
 * 非法编码限制持久化缓存
*/

import { AbstractStorageClass } from './base';

export enum InvalidCodeLimitStorageKey {
  /**
   * 非法行为记录日志
  */
  INVALID_BEHAVIOR_LOG =  'INVALID_BEHAVIOR_LOG',

  /**
   * 达到限制时间点
  */
  LIMIT_REACHED_TIME =  'LIMIT_REACHED_TIME',
}

class InvalidCodeLimitStorage extends AbstractStorageClass<InvalidCodeLimitStorageKey> {
  protected prefix = 'WU_INVALID_CODE_LIMIT_HANDLE';
  protected isNative = true;
  protected whiteList = undefined;
}

export const invalidCodeLimitStorage = new InvalidCodeLimitStorage();
