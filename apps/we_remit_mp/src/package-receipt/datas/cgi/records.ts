import { ButtonConf } from '../../../common/business/record-base.js';
import { getSystemTime } from '../../../common/business/remit.js';
import { request } from '../../../common/utils/request.js';
import { posListToDemical, wxPromisify } from '../../../common/utils/util.js';
import { descLink } from '../../../components/remit/remit-status/index.js';
import { EDescLinkActionEnum, IDescLink } from '../../../components/remit/text-link/constant.js';
import { G_CGI } from '../../../configs/index.js';
import {
  PCSStatus,
  RecordDetailReq,
  RecordDetailRes,
  RecordListItem,
  RecordListReq,
  RecordListRes,
} from './cgi.td.js';

const Records = {
  /**
   * @description 订单列表查询
   * @param {Object} options
   * @returns
   */
  getRecordList(options: RecordListReq) {
    // 需查询的订单状态组装转换为十进制
    const reqData = JSON.parse(JSON.stringify(options));
    if (reqData.states && reqData.states.length > 0) {
      reqData.state = posListToDemical(
        reqData.states,
        Object.keys(RECORD_STATE).length,
      );
    }
    delete reqData.states;

    const promisifyRequest = wxPromisify(request);
    let reqCfg = {
      url: (G_CGI as Record<string, string>).TRAN_LIST_QRY_CGI,
      data: reqData,
    };
    reqCfg = Object.assign(reqCfg, reqData);
    return promisifyRequest(reqCfg)
      .then((res: RecordListRes) => res)
      .catch((reason: unknown) => Promise.reject(reason));
  },

  /**
   * @description 订单详情查询
   * @param {Object} options
   * @returns
   */
  getRecordDetail(options: RecordDetailReq) {
    const promisifyRequest = wxPromisify(request);
    let reqCfg = {
      url: (G_CGI as Record<string, string>).TRAN_DETAIL_QRY_CGI,
      data: options,
    };
    reqCfg = Object.assign(reqCfg, options);
    return promisifyRequest(reqCfg).then((res: RecordDetailRes) => res)
      .catch((reason: unknown) => Promise.reject(reason));
  },
};

// TS类型声明：订单状态枚举
// eslint-disable-next-line @typescript-eslint/naming-convention
export enum RECORD_STATE {
  VERIFY_FAIL = 0, // 审核失败
  VERIFY_SUC = 1, // 审核成功
  ACCEPT_FAIL = 2, // 受理失败
  ACCEPT_SUC = 3, // 受理成功，即银行处理中
  TRAN_FAIL_CAN_RETRY = 4, // 收款失败，可重试
  TRAN_FAIL = 5, // 收汇失败终态，不可重试
  TRAN_SUC = 6, // 收款成功
}

// TS类型声明：订单状态枚举
// eslint-disable-next-line @typescript-eslint/naming-convention
export enum RECORD_STATE_RES {
  VERIFY_FAIL = 0, // 审核失败
  VERIFY_SUC = 10, // 审核成功（待收款）
  PROCESSING = 15, // 受理中（对应超时情况，我方不知道该笔订单状态）
  ACCEPT_FAIL = 20, // 受理失败
  ACCEPT_SUC = 30, // 受理成功，即银行处理中
  TRAN_FAIL_CAN_RETRY = 40, // 收款失败，可重试
  TRAN_FAIL = 50, // 收汇失败终态，不可重试
  TRAN_SUC = 60, // 收款成功终态
}

export type Icon = 'success'|'fail'|'waiting';

export interface RecordProgressConf {
  tit: string;
  icon: Icon;
  time: string[];
  desc?: string
}

export interface ErrCodeConf {
  desc: string,
  descLinks: descLink | [],
  progress: {
    desc: string,
    descLinks: descLink | [],
  },
}

export interface RecordTextConf {
  title: string;
  icon: Icon;
  desc?: string;
  descLinks?: IDescLink[];
  progress?: RecordProgressConf;
  button?: ButtonConf;
  errCode?: Record<string, ErrCodeConf>;
}

// 订单状态对应相关文案
export const getRecordConfText = (data: RecordDetailRes | RecordListItem) => {
  const RECORD_TEXT: Record<string, RecordTextConf> = {
    0: {
      title: '审核失败', // 此状态订单不会在用户端显示
      icon: 'fail',
    },
    10: {
      title: '审核成功', // 此状态订单只会在首页提醒订单显示
      icon: 'waiting',
      progress: {
        tit: '发起收款',
        icon: 'success',
        time: ['query_time'], // 取query_time字段
      },
    },
    20: {
      title: '受理失败',
      icon: 'fail',
    },
    30: {
      title: '银行处理中',
      icon: 'waiting',
      desc: getProcessingTimeText(data),
      progress: {
        tit: '银行处理中',
        icon: 'success',
        time: ['confirm_time', 'retry_time', 'accept_time'], // 优先取confirm_time字段，再取retry_time字段，其次accept_time
        desc: getProcessingTimeText(data),
      },
    },
    40: {
      // 收款失败可重试
      title: '请更换银行卡',
      icon: 'fail',
      desc: '仅支持I类银行卡收款，请及时换卡，或与发卡行联系',
      descLinks: [{
        type: 'canTapText',
        text: '仅支持',
        style: 'color: rgba(0, 0, 0, 0.88);',
        action: EDescLinkActionEnum.CLASS_I_CARD_TIPS_POPUP,
      }, {
        type: 'canTapText',
        text: 'I类银行卡',
        style: 'color: #3089F0;',
        action: EDescLinkActionEnum.CLASS_I_CARD_TIPS_POPUP,
      }, {
        type: 'canTapText',
        text: '收款，请及时换卡，或与发卡行联系',
        style: 'color: rgba(0, 0, 0, 0.88);',
        action: EDescLinkActionEnum.CLASS_I_CARD_TIPS_POPUP,
      }],
      progress: {
        tit: '更换银行卡',
        icon: 'fail',
        time: ['modify_time'], // 优先取retry_time字段，其次accept_time
        desc: '',
      },
      button: {
        theme: 'green',
        text: '更换银行卡',
        type: 'changeCard',
      },
    },
    50: {
      // 收款失败不可重试
      title: '收款失败',
      icon: 'fail',
      desc: getFailDesText(data),
      progress: {
        tit: '收款失败',
        icon: 'fail',
        time: ['final_time'], // 优先取retry_time字段，其次accept_time
        desc: getFailDesText(data),
      },
      errCode: {
        // 错误码特殊文案处理
        1610674027: {
          desc: '个人外汇额度登记失败，无法完成收款',
          descLinks: [],
          progress: {
            desc: '个人外汇额度登记失败，无法完成收款',
            descLinks: [],
          },
        },
        **********: {
          desc: '结汇金额超过你本年度剩余便利化结汇额度',
          descLinks: [],
          progress: {
            desc: '结汇金额超过你本年度剩余便利化结汇额度',
            descLinks: [],
          },
        },
      },
    },
    60: {
      title: '收款成功',
      icon: 'success',
      desc: '可在{{bankname}}查询到账信息',
      progress: {
        tit: '收款到账',
        icon: 'success',
        time: ['final_time'], // 优先取retry_time字段，其次accept_time
        desc: '到账银行卡：{{tranbank}}',
      },
      button: {
        theme: 'grey',
        text: '告诉汇款人',
        type: 'share',
      },
    },
  };
  return RECORD_TEXT;
};

// 订单失败时，根据浦发状态展示订单失败指引
export const getFailDesText = (data: RecordDetailRes | RecordListItem) => {
  if (!(data as RecordDetailRes)?.pcs_status) return '本渠道收款失败，如有疑问可联系客服咨询';
  const { collect_code: originCode, new_pickup_code: newCode, pcs_status: pcsStatus } = data as RecordDetailRes;
  const conf = {
    [PCSStatus.ORIGIN_CODE]: `本渠道收款失败，请使用原收款编号${originCode}前往其他渠道收款`,
    [PCSStatus.NEW_CODE]: `因系统抖动导致收款失败，请使用新收款编号${newCode}重试`,
    [PCSStatus.CARD_FAIL]: '因您60天内未更换银行卡导致收款失败，请联系客服处理',
  };
  return conf[pcsStatus] || '本渠道收款失败，如有疑问可联系客服咨询';
};

/**
 * @description 订单进入银行处理中状态，根据是否超时展示文案
*/
export const getProcessingTimeText = (data: RecordDetailRes | RecordListItem) => {
  // 默认文案
  const defaultText = '预计{{processingTime}}到账，请留意银行到账通知';

  /**
   * 获取银行处理中时间
   * 按照以下优先级获取：
   * 1、优先取confirm_time（收汇确认时间），这个是最准确的用户感知的确认收款时间
   * 2、再取retry_time（重试时间），此字段是历史遗留字段，理论上当前始终返回空
   * 3、最后取accept_time（受理时间），此字段是收汇审核查询的时间；但是收汇审核查询距离确认收款可能有一段时间差，用这个字段来表示银行处理时间不准确
  */
  const processTime = (data as RecordDetailRes)?.confirm_time
   || (data as RecordDetailRes)?.retry_time
    || (data as RecordDetailRes)?.accept_time;

  if (!processTime) {
    return defaultText;
  }

  /**
   * 23年7月17日注：此处有隐藏的时区问题
   * getSystemTime可能获取到正确/错误时区（看setTimeDiff时用的是什么时间），因此timeDiff计算可能不对
   * 产品决策时区问题后续微汇款统一拉通看
  */
  const curTime = getSystemTime();
  const timeDiff = curTime - +new Date(processTime);
  // 计算时间差，如果时间差超过10分钟，则展示超时文案
  if (timeDiff > 10 * 60 * 1000) {
    return '因西联侧网络抖动，请耐心等待，订单状态一般在2-3个工作日内更新，请留意到账通知';
  }
  return defaultText;
};

/**
 * 速汇金-中行订单详情，中行未上线，此逻辑暂不生效
 */
export const getBocRecodeText = (data: RecordDetailRes | RecordListItem) => {
  const baseRecordText = getRecordConfText(data);
  baseRecordText[50] = {
    // 收款失败不可重试
    title: '重新确认收款',
    icon: 'fail',
    desc: '收款有异常 请重新确认收款',
    progress: {
      tit: '重新确认收款',
      icon: 'fail',
      time: ['final_time'], // 优先取retry_time字段，其次accept_time
      desc: '收款有异常 请重新确认收款',
    },
    button: {
      theme: 'green',
      text: '重新收款',
      type: 'reConfirm',
    },
  };
  return baseRecordText;
};

export default Records;
