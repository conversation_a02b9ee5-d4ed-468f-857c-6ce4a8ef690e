import {
  authSubscribeMsg,
  dealCommonErr,
  isServiceTime,
} from '../../../common/business/remit';
import { CustomException } from '../../../common/utils/error.js';
import log from '../../../common/utils/log';
import {
  commonModal,
  fen2SplitYuan,
  hideLoading,
  rateFormate,
  showLoading,
  to,
} from '../../../common/utils/util.js';
import RemitCgi, { REMIT_QRY_ERROR } from '../../datas/cgi/remit';
import CONSTANT, { ChannelConfig, ENTRY_FROM } from '../../datas/configs/constant';
import { currencyObj, getCurrencyByKey } from '../../datas/configs/currency';
import { RECORD_STATE_RES } from '../../datas/cgi/records';
import { getPrePageName } from '../../../common/utils/page-util';
import { ReceiptBank, getReceiptChannel, receiptBiReport } from '../../common/business/remit';
import { navigateTo } from '../../../common/utils/util-v2';
import { BaseSelectCard, SelectBankInfo, SelectCard } from '../../../common/business/select-card1';
import { RemitQryRes } from '../../datas/cgi/cgi.td';
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const app: any = getApp();
// eslint-disable-next-line @typescript-eslint/no-require-imports, @typescript-eslint/no-var-requires
const computedBehavior = require('miniprogram-computed');

// 汇率过期时间，5分钟刷新
const RATE_EXPIRE_TIME = 1000 * 60 * 5;

// 需要单独处理的三种错误码
const NEED_DEAL_CODE: Record<string, string> = {
  **********: 'EXPIRED',
  **********: 'UNDER_AGE',
  **********: 'EXPIRED_ERROR', // 选卡sessionId过期
  // **********: 'FINAL_FAIL', // 改为state判断
};

// const VERIFY_TYPE = {
//   NO_VERIFY: '0', // 无身份验证
//   PASSWORD: '1', // 密码验证
//   FACE: '2' //刷脸验证
// };

Page({
  name: '微汇款-全球收款-收款确认页',
  behaviors: [computedBehavior],
  data: {
    remitter_name: '',
    exc_unit: '',
    exc_amt: '',
    bank: '', // 经过拼接的银行卡信息，格式：银行名(银行卡尾号)
    bankLogo: '',
    bankName: '',  // 银行名
    bankTail: '', // 银行卡尾号
    purposeIndex: 0,
    initPurpose: true,
    disableConfirmBtn: true,
    isCheckedProtocol: false,
    showIClassCardTipsPop: false,
    purposeList: CONSTANT.SPDB_PURPOSE_LIST,
    relationIndex: 0,
    relationList: CONSTANT.REALATION_LIST, // 与汇款人关系
    initRelation: true,
    isChangeCard: false, // 更换银行卡流程
    startTime: {},
    endTime: {},
    channelName: '',
    receiptChannel: '',
    protocol: [] as unknown as ChannelConfig['protocol'],
  },
  isFirstShow: true,
  updateRateTime: 0, // 更新汇率的时间戳
  isReqSubscribeMsg: false, // 是否已问询过订阅消息
  listId: '',
  sessionId: '', // 预选卡sessionid
  timer: null as null | number, // 汇款过期定时器
  isServAvailable: true, // 当前服务是否可用：9:00-20:00为服务可用时间
  tranType: 1, // 收汇类型：1、首次收汇；2、重新收汇
  isNeedRefreshRate: false, // 是否需要刷新收汇信息
  isOpenTimeout: false, // 是否开启汇率定时器
  // faceParams: <any>{},
  faceToken: '', // 验证人脸token
  encData: '', // 验证人脸信息
  channelConfig: {} as unknown as ChannelConfig,
  receiptOrg: '', // 机构key
  receiptBank: '', // 银行key
  selectCardInstance: null as SelectCard<BaseSelectCard> | null, // 选卡实例
  oldBankName: '',  // 旧银行卡名称（换卡场景保存旧的银行卡名）
  oldBindTail: '',  // 旧银行卡尾号（换卡场景保存旧的银行卡尾号）
  watch: {
    /**
     * 监听：数据完整后，按钮可点击
     * TODO 优化点：
     * 此处bank是对客展示的文案，不是实际传输的数据
     * 合理方式是监听选卡返回数据
    */
    'bank, bankLogo, initPurpose, isCheckedProtocol'(bank: string, bankLogo: string, initPurpose: boolean, isCheckedProtocol: boolean) {
      // 是否需要校验换卡信息
      const needJudgeChangeCardInfo = this.tranType === 2 && this.oldBankName && this.oldBindTail;

      // 检查银行名称是否改变
      const isBankNameChanged = this.data.bankName !== this.oldBankName;

      // 检查银行卡尾号是否改变
      const isBankTailChanged = this.data.bankTail !== this.oldBindTail;

      // 如果需要校验换卡，且银行名称和银行卡尾号【有一项改变】，则设置changeCardFlag为true
      // 否则，直接设置changeCardFlag为true代表校验通过
      const changeCardFlag = needJudgeChangeCardInfo ? (isBankNameChanged || isBankTailChanged) : true;

      this.setData({
        disableConfirmBtn: !(
          bank
          && bankLogo
          && changeCardFlag
          && !initPurpose
          && isCheckedProtocol
        ),
      });
    },
    /**
     * 监听银行名以及银行卡尾号
    */
    'bankName, bankTail'(bankName: string, bankTail: string) {
      // 如果旧银行卡信息不齐全，则不做校验
      if (!this.oldBankName || !this.oldBindTail) {
        return;
      }
      // 银行名称改变
      const isBankNameChanged = bankName !== this.oldBankName;

      // 银行卡尾号改变
      const isBankTailChanged = bankTail !== this.oldBindTail;

      // 如果银行卡名和银行卡尾号均未改变，则展示错误提示
      if (!isBankNameChanged && !isBankTailChanged) {
        this.setData({
          showChangeCardTips: true,
        });
      } else {
        this.setData({
          showChangeCardTips: false,
        });
      }
    },
  },
  onLoad(options) {
    this.initChannelConfig();
    log.info('confirm page onLoad', options);
    // 监听来源页面传输收汇数据
    const eventChannel = this.getOpenerEventChannel();
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    eventChannel.on('acceptRemitData', async (data = {}) => {
      let remitData = data;

      console.log('remitData', remitData);

      // 无订单号，报错处理
      if (!data.listid) {
        commonModal();
        return;
      }

      // 只收到订单号listid，需查询收汇信息
      if (!data.ref_exchange_rate) {
        const qryResp = await this.qryRemitData();
        if (!qryResp) {
          return;
        }
        remitData = qryResp;
      }

      // 收汇类型，来源页面传入
      this.tranType = data.tran_type ? data.tran_type : this.tranType;
      this.isOpenTimeout = this.tranType === 1;

      // 重新收款，不展示资金用途和与汇款人关系
      if (this.tranType === 2) {
        if (data.branch_name && data.acct_no_mask) {
          // 保存旧卡银行名&尾号
          this.oldBankName = data.branch_name;
          this.oldBindTail = data.acct_no_mask;
          this.setData({
            showChangeCardTips: true,
          });
        } else {
          // 如果换卡场景未传入旧银行卡信息，则告警
          new CustomException(data, 'changeCardInit_withoutOldCardInfo', '换卡场景未传入旧银行卡信息');
        }

        this.setData({
          isChangeCard: true,
          bank: `${data.branch_name}(${data.acct_no_mask})`,
        });
      }

      // 若有purpose_code，需默认填写
      if (data.purpose_code) {
        const purposeIndex = this.getPurposeIndexByKey(data.purpose_code);
        this.setData({
          purposeIndex: purposeIndex > -1 ? purposeIndex : 0,
          initPurpose: purposeIndex === -1,
        });
      }

      // 更新收汇查询数据
      this.updateRemitData(remitData);
    });
    this.initSelectInstance();
  },
  onReady() {
    // 自定义分析数据上报
    wx.reportAnalytics(
      'receipt_confirm_enter',
      this.channelConfig,
    );
  },
  async onShow() {
    receiptBiReport('weremit.collection.confirm.brow', {
      entrance: getPrePageName(),
      isChangeCard: this.tranType === 2 ? 1 : 0,
    });
    log.info('confirm page onShow');

    // 选卡组件返回，处理卡信息
    // 选卡组件返回，处理卡信息
    this.selectCardInstance?.selectBack();

    // 需更新收汇信息：1、跳转更改实名信息；
    if (this.isNeedRefreshRate && this.isOpenTimeout) {
      // 重新查询收汇信息
      const qryResp = await this.qryRemitData();
      if (!qryResp) {
        return;
      }
      this.updateRemitData(qryResp);
      return;
    }

    // 第一次onshow不处理汇率定时任务
    if (!this.isFirstShow && this.isOpenTimeout) {
      // 汇率未失效，刷新定时器
      if (!this.isRateExpired()) {
        this.refreshRateExpireStatus();
        return;
      }

      // 汇率失效处理
      this.dealRemitError('EXPIRED');
    }

    this.isFirstShow = false;
  },
  onHide() {
    // 页面隐藏，关闭定时器
    this.clearRateTimeout();
  },
  onUnload() {
    // 页面卸载，关闭定时器，清除卡信息
    this.clearRateTimeout();
    app.globalData.bank = null;
  },
  initChannelConfig() {
    // 获取机构银行渠道信息
    const receiptChannel = getReceiptChannel();
    this.receiptBank = receiptChannel.receiptBank;
    this.receiptOrg = receiptChannel.receiptOrg;
    this.channelConfig = CONSTANT[receiptChannel.receiptBank];
    const channelName = this.channelConfig.channel_name;
    const { startTime, endTime } = this.channelConfig.serviceTime;
    const protocol = this.channelConfig.protocol.map(item => ({
      ...item,
      // 为跳转的协议加上入口参数（事件上报用）
      link: `${item.link}?entryFrom=${ENTRY_FROM.CONFIRM}`,
    }));
    this.setData({
      channelName,
      startTime,
      endTime,
      receiptChannel: receiptChannel.receiptBank,
      protocol,
    });
  },
  /**
   * @description 更新收汇信息
   */
  updateRemitData(data: RemitQryRes) {
    console.log(data);
    this.listId = data.listid;
    const excCurr = getCurrencyByKey(
      data.exchange_ccy,
      getReceiptChannel().receiptBank as ReceiptBank,
    ) as unknown as currencyObj;
    const tranCcy = getCurrencyByKey(
      data.tran_ccy,
      getReceiptChannel().receiptBank as ReceiptBank,
    ) as unknown as currencyObj;
    this.setData({
      remitter_name: data.remitter_name,
      exc_unit: `${excCurr.symbol}`,
      exc_amt: data.exchange_amt ? `${fen2SplitYuan(data.exchange_amt)}` : '',
      tran_amt: `${
        data.ref_tran_amt
          ? `${tranCcy.symbol}${fen2SplitYuan(data.ref_tran_amt)}`
          : ''
      }`,
      rate: `参考汇率: 1 ${excCurr.key} = ${rateFormate(
        data.ref_exchange_rate,
        4,
      )} ${tranCcy.key}`,
    });

    // 更新汇率的时间
    this.updateRateTime = new Date().getTime();

    // 刷新汇率过期定时器
    this.refreshRateExpireStatus();

    // 非服务时间处理
    this.getIsServAvailable();
  },
  /**
   * @description 更新银行卡信息
   */
  updateBankCard(sessionId: string, bankInfo: SelectBankInfo) {
    this.sessionId = sessionId;
    this.setData({
      bank: `${bankInfo.bank_name}(${bankInfo.bind_tail})`,
      bankLogo: bankInfo.bank_logo_url,
      bankName: bankInfo.bank_name,
      bankTail: bankInfo.bind_tail,
    });
  },
  /**
   * @description 确认收汇提交
   */
  async tapConfirmBtn() {
    showLoading();
    // 自定义分析数据上报
    wx.reportAnalytics(
      'receipt_confirm_confirmclick',
      this.channelConfig,
    );

    if (!this.getIsServAvailable()) {
      return;
    }

    if (!this.isReqSubscribeMsg) {
      await this.reqSubscribeMsg();
    }
    hideLoading();

    // 未通过人脸识别组件选卡，需要重新人脸识别
    // if (app.globalData.bank?.verify_type !== VERIFY_TYPE.FACE) {
    //   this.startFaceVerify();
    //   return;
    // }

    // 执行收汇确认
    this.remitConfirm();
  },

  /**
   * 一类卡提示文案tips点击
  */
  onICardTipsClick() {
    receiptBiReport('weremit.collection.confirm.iclasscardtips_click');
    this.setData({
      showIClassCardTipsPop: true,
    });
  },

  /**
   * 一类卡提示弹窗关闭按钮点击
  */
  onICardTipsClose() {
    receiptBiReport('weremit.collection.confirm.iclasscardpop_close');
    this.setData({
      showIClassCardTipsPop: false,
    });
  },

  /**
   * @description 订阅消息问询
   */
  reqSubscribeMsg() {
    const { templateIds } = this.channelConfig;
    return authSubscribeMsg(templateIds)
      .then((res) => {
        if (res) {
          this.isReqSubscribeMsg = true;
        }
      })
      .catch((err) => {
        new CustomException(err, 'receipt_confirmAuthSubscribeMsgFail', '汇入-confirm-订阅消息问询报错-reqSubscribeMsg');
      });
  },
  /**
   * @description 处理收汇确认异常情况
   */
  dealRemitError(type: string, err?: { retcode: string; retmsg: string }) {
    const errMsg = err?.retmsg ? err?.retmsg : '系统繁忙，请稍后再试';
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const errConfig: Record<string, any> = {
      EXPIRED: {
        content: err?.retmsg
          ? errMsg
          : '由于停留页面超过5分钟，需要重新获取最新参考汇率',
        success: async (res: WechatMiniprogram.ShowModalSuccessCallbackResult) => {
          if (res.confirm) {
            // 重新查询收汇信息
            const qryResp = await this.qryRemitData();
            if (qryResp) {
              this.updateRemitData(qryResp);
            }
          }
        },
      },
      UNDER_AGE: {
        content: errMsg,
        success: (res: WechatMiniprogram.ShowModalSuccessCallbackResult) => {
          // 触发未成年用户错误，不可重试
          if (res.confirm) {
            wx.reLaunch({
              url: `${CONSTANT.PAGE.result}?listid=${this.listId}&status=FAIL`,
            });
          }
        },
      },
      FINAL_FAIL: {
        callback: () => {
          // 不可重试跳转至受理失败页面
          wx.reLaunch({
            url: `${CONSTANT.PAGE.result}?listid=${this.listId}&status=FAIL`,
          });
        },
      },
      EXPIRED_ERROR: {
        callback: () => {
          // 选卡sessionId过期,跳转选卡组件，重新获取银行卡
          receiptBiReport('weremit.collection.confirm.sessionid_expired');
          commonModal({
            content: '银行卡信息获取超时，请重新选择银行卡',
            confirmText: '重新选卡',
            success: (res: WechatMiniprogram.ShowModalSuccessCallbackResult) => {
              if (res.confirm) {
                this.tapSelectCard();
              }
            },
          });
        },
      },
    };
    if (errConfig[type].callback) {
      errConfig[type].callback();
    } else {
      commonModal(errConfig[type]);
    }
  },
  /**
   * @description 协议勾选处理
   */
  bindCheckProtocol(e: WechatMiniprogram.CustomEvent) {
    receiptBiReport('weremit.collection.confirm.select_click', {
      checked: e.detail.checked,
    });
    this.setData({
      isCheckedProtocol: e.detail.checked,
    });
  },
  /**
   * @description 收汇确认接口请求
   */
  reqRemitConfirm() {
    return RemitCgi.setRemitConfirm({
      channel_id: this.channelConfig.channel_id, // 默认上海银行
      business_type: this.channelConfig.business_type, // 微汇款业务
      listid: this.listId,
      tran_type: this.tranType,
      purpose_code: this.data.purposeList[this.data.purposeIndex].key,
      sessionid: this.sessionId,
      relationship: this.data.relationList[this.data.relationIndex].key, // 与汇款人关系
      // face_token: this.faceToken, // 验证人脸token
      // enc_data: this.encData, // 验证人脸信息
    });
  },
  /**
   * @description 判断汇率是否失效
   */
  isRateExpired() {
    if (!this.updateRateTime) {
      return true;
    }

    // 超过汇率有效期
    if (new Date().getTime() - this.updateRateTime > RATE_EXPIRE_TIME) {
      return true;
    }

    return false;
  },
  /**
   * @description 刷新汇率过期时间
   */
  refreshRateExpireStatus() {
    if (!this.isOpenTimeout) {
      return;
    }
    console.log('open Timeout');
    this.clearRateTimeout();

    // 汇率剩余有效时间
    const leftTime = this.updateRateTime + RATE_EXPIRE_TIME - new Date().getTime();

    this.timer = setTimeout(() => {
      this.dealRemitError('EXPIRED');

      // 自定义分析数据上报
      wx.reportAnalytics(
        'receipt_confirm_expired',
        this.channelConfig,
      );
    }, leftTime);
  },
  /**
   * @description 清除汇率失效定时任务
   */
  clearRateTimeout() {
    clearTimeout(this.timer as number);
  },

  /**
   * @description 收汇服务时间处理
   */
  getIsServAvailable() {
    const { channelConfig } = this;
    const { startTime, endTime } = channelConfig.serviceTime;
    const isAvailable = isServiceTime(startTime, endTime);
    this.setData({
      isServAvailable: isAvailable,
    });
    return isAvailable;
  },
  /**
   * @description 收汇审核查询接口调用
   */
  async qryRemitData() {
    showLoading();
    const params = {
      channel_id: this.channelConfig.channel_id,
      business_type: this.channelConfig.business_type,
      org: this.channelConfig.org,
      listid: this.listId,
      showErrModal: false,
    };
    const [qryErr, qryResp] = await to(RemitCgi.remitQry(params));
    hideLoading();
    if (qryErr) {
      receiptBiReport('weremit.collection.confirm.remitQryFail', {
        retcode: qryErr.retcode,
      });
      this.dealQryRemitErr(qryErr);
      new CustomException(qryErr, 'receipt_confirmQryRemitDataFail', '汇入-confirm页面-收汇审核接口报错-qryRemitData');
      return;
    }
    return qryResp;
  },

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  async dealQryRemitErr(qryErr) {
    // 需要处理的错误
    log.warn('remitQry fail', qryErr);

    if (!dealCommonErr(qryErr, REMIT_QRY_ERROR)) {
      return;
    }

    // 需要特殊处理的错误码
    const options: WechatMiniprogram.ShowModalOption = {
      content: qryErr.retmsg,
    };

    // 实名信息错误
    if (REMIT_QRY_ERROR[qryErr.retcode] === 'NAME_EN_ERROR') {
      options.success = (res: WechatMiniprogram.ShowModalSuccessCallbackResult) => {
        if (res.confirm) {
          navigateTo({
            url: CONSTANT.PAGE.verify,
            success: (res) => {
              // 通过eventChannel向被打开页面传送数据
              this.isNeedRefreshRate = true;
              res.eventChannel.emit('acceptRemitData', {
                listid: this.listId,
                pinyinError: true,
              });
            },
          });
        }
      };
    }
    commonModal(options);
  },
  /**
   * @description 点击选择银行卡
   */
  async tapSelectCard() {
    receiptBiReport('weremit.collection.confirm.bankcard_click', {
      isChangeCard: this.tranType === 2 ? 1 : 0,
    });
    // 自定义分析数据上报
    wx.reportAnalytics(
      'receipt_confirm_selectcardclick',
      this.channelConfig,
    );

    showLoading();
    this.selectCardInstance?.toSelectCard({
      preCardPara: CONSTANT.PRE_CARD_PARA,
    });
  },
  onMoneyClick() {
    receiptBiReport('weremit.collection.confirm.usages_click');
  },
  bindPickerChange(e: WechatMiniprogram.CustomEvent) {
    // 自定义分析数据上报
    wx.reportAnalytics(
      'receipt_confirm_purposeclick',
      this.channelConfig,
    );

    if (CONSTANT.SPDB_UNSUPPORT_PURPOSE_LIST.includes(e.detail.value)) {
      commonModal({
        content: '本渠道不支持该资金属性',
        confirmText: '重新选择',
      });
      return;
    }

    if (this.data.initPurpose) {
      this.setData({
        initPurpose: false,
      });
    }
    this.setData({
      purposeIndex: e.detail.value,
    });
  },
  /**
   * 拿到purpose_code获取选项index
   *  */
  getPurposeIndexByKey(code: string): number {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const index = CONSTANT.SPDB_PURPOSE_LIST.findIndex(item => item.key === code);
    return index;
  },
  // 点击协议
  onUserAgreementClick(e: WechatMiniprogram.CustomEvent) {
    // 获取协议的bi事件代码
    const biCode = e.currentTarget.dataset.bicode;
    // 如果有配置事件代码，则上报
    if (biCode) {
      // 上报事件 - 银行卡签约页-查看《XX协议》
      receiptBiReport(`weremit.collection.confirm.${biCode}_click`);
    }
    // 自定义分析数据上报
    wx.reportAnalytics('receipt_confirm_protocolclick', this.channelConfig);
  },
  onRelationClick() {
    receiptBiReport('weremit.collection.confirm.relationship_click');
  },
  bindRelationChange(e: WechatMiniprogram.CustomEvent) {
    // 自定义分析数据上报
    wx.reportAnalytics(
      'receipt_confirm_relationclick',
      this.channelConfig,
    );

    if (this.data.initRelation) {
      this.setData({
        initRelation: false,
      });
    }
    this.setData({
      relationIndex: e.detail.value,
    });
  },
  async remitConfirm() {
    showLoading();

    const [confirmErr, confirmResp] = await to(this.reqRemitConfirm());

    hideLoading();

    // 收汇确认失败
    if (confirmErr) {
      new CustomException(confirmErr, 'receipt_confirmReqRemitConfirmFail', '汇入-confirm-确认收汇接口报错-reqRemitConfirm');
      receiptBiReport('weremit.collection.confirm.known_click', {
        retcode: confirmErr.retcode,
      });

      if (!dealCommonErr(confirmErr, NEED_DEAL_CODE)) {
        return;
      }

      // 特殊错误码逻辑单独处理
      this.dealRemitError(NEED_DEAL_CODE[confirmErr.retcode], confirmErr);
      return;
    }

    // 收汇确认超时处理
    if (+confirmResp?.state === RECORD_STATE_RES.PROCESSING) {
      commonModal({
        content: '系统繁忙，请重试',
        confirmText: '确认收款',
        success: (res: WechatMiniprogram.ShowModalSuccessCallbackResult) => {
          if (res.confirm) {
            this.remitConfirm();
          }
        },
      });
      return;
    }

    // 收汇确认失败
    if (+confirmResp?.state === RECORD_STATE_RES.ACCEPT_FAIL) {
      this.dealRemitError('FINAL_FAIL');
      return;
    }

    if (
      +confirmResp?.state === RECORD_STATE_RES.TRAN_FAIL_CAN_RETRY
      || +confirmResp?.state === RECORD_STATE_RES.TRAN_FAIL
      || +confirmResp?.state === RECORD_STATE_RES.TRAN_SUC
    ) {
      wx.reLaunch({
        url: `${CONSTANT.PAGE.recordDetail}?listId=${this.listId}`,
      });
      return;
    }

    // 收汇确认成功
    wx.reLaunch({
      url: `${CONSTANT.PAGE.result}?listid=${this.listId}&status=SUCCESS&cardNo=${confirmResp?.card_no}&bankName=${confirmResp?.bank_name}`,
    });
  },
  onSpdbClick() {
    receiptBiReport('weremit.collection.confirm.pfagreement_click');
  },
  onConfirmClick() {
    receiptBiReport('weremit.collection.confirm.next_click', {
      disable: this.data.disableConfirmBtn,
    });
  },
  /**
   * 初始化选卡实例
   */
  initSelectInstance() {
    this.selectCardInstance = new SelectCard({
      afterSelect: (sessionId: string, bankInfo: SelectBankInfo) => {
        this.updateBankCard(sessionId, bankInfo);
      },
      navigateSuc: () => {
        hideLoading();
      },
    });
  },
});
