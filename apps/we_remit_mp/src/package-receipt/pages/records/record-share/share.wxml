<template name="share">
    <view class="logo flex align-items-center"><text class="logo-text">微汇款</text> </view>
    <view class="amt-wrap" wx:if="{{shareData.exch_amt}}">
        <view class="amt-tit">已收到 {{shareData.remitter_name}} 的汇款</view>
        <view class="amt-main">
            <text class="unit WeChatSansSS-M">{{shareData.exch_unit}}</text>
            <text class="amount F-DIN">{{shareData.exch_amt}}
              </text>
        </view>
        <view class="tran-wrap">
            <view class="tran-amt">到账金额：{{shareData.tran_unit}}{{shareData.tran_amt}}</view>
            <view class="tran-rate">汇率 {{shareData.rate}}</view>
        </view>
    </view>
    <view class="info" wx:if="{{shareData.exch_amt}}">
        <view class="item flex">
            <view class="flex-1">到账时间</view>
            <view>{{shareData.final_time}}</view>
        </view>
         <view class="item flex">
            <view class="flex-1">{{codeName}}</view>
            <view>{{shareData.collect_code}}</view>
        </view>
    </view>
    <view class="btn-wrap flex-1 flex align-items-center" wx:if="{{shareData.exch_amt}}">
        <fit-button
            ext-class="remit-button-white share-btn"
            theme="green"
            size="big"
            value="分享给好友"
            open-type="share"
            wx:if="{{shareData.from === 'record'}}"
            bind:tap="onShareClick"
        />
        <fit-button
            ext-class="remit-button-white share-btn"
            theme="green"
            size="big"
            value="进入微汇款"
            bindtap="goHome"
            wx:if="{{shareData.from === 'share'}}"
        />
    </view>
</template>