import bi from '../../common/business/global/bi';
import { Router } from '../../common/business/router';
import { ChannelKey } from '../../configs/channel';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function activityBiReport(key: string, params?: Record<string, any>): boolean {
  bi.eventStat(key, Object.assign({}, params, {
    promChannel: Router.getChannel(ChannelKey.promChannel),  // 活动渠道
    entryChannel: Router.getChannel(ChannelKey.entryChannel), // 入口渠道
  }));
  return true;
}
