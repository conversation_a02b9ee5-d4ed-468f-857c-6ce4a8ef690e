import { ComponentWithComputed } from '@tencent/pdd-miniprogram-computed';

ComponentWithComputed({
  options: {
    multipleSlots: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    // 打开方式
    popupVisible: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    images: {
      close: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/digital-souvenirs/index-img/close.svg',
    },
  },

  methods: {
    /**
     * 点击关闭按钮
    */
    onCloseTap() {
      this.triggerEvent('close');
    },
  },
});
