<view class='fit-notice my-class {{type == "operate"?"fit-notice-operate":(type == "tips"?"fit-notice-tips":"")}}' catchtap='tapItem' wx:if="{{showTips}}">
	<view class='iconfont icon-base icon-{{iconType}}' wx:if="{{showNoticeIcon}}"></view>
	<view class='fonttips icon-base icon-{{iconType}}' wx:if="{{showTipIcon}}"></view>
	<view wx:if="{{marquee !== 'true'}}" class='notice-wrap' bindtap='tapItem'>
		<view id="context" 
			class="notice-context notice-overflow {{single === true ? 'notice-context-overflow' : ''}}" 
			style="{{contentStyle}}"
		>{{content}}<text
			class="link"
			wx:if="{{showInlineLink}}"
			style="color: {{linkColor}}"
			bindtap="tapLink"
			>{{linkContent}}</text>
		</view>
	</view>
	<view wx:if="{{marquee === 'true'}}" class='marquee-wrap' bindtap='tapItem'>
		<view id="context" class="notice-context-marquee" style="{{'animation: marquee '+duration+'s linear infinite both'}}">
				{{content}}
		</view>
	</view>
	<view class="link" bindtap="tapLink" wx:if="{{showLink}}" style="color: {{linkColor}}">
			{{linkContent}}
	</view>
	<view class='iconfont close-btn icon-{{iconCloseType}}' wx:if="{{showClose}}" catchtap='tapClose'></view>
	<view class='iconfont icon-arrow-r' wx:if="{{showArrow}}" catchtap='tapGoto'></view>
</view>