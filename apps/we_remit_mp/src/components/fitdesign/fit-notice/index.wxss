/* miniprogram/components/fit-notice/index.wxss */
@import "./iconfont/css/iconfont.wxss";
@-webkit-keyframes marquee{
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes marquee {
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}
.fit-notice{
    width: 100%;
    display:flex;
    position: relative;
    /* padding: 0 40rpx; */
    box-sizing: border-box;
    flex-wrap: nowrap;
    justify-content:space-around;
    font-weight: normal;
    color: #F58C23;
    background: #FEF6E9;
    /* line-height: 80rpx; */
    padding: 20rpx 32rpx;
}
.fit-notice .icon-close-circle{
    color: rgba(0,0,0,0.4);
}
.fit-notice .icon-base{
    margin-right: 16rpx;
	font-size: 28rpx;
    line-height: 40rpx;
    color: #FFA338;
}
.fit-notice .icon-base::before{
    font-size: 30rpx;
}
.fit-notice .icon-close, .fit-notice .icon-arrow-r{
    margin-left: 38rpx;
    margin-top: 7rpx;
    display: none;
}
.fit-notice .icon-close::before, .fit-notice .icon-arrow-r::before{
    font-size: 36rpx;
}
.fit-notice.fit-notice-operate{
    background: #FFFFFF;
    border: 1rpx solid #CACACD;
    border-radius: 8rpx;
    color: #3890CD;
    padding-right: 26rpx;
}
.fit-notice .notice-wrap{
    flex:1;
    overflow: hidden;
    word-break: break-all;
    word-wrap: break-word;
    position: relative;
    text-align: left;
}
.fit-notice .notice-context{
    display:block;
    font-size: 28rpx;
    line-height: 40rpx;
}
.fit-notice .notice-context.notice-context-overflow{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.fit-notice .marquee-wrap {
    flex:1;
    overflow: hidden;
    position: relative;
    line-height: 1;
}
.fit-notice .marquee-wrap .notice-context-marquee{
    display:inline-block;
    padding-left: 100%;
    font-size: 28rpx !important;
    white-space: nowrap;
    text-size-adjust: none; 
    -ms-text-size-adjust: none; 
    -moz-text-size-adjust: none; 
    -webkit-text-size-adjust: none;
}
.fit-notice.fit-notice.fit-notice-operate .icon-notice,.fit-notice.fit-notice.fit-notice-operate .icon-close,.fit-notice.fit-notice.fit-notice-operate .icon-arrow-r{
    display: inline-block;
    margin-top: 0;
}

.fit-notice.fit-notice.fit-notice-tips .icon-close,.fit-notice.fit-notice.fit-notice-tips .icon-arrow-r{
    display: inline-block;
	  margin-top: 0;
}
.fit-notice .link {
    font-size: 28rpx;
    color: #3890CD;
    margin-left: 24rpx;
}
.fit-notice .close-btn{
    margin-left: 24rpx;
    line-height: 40rpx;
}