<!--
fit-status 订单状态组件
<AUTHOR>
@页面使用方式：
<text-link
  desc = "{{desc}}" descLinks="{{descLinks}}"
</text-link>
@属性说明：
desc: '如有疑问请联系客服：95555' //状态栏描述
descLinks: [
  {
    type: 'tel', //链接类型：（1）tel电话；（2）navigate链接；（3）minipro跳转小程序（4）text文案
    text: '联系客服：95555', //链接文案，会代替desc中相同文案，默认显示链接色
    url: '95555', //（1）tel类型：传电话号码；（2）navigate类型：传跳转链接；（3）minipro类型：传跳转小程序参数；4）text类型：不传
    style: 'color: green' //链接样式
  },
  {
    type: 'navigate', //navigate类型
    text: '联系客服',
    url: '/pages/confirm/confirm',
    style: 'color: red'
  },
  {
    type: 'minipro', //minipro类型示例
    text: '联系客服',
    url: {
      appId: 'wxf75cfcbb5412db15',
      path: '/pages/product/productInfo/product?code=A4793&producttitle=微汇款',
      envVersion: 'develop'
    },
    style: 'color: red'
  }
],
@事件说明：
bindTapLink：点击链接触发回调
-->
<view class="text-link my-class {{type}}" wx:if="{{texts && texts.length > 0}}">
  <!-- eslint-disable-next-line vue/singleline-html-element-content-newline -->
  <block wx:for="{{texts}}"
      wx:for-index="idx"
      wx:key="idx">
    <kefu 
      wx:if="{{item.type==='kefu'}}"
      class="link"
      data-index="{{idx}}"
      bind:tapkefu="bindTapLink"
    >{{item.text}}</kefu>
    <text
      wx:else
      class="{{item.url ? 'link': 'text'}}"
      style="{{item.style ? item.style : ''}}"
      data-index="{{idx}}"
      bindtap="bindTapLink">{{item.text}}</text>
  </block>
</view>