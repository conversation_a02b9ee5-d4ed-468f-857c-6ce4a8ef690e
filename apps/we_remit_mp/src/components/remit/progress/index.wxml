<!--
progress 订单流水组件
<AUTHOR>
@页面使用方式：
<remit-progress
  data = "{{data}}"
</remit-progress>
@属性说明：
data: [
  {
    tit: '收款到账',
    time: '2020-08-23 13:34:45',
    icon: 'success', // icon类型
    status: 'success' // 不传默认与icon一致
    desc: '如有疑问请联系客服：95555' //描述文案
    descLinks: [
      {
        type: 'tel', //链接类型：（1）tel电话；（2）navigate链接；（3）minipro跳转小程序（4）text文案
        text: '联系客服：95555', //链接文案，会代替desc中相同文案，默认显示链接色
        url: '95555', //（1）tel类型：传电话号码；（2）navigate类型：传跳转链接；（3）minipro类型：传跳转小程序参数；4）text类型：不传
        style: 'color: green' //链接样式
      },
      {
        type: 'navigate', //navigate类型
        text: '联系客服',
        url: 'https://www.qq.com',
        style: 'color: red'
      },
      {
        type: 'minipro', //minipro类型示例
        text: '联系客服',
        url: {
          appId: 'wxf75cfcbb5412db15',
          path: '/pages/product/productInfo/product?code=A4793&producttitle=微汇款',
          envVersion: 'develop'
        },
        style: 'color: red'
      },
    ]
  }
]
-->
<view class="progress">
  <view class="progress-item {{item.status}} next-{{item.nextStatus}}" wx:for="{{progress}}" wx:for-index="pIndex" wx:key="index">
    <remit-icon my-class="progress-icon" type="{{item.icon}}" size="32"></remit-icon>
    <view class="item-info">
      <view class="main-info">
        <view class="item-tit">{{item.tit}}</view>
        <view class="item-time" wx:if="item.time">{{item.time}}</view>
      </view>
      <text-link my-class="desc-link" desc="{{item.desc}}" descLinks="{{item.descLinks}}"></text-link>
    </view>
  </view>
</view>