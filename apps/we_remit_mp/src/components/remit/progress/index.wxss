
.progress{
  background: #fff;
  padding: 28rpx 32rpx 0;
}
.progress-tit{
  font-size: 24rpx;
  color: rgba(0,0,0,0.56);
  line-height: 32rpx;
  padding: 24rpx 0 16rpx;
  border-bottom: 1rpx solid #ebebeb;
}

.progress-item{
  position: relative;
  display: flex;
}

.item-info{
  flex: 1;
  margin-left: 16rpx;
  margin-bottom: 40rpx;
}
.main-info{
  display: flex;
  line-height: 32rpx;
}

.progress-icon{
  position: relative;
  width: 32rpx;
  height: 32rpx;
  z-index: 10;
  background: #fff;
  border-radius: 50%;
}
.item-tit{
  color: rgba(0,0,0,0.88);
  font-size: 24rpx;
  line-height: 32rpx;
}
.item-time{
  flex: 1;
  color: rgba(0,0,0,0.24);
  font-size: 24rpx;
  line-height: 32rpx;
  text-align: right;
}
.item-desc{
  color: rgba(0,0,0,0.56);
  font-size: 24rpx;
  line-height: 32rpx;
  margin-top: 8rpx;
}

.progress-item::before, .progress-item::after{
  content: '';
  width: 2rpx;
  position: absolute;
  background: rgba(0,0,0,0.08);
}

.progress-item::before{
  left: 16rpx;
  top: 0;
  height: calc(50% + 16rpx);
}

.progress-item::after{
  left: 16rpx;
  bottom: 0;
  height: calc(50% - 16rpx);
}

.progress-item:first-child::before{
  top: 4rpx;
}

.progress-item:last-child::after{
  content: none;
}

.progress-item:last-child::before{
  height: 18rpx;
}

.progress-item.success::before, .progress-item.next-success::after{
  /* background: #12CB78 */
}

.progress-item.waiting::before, .progress-item.next-waiting::after{
  /* background: #10AEFF; */
}

.progress-item.fail::before, .progress-item.next-fail::after{
  /* background: #FA5151; */
}

.progress .desc-link{
  color: rgba(0,0,0,0.56);
  font-size: 24rpx;
  line-height: 32rpx;
  margin-top: 8rpx;
}

.not-reach .progress-icon, .hold .progress-icon{
  width: 10rpx;
  height: 10rpx;
  top: 11rpx;
  background: #ccc;
}

.progress-item.not-reach, .progress-item.hold{
  padding-left: 12rpx;
}
.progress-item.not-reach .item-info, .progress-item.hold .item-info{
  padding-left: 9rpx;
}
.not-reach .item-tit, .not-reach .desc-link, .hold .item-tit, .hold .desc-link{
  color: rgba(0,0,0,0.24);
}