<!--
link-tit 表单模块title组件
<AUTHOR>
@页面使用方式：
<link-tit 
  title="添加入学录取通知书"  //title 内容
  required="true"  //是否显示红*
  linkUrl="/pages/demo/demo" //范例链接
  linkLabel="例子" //“范例”文案，默认为：范例。可不传
></link-tit>

@事件说明：
bindtapExample：点击范例链接触发回调
回调事件数据：
{
  link: string; //链接地址
  success: boolean //是否跳转成功
}
-->
<view class="link-tit my-class">
  <view class="tit-wrap">
    <view class="tit my-class-tit">{{title}}<text class="required" wx:if="{{required}}">*</text></view>
    <view class="link my-class-link" bindtap="tapLink">{{linkLabel}}</view>
  </view>
  <slot name="tip"></slot>
</view>