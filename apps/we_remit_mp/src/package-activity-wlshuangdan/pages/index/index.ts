import { NEW_USER_FOLLOW_ARTICLE, OLD_USER_FOLLOW_ARTICLE, PAGE, SHARE_TITLE, SHARE_IMAGE, SHARE_PATH, ACT_ID, IMAGE, NEW_USER_ARTICLE, OLD_USER_ARTICLE } from './../../configs/constant';
/* eslint-disable @typescript-eslint/naming-convention */
import type { BaseRes } from '../../../common/utils/request-base';
import { requireChainPkg } from '../../../common/utils/require-package';
import { behavior as BehaviorWithComputed } from '@tencent/pdd-miniprogram-computed';
import { ActivityStatus, qryUserActState, receivePrize, SourceEnum, UserType } from '../../cgi/activity';
import { commonModal, navigateBack, navigateTo, to, URL_TYPE, hideLoading, showLoading, getCurrentPage, objToQueryType } from '../../../common/utils/util-v2';
import { CustomException } from '../../../common/utils/error';
import { handleJumpUrl } from '../../../common/business/remit';
import { activityBiReport } from '../../business/bi-report';
import { Router } from '../../../common/business/router';
import CONSTANT from '../../../datas/configs/constant';
import { ChannelKey } from '../../../configs/channel';
/**
 * 错误弹窗参数
 */
export type ErrDealConfig={
  content: string,
  confirmText: string
};

export type PageData = {
  upperBg: string,
  bottomBg1: string,
  bottomBg2: string,
  bottomLetter: string,
  /**
   * 活动id
  */
  actId: string;
  /**
   * 活动状态
   */
  activityStatus: ActivityStatus | null;
  userType: UserType|null;

  /**
   * 规则弹窗可视标志位
  */
  rulePopupVisible: boolean;

  /**
   * 活动页返回标志位
   * 当标志位为1且活动状态是「去收款」时，点击按钮返回上一个页面
  */
  navigateBackFlag: boolean;
};

export type PageCustomOption = WechatMiniprogram.Page.CustomOption;

Page<PageData, PageCustomOption>({
  behaviors: [BehaviorWithComputed],
  data: {
    /**
     * 页面图片集合
     */
    // 上半屏图片
    upperBg: '',
    // 下半屏图片
    bottomBg1: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/wlshuangdan/bg2.png',
    bottomBg2: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/wlshuangdan/bg3.png',
    // 驻底信封图片
    bottomLetter: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/wlshuangdan/letter.png',

    // 活动id
    actId: ACT_ID,

    // 用户类型
    userType: null,
    // 用户状态
    activityStatus: null,

    // 规则弹窗可视标志位
    // TODO 代码规范
    rulePopupVisible: false,
    // 返回标志位
    navigateBackFlag: false,
  },

  /* 任务列表 */
  // 主任务链
  mainChain: null,

  // 子链
  subChain: null,

  // 查询用户状态任务
  userActTask: null,

  // 展示loading任务
  showLoadingTask: null,
  // 加载公共图片任务
  loadPublicImageTask: null,
  // 加载特殊图片任务
  loadSpecifiedImageTask: null,
  // 隐藏loading任务
  hideLoadingTask: null,

  // 公共图片数量
  publicImageNum: 1,
  // 已加载的公共图片数量
  loadedPublicImageNum: 0,

  /**
   * 页面加载
   */
  onLoad(options) {
    this.init();  // 初始化数据
    const { navigateBackFlag } = options;
    this.setData({
      // 设置直接返回的标志位
      navigateBackFlag: navigateBackFlag === '1',
    });
  },

  /**
   * 初始化数据
  */
  async init() {
    // 初始化职责链
    this.startChain();
  },

  /**
   * 页面初始化职责链
   * 职责链流程：
   * 开始 ->  showLoading任务 -> 请求用户userAct接口任务 -> 请求差异化图片任务  ] -> hideLoading任务 -> 结束
   *                          -> 请求公共图片任务                           ]
  */
  async startChain() {
    // 分包异步化
    const [err, pkg] = await to(requireChainPkg());
    if (err) {
      new CustomException(err, 'wlshuangdan_require_pkg_error', '感恩节活动落地页请求分包失败');
      return;
    }
    const { Chain } = pkg;
    const PromiseTaskClass = pkg.PromiseTask;

    /**
     * 主任务
    */
    this.mainChain = new Chain('mainChain', {
      onComplete: () => {
        console.log('chain 主任务完成');
      },
    });

    /**
     * showloading任务
    */
    this.showLoadingTask = new PromiseTaskClass('showLoadingTask', () => {
      showLoading();
      this.showLoadingTask.resolve('');
    });

    /**
     * hideloadin任务
    */
    this.hideLoadingTask = new PromiseTaskClass('showLoadingTask', () => {
      hideLoading();
      this.hideLoadingTask.resolve('');
    });

    /**
     * 请求公共图片任务
     * 兜底超时时间：3s
    */
    this.loadPublicImageTask = new PromiseTaskClass('loadPublicImageTask', () => {
      console.log('chain 开始请求公共图片');
      // 执行请求公共图片任务时，要查一下图片是否已完成加载。避免出现图片完成加载先于任务开始的情况
      if (this.isPublicImageDone()) {
        this.loadPublicImageTask?.resolve('');
      }
      // 兜底3秒后resolve
      setTimeout(() => {
        console.log('chain loadPublicImageTask.resolve');
        this.loadPublicImageTask?.resolve('');
      }, 3000);
    });

    /**
     * 子职责链，包含请求用户状态接口 -> 请求对应用户状态图片两个任务
    */
    this.subChain = new Chain('subChain', {
      onComplete: () => {
        console.log('chain 子任务完成');
      },
    });

    /**
     * 查询用户状态任务
    */
    this.userActTask = new PromiseTaskClass('userActTask', () => {
      console.log('chain 开始请求用户状态');
      this.dealUserState();
    });

    /**
     * 请求差异化图片任务
     * 兜底超时时间：3s
    */
    this.loadSpecifiedImageTask = new PromiseTaskClass('loadSpecifiedImage', () => {
      this.setBg();
      // 兜底3秒后resolve
      setTimeout(() => {
        console.log('chain loadPublicImageTask.resolve');
        this.loadSpecifiedImageTask?.resolve('');
      }, 3000);
    });


    /* 向子链中添加任务 */
    // 单独封装一下
    this.subChain.add(this.userActTask);  // 1、查询用户状态任务
    this.subChain.add(this.loadSpecifiedImageTask);  // 2、请求差异化背景图任务

    /* 向主链中添加任务 */
    this.mainChain.add(this.showLoadingTask);  // 1、showLoading任务
    this.mainChain.add([this.loadPublicImageTask, this.subChain]);  // 2、并行的请求公共图片任务和子职责链
    this.mainChain.add(this.hideLoadingTask);  // 3、hideLoading任务

    // 开始执行职责链
    this.mainChain.next();
  },

  /**
   * 调用接口查询用户状态
  */
  async dealUserState() {
    // 处理活动状态
    // 请求用户参与活动状态查询接口
    const [err, res] = await to(qryUserActState({
      act_id: this.data.actId,
      source: SourceEnum.WECHART_APPLET,
    }));

    // 异常流程，用户状态接口报错
    if (err) {
      new CustomException(err, 'qryUserActStateForwlshuangdanError', '请求用户参与活动状态查询接口出错');
      this.commonErrDeal({
        content: (err as BaseRes)?.retmsg || '系统繁忙，请重试',
        confirmText: '确定',
      }, () => {
        const curQuery = Router.getQuery();
        const curPage = getCurrentPage();
        // 点击确认时刷新页面
        wx.redirectTo({
          url: `/${curPage.route}?${objToQueryType(curQuery as Record<string, string>)}`,
        });
      });
      return;
    }

    // 对用户状态做处理
    const status = res.state;
    const userType = res.user_type;
    this.setData({
      activityStatus: status,
      userType,
    });
    // 如果用户命中已领取状态，则直接跳转到对应的公众号文章页
    if (status === ActivityStatus.ACCEPTED && userType) {
      this.jumpToArticle(userType, true);
    }
    // 上报事件 - 微信收款有礼活动落地页-浏览量
    activityBiReport('collection.miniapp_fastreceipt.wlshuangdan.brow', {
      status,
      userType,
    });

    console.log('chain 查询用户状态成功');
    this.userActTask?.resolve('chain 查询用户状态成功');
  },

  /**
   * 根据用户状态设置背景图
  */
  setBg() {
    // 根据新老用户的状态来设置背景图
    switch (this.data.userType) {
      case UserType.NEW_USER:
        this.setData({
          upperBg: IMAGE.NEW_USER_BG,
        });
        break;
      case UserType.OLD_USER:
        this.setData({
          upperBg: IMAGE.OLD_USER_BG,
        });
        break;
      default:
        new CustomException(this.data.userType, 'wlshuangdan_unkonw_usertype', '未知的用户状态');
        return;
    }
  },

  /**
   * 根据用户类型跳转对应的公众号文章
  */
  jumpToArticle(userType: UserType, redirectToFlag = false) {
    let articleUrl = '';
    switch (userType) {
      case UserType.NEW_USER:
        articleUrl = NEW_USER_ARTICLE;
        break;
      case UserType.OLD_USER:
        articleUrl = OLD_USER_ARTICLE;
        break;
    }
    // 跳转到对应的公众号文章页
    const shareConfig = {
      shareTitle: (SHARE_TITLE),
      sharePath: (SHARE_PATH),
      shareImageUrl: (SHARE_IMAGE),
    };
    // objToQueryType有encodeURIComponent逻辑;
    const params = objToQueryType(shareConfig);
    handleJumpUrl(
      URL_TYPE.H5,
      {
        url: articleUrl,
        params,
      },
      CONSTANT.PAGE.webView,
      false,
      redirectToFlag,
    );
  },

  /**
   * 调用领奖接口
  */
  async receivePrize() {
    showLoading();
    const [err] = await to(receivePrize({
      act_id: this.data.actId,
      source: SourceEnum.WECHART_APPLET,
    }));
    hideLoading();
    if (err) {
      new CustomException(err, 'wlshuangdan_receivePrize_err', '领奖接口失败');
      this.commonErrDeal({
        content: (err as BaseRes)?.retmsg || '系统繁忙，请重试',
        confirmText: '确定',
      }, async () => {
        // 如果领奖接口报错了，原地查询落地页状态
        showLoading();
        await this.dealUserState();
        hideLoading();
      });
      return;
    }

    // 和后端同学确认，领奖接口只要retcode为0，就表示领奖成功
    // 领奖成功后手动将用户状态扭转到「已领取」
    this.setData({
      activityStatus: ActivityStatus.ACCEPTED,
    });

    // 跳转到公众号文章
    this.jumpToArticle(this.data.userType);
    return;
  },

  /**
   * 点击活动按钮事件
  */
  clickAwardButton() {
    const status = this.data.activityStatus;
    const { userType } = this.data;
    // 上报事件 - 微信收款有礼活动落地页-微信收款有礼活动落地页-按钮点击点击
    activityBiReport('collection.miniapp_fastreceipt.wlshuangdan.button_click', {
      status,
      userType,
    });
    // 按钮点击策略
    switch (this.data.activityStatus) {
      /**
       * 活动未开始、奖品已发完、奖品已领取、活动已结束几种状态按钮不可点击
      */
      case ActivityStatus.NOT_YET:
      case ActivityStatus.FINISHED:
      case ActivityStatus.ACCEPTED:
      case ActivityStatus.END:
        return;
      // 活动已开始状态按钮点击后跳转XXX
      case ActivityStatus.IN_PROGRESS:
        if (Router.getChannel(ChannelKey.promChannel).indexOf('timeline') > 0) {
          navigateTo({
            url: PAGE.institutionList,
            success: () => {
              console.log('跳转机构营销页成功');
            },
          });
        } else if (this.data.navigateBackFlag) {
          navigateBack();
        } else {
          navigateTo({
            url: PAGE.quickReceiptIndex,
            success: () => {
              console.log('活动页跳转成功');
            },
          });
        }
        return;
      // 领奖状态按钮点击后XXX
      case ActivityStatus.READY_TO_ACCEPT:
        this.receivePrize();
        return;
      default:
        new CustomException(status, 'wlshuangdan_unkonw_status', '未知的活动状态');
        return;
    }
  },

  /**
   * 差异化图片加载完成事件
  */
  specialImageOnload() {
    this.loadSpecifiedImageTask?.resolve('');
  },

  /**
   * 差异化图片加载失败事件
  */
  specialImageOnerror() {
    this.loadSpecifiedImageTask?.resolve('');
  },

  /**
   * 加载公共图片事件
  */
  publicImageOnLoad() {
    this.loadedPublicImageNum = this.loadedPublicImageNum + 1;
    if (this.isPublicImageDone()) {
      this.loadPublicImageTask?.resolve('');
    }
  },

  /**
   * 加载公共图片失败事件
  */
  publicImageOnerror() {
    this.loadPublicImageTask?.resolve('');
  },

  /**
   * 检查公共图片是否加载完毕
  */
  isPublicImageDone() {
    return this.loadedPublicImageNum === this.publicImageNum;
  },

  /**
   * 规则按钮点击事件
  */
  clickRuleButton() {
    this.setData({
      rulePopupVisible: true,
    });
    // 微信收款有礼活动落地页-点击“规则”按钮
    activityBiReport('collection.miniapp_fastreceipt.wlshuangdan.rule_click', {
      status: this.data.activityStatus,
      userType: this.data.userType,
    });
  },
  /**
   * 关注公众号点击
  */
  jumpToFollowArticle() {
    let articleUrl = NEW_USER_FOLLOW_ARTICLE;
    if (this.data.userType === UserType.OLD_USER) {
      articleUrl = OLD_USER_FOLLOW_ARTICLE;
    }
    activityBiReport('collection.miniapp_fastreceipt.wlshuangdan.officialaccountentry', {
      status: this.data.activityStatus,
      userType: this.data.userType,
    });
    handleJumpUrl(
      URL_TYPE.H5,
      {
        url: articleUrl,
      },
    );
  },

  /**
   * 关闭规则弹窗事件
  */
  onCloseRulePop() {
    this.setData({
      rulePopupVisible: false,
    });
  },

  /**
   * 微信分享内容配置
   */
  onShareAppMessage() {
    // 微信收款有礼活动落地页-分享活动
    activityBiReport('collection.miniapp_fastreceipt.wlshuangdan.share', {
      status: this.data.activityStatus,
      userType: this.data.userType,
    });
    return {
      title: SHARE_TITLE,
      path: SHARE_PATH,
      imageUrl: SHARE_IMAGE,
    };
  },

  /**
   * 通用接口异常处理逻辑
   */
  commonErrDeal(data: ErrDealConfig, successCallBack?: () => void) {
    // 展示错误弹窗前先hideLoading，避免loading挡住弹窗
    hideLoading();
    commonModal({
      content: data.content,
      confirmText: data.confirmText,
      success: successCallBack ? successCallBack : () => {
        // 默认不做处理
      },
    });
  },
});
