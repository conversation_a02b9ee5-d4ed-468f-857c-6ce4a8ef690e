import { ComponentWithComputed } from '@tencent/pdd-miniprogram-computed';
import { ActivityStatus, UserType } from '../../cgi/activity';

ComponentWithComputed({
  options: {
    multipleSlots: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    activityStatus: {
      type: String,
      value: '',
    },
    userType: {
      type: String,
      value: '',
    },
  },

  computed: {
    // 用户头像的css属性
    buttonText(data) {
      const { activityStatus } = data;
      switch (activityStatus) {
        /**
         * 活动未开始、奖品已发完、奖品已领取、活动已结束几种状态按钮不可点击
        */
        case ActivityStatus.NOT_YET:
          return '活动未开始';
        case ActivityStatus.FINISHED:
          return '立减金已发完';
        case ActivityStatus.ACCEPTED:
          return '已领取';
        case ActivityStatus.END:
          return '活动已结束';
        case ActivityStatus.IN_PROGRESS:
          return '去完成一笔收款';
        case ActivityStatus.READY_TO_ACCEPT:
          if (data.userType === UserType.NEW_USER) {
            return '领取20元立减金';
          }
          return '领取10元立减金';
        default:
          return '';
      }
    },
    disableButton(data) {
      const { activityStatus } = data;
      switch (activityStatus) {
        /**
         * 活动未开始、奖品已发完、奖品已领取、活动已结束几种状态按钮不可点击
        */
        case ActivityStatus.NOT_YET:
        case ActivityStatus.FINISHED:
        case ActivityStatus.ACCEPTED:
        case ActivityStatus.END:
          return true;
        case ActivityStatus.IN_PROGRESS:
        case ActivityStatus.READY_TO_ACCEPT:
          return false;
        default:
          return true;
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    click() {
      // 按钮点击事件
      this.triggerEvent('clk');
    },
  },

  lifetimes: {
  },
});
