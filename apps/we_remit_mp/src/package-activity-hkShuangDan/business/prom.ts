/**
 * 活动business
 * <AUTHOR>
*/
import { AwardType, PurposeCode } from '../types/index';
import { requirePromUtilPkg } from '../../common/utils/require-package';
import { to } from '../../common/utils/util-v2';
import { CustomException } from '../../common/utils/error';
import { BusinessKey, CacheType, GlobalCache } from '../../common/business/cache/global-cache';

const activityGlobalCache = new GlobalCache({ business: BusinessKey.hkShuangDan });

export enum ActivityCacheKey {
  /**
  * 用户信息缓存key
  */
  ANIMATION_CACHE= 'animationCache',
}

/**
 * 红包数字图片Map
*/
const numImageMap: Record<AwardType, string> = {
  [AwardType.UNKNOWN]: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/hkShuangDan/six.png',
  [AwardType.FIRST]: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/hkShuangDan/six.png',
  [AwardType.SECOND]: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/hkShuangDan/eight.png',
  [AwardType.THIRD]: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/activity/hkShuangDan/ten.png',
};

/**
 * 未知情况下的兜底文案
*/
const defaultText = '每一笔汇款，离心中梦想更近\n明年一定越来越六';

/**
 * 文案Map
*/
const textMap: Record<AwardType, Record<PurposeCode, string>> = {
  [AwardType.UNKNOWN]: {
    [PurposeCode.UNKNOWN]: defaultText,
    [PurposeCode.SELF]: defaultText,
    [PurposeCode.RELATIVES]: defaultText,
  },
  [AwardType.FIRST]: {
    [PurposeCode.UNKNOWN]: defaultText,
    [PurposeCode.SELF]: '每一笔汇款，离心中梦想更近\n明年一定越来越六',
    [PurposeCode.RELATIVES]: '每一次汇款，心都更近一些\n明年一起六到飞起',
  },
  [AwardType.SECOND]: {
    [PurposeCode.UNKNOWN]: defaultText,
    [PurposeCode.SELF]: '这一年辛苦了\n往前奔跑别忘了给自己拥抱',
    [PurposeCode.RELATIVES]: '这一年辛苦了\n即使无法相见，常忆曾经相拥',
  },
  [AwardType.THIRD]: {
    [PurposeCode.UNKNOWN]: defaultText,
    [PurposeCode.SELF]: '离乡是为了理想\n感谢始终认真生活的自己',
    [PurposeCode.RELATIVES]: '相信好事终会发生\n见面的日子近了',
  },
};

/**
 * 返回命中的文案
*/
export function getText(awardType: AwardType, purposeCode: PurposeCode) {
  return textMap[awardType][purposeCode];
}

/**
 * 返回命中的红包数字图片
*/
export function getNumImage(awardType: AwardType) {
  return numImageMap[awardType];
}

/**
 * bi上报
*/
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function activityBiReport(key: string, params?: Record<string, any>) {
  const [err, pkg] = await to(requirePromUtilPkg());
  if (err) {
    new CustomException(null, 'hkShuangDan_requirePromUtilPkgErr', '港陆双旦请求活动工具分包失败');
    return;
  }
  pkg.activityBiReport(key, params);
}

function getAniCacheKey(listId: string) {
  return `${ActivityCacheKey.ANIMATION_CACHE}_${listId}`;
}

/**
 * 缓存红包动画播放
 */
export function setAniCache(listId: string) {
  activityGlobalCache.setData({
    key: getAniCacheKey(listId),
    data: 'true',
    type: CacheType.storage,
  });
}

/**
 * 获取红包动画缓存
 */
export function getAniCache(listId: string) {
  return activityGlobalCache.getData(getAniCacheKey(listId), CacheType.storage);
}
