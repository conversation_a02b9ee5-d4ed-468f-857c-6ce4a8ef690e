/* eslint-disable */
import { reportRedirectPage } from '../../../common/business/redirect-page-report';
import { objToQueryType } from '../../../common/utils/util-v2';
import CONSTANT from '../../../datas/configs/constant';



Page({
  name: '全球收款-小鹅快收-名片分享页面',
  data: {},
  onLoad(options) {
    const myCardOptions = { ...options };
    myCardOptions.name_en = myCardOptions.nameEn;
    myCardOptions.auth_phone = myCardOptions.mobileNo;
    wx.redirectTo({
      url: `${CONSTANT.PAGE.MyCard}?${objToQueryType(myCardOptions as Record<string, string | Record<string, string>>, false)}`,
    });
    reportRedirectPage('package-quick-receipt/pages/share-card/share-card');
    return;
  },
});
