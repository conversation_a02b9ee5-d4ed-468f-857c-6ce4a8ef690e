import log from '../../../common/utils/log';
import { getPrePageName } from '../../../common/utils/page-util';
import { objToQueryType } from '../../../common/utils/util-v2';
import { quickReceiptBiReport } from '../../business/bi-report';
import { Entrance } from '../../business/card';
import { PAGE } from '../../configs/constant';

Page<DetailPageData, DetailPageProperty>({
  name: '全球收款-小鹅快收-收款详情页',
  entrance: Entrance.DEFAULT,
  async onLoad(options: Record<string, string>) {
    log.info('recordDetail page onload', options);
    // 从页面参数中解析渠道
    const entrance = options.entrance || Entrance.DEFAULT;
    this.entrance = entrance;
    // 跳转到 uniapp 框架下的订单详情页，并且带上所有当前的 url 参数
    wx.redirectTo({
      url: `${PAGE.recordDetail}?${objToQueryType(options)}`,
    });
    return;
  },
  async onShow() {
    quickReceiptBiReport('weremit.fastreceipt.details.brow', {
      entrance: this.entrance || getPrePageName(),
    });
  },
});
