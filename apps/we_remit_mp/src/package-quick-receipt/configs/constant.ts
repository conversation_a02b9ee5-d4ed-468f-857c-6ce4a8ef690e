import { getCommonWebViewUrl } from '../../common/business/webview/index';

// 配在CMS平台上的协议
const CmsProtocolUrl = {
  UserProtocol: 'https://posts.tenpay.com/posts/9ea15e3a66826d5b000043de2b090d35.html', // 个人信息授权书
  chouzhouProtocol: 'https://posts.tenpay.com/posts/8da2cdda668215e001611c3d5613f841.html', // 浙江稠州商业银行汇入汇款服务协议
  CZServiceProtocol_v2: 'https://posts.tenpay.com/posts/ad727431667d348a014daf927fdf5164.html', // 财付通用户收款信息服务协议

  privateProtocol: 'https://posts.tenpay.com/posts/886895ec641c001e000e3fdf13764942.html',  // 财付通跨境收款服务隐私保护指引
  GGServiceProtocol_v2: 'https://posts.tenpay.com/posts/9ba6d790641c008500108ef123e34ab6.html', // 财付通用户跨境收款服务协议
};
/**
 * 页面列表
 */
export const ProtocolPage = {

  /**
   * 个人信息授权页
   */
  UserProtocol: getCommonWebViewUrl({
    url: CmsProtocolUrl.UserProtocol,
    onShowReportEvent: {
      key: 'gpc_agreement_user_protocol_show.brow',
      params: {
        contractVer: '1.0',
      },
    },
  }),
  /**
   * 稠州协议1.0
   */
  chouzhouProtocol: getCommonWebViewUrl({
    url: CmsProtocolUrl.chouzhouProtocol,
    onShowReportEvent: {
      key: 'gpc_agreement_chouzhou_protocol_show.brow',
      params: {
        contractVer: '1.0',
      },
    },
  }),
  /**
   * 稠州服务协议2.0
   */
  CZServiceProtocol_v2: getCommonWebViewUrl({
    url: CmsProtocolUrl.CZServiceProtocol_v2,
    onShowReportEvent: {
      key: 'gpc_agreement_cz_protocol_v2_show.brow',
      params: {
        contractVer: '2.0',
      },
    },
  }),
  /**
   * 广工服务协议2.0
   */
  GGServiceProtocol_v2: getCommonWebViewUrl({
    url: CmsProtocolUrl.GGServiceProtocol_v2,
    onShowReportEvent: {
      key: 'gpc_agreement_gg_cft_detail_show.brow',
      params: {
        contractVer: '2.0',
      },
    },
  }),
  /**
   * 人脸识别协议
   */
  faceProtocol: '/package-quick-receipt-extra/pages/face-protocol/face-protocol',
  /**
   * 隐私保护指引
   */
  privateProtocol: getCommonWebViewUrl({ // 隐私保护指引
    url: CmsProtocolUrl.privateProtocol,
    onShowReportEvent: {
      key: 'gpc_private_protocol_show.brow',
      params: {
        contractVer: '1.0',
      },
    },
  }),

};


/**
 * 港陆兔年桥批活动页面
 */
export const HKSpringFestival = {
  home: '/package-activity-hkSpringFestival/pages/index/index',
};

const MainPage = {
  Home: '/package-main-uni/pages/home/<USER>',
  receiptCard: '/package-quick-receipt/pages/receipt-card/receipt-card', // 名片页(包含创建名片页)
  bankCard: '/package-quick-receipt/pages/bank-card/bank-card', // 银行卡页(包含创建银行卡页)
  shareCard: '/package-quick-receipt/pages/share-card/share-card', // 分享给其他人打开的名片页面
  recordList: '/package-quick-receipt/pages/records/record-list', // 订单列表页
  recordDetail: '/package-gpc-uni/pages/records/record-detail/index', // 订单详情页面
  recordShare: '/package-quick-receipt/pages/records/record-share', // 收款成功分享的页面
  // receiptHK2CNResult: '/package-quick-receipt/pages/receipt-result/hk2cn-result', // 港陆收款处理中页面
  receiptHK2CNResult: '/package-gpc-uni/pages/receipt-hk2cn/result', // 港陆收款处理中页面
  uploadMaterials: '/package-quick-receipt/pages/materials/materials', // 补充材料/签署承诺函页面
  previewSign: '/package-quick-receipt/pages/preview-sign/preview-sign', // 预览承诺函页面
  messageResult: '/package-quick-receipt/pages/receipt-result/message-result', // 短信收款处理中页面
  receiveEntry: '/package-quick-receipt/pages/receive-entry/receive-entry', // 短信收款入口页面
  // fileExample: '/package-quick-receipt/pages/materials/examples/example', // 补充材料范例页面
  fileExample: '/package-gpc-uni/pages/material/examples/index', // 补充材料范例页面，已迁移到 uni
  collectInfo: '/package-gpc-uni/pages/element/collect/index', // 收集地址页面

  branchProtocol: '/package-gpc-uni/pages/contract/sign/index', // 渠道协议页面
  signedProtocols: '/package-gpc-uni/pages/contract/signed/index', // 签署过的协议列表页
  MechanismCountriesSelect: '/package-quick-receipt/pages/country-select/country-select', // 机构国家地区选择页面
  // MechanismList: '/package-quick-receipt/pages/institution-list/index', // 机构列表页面
  MechanismList: '/package-fastreceipt-uni/pages/institution/list', // 机构列表页面

  RetrievalList: '/package-quick-receipt/pages/retrieval/retrieval-list/index', // 事后调单列表
  gpcRetrievalList: '/package-gpc-uni/pages/material/retrieval-list/index',  // gpc分包事后调单列表

  RetrievalResult: '/package-quick-receipt/pages/retrieval/retrieval-result/index', // 事后调单结果页
  RetrievalUploadMaterial: '/package-quick-receipt/pages/retrieval/upload-materials/index', // 事后调单上传材料页
  errPage: '/package-quick-receipt/pages/err-page/index', // 错误提示页面
  wechatExchangeExplain: '/package-quick-receipt/pages/wechat-exchange-explain/index', // 零钱收款限制范围说明页
  gpcReceiveRemittanceOrderRouterPage: '/package-gpc-uni/pages/receive-remittance/order-router-page/index', // gpc 项目下的 uniapp 框架开户流程页
};
// 小鹅快收页面
export const PAGE = { ...MainPage, ...ProtocolPage };

export const WebViewPage = {
  institutionList: 'https://weremit.tenpay.com/fastreceipt/index.html',
};

// 模板消息id
// eslint-disable-next-line @typescript-eslint/naming-convention
export enum TEMPLATE_IDS {
  RETRIEVAL_UPLOAD = '6MeK3VVLVTctzDw0is3CI8ZgpBbAL6V_fxZofyIwWsw',
  RETRIEVAL_FAIL = 'u7J3UHygoFDUGyhs2-gz11jN1bgPg9x5s5P70yXsIsc',
  RETRIEVAL_SUCCESS = 'kKICkmvTM2UYDND1c4GcCbaUSepOxNUaxNHTE6irUBw',
}

// 根据模版消息id获取名称
export const getAuthTemplateName = (value: string): string => {
  switch (value) {
    case TEMPLATE_IDS.RETRIEVAL_UPLOAD:
      return '重新提交证明材料通知';
    case TEMPLATE_IDS.RETRIEVAL_FAIL:
      return '证明材料审核失败通知';
    case TEMPLATE_IDS.RETRIEVAL_SUCCESS:
      return '证明材料审核成功通知';
  }
  return '';
};

export const RELATIONSHIP_LIST = [
  {
    name: '配偶',
    value: 10,
  },
  {
    name: '父母',
    value: 20,
  },
  {
    name: '子女',
    value: 30,
  },
  {
    name: '兄弟姐妹',
    value: 40,
  },
  {
    name: '祖父母/外祖父母',
    value: 50,
  },
  {
    name: '子孙女/外子孙女',
    value: 60,
  },
];

// 调单类型
export enum DispatchBusType {
  InOrder = 'InOrder', // 事中调单
  AfterOrder = 'AfterOrder',
}

// 按钮openType
export enum OpenType {
  CONTACT = 'contact'
}
