import aegis from '../../common/business/aegis';
import acc from '../../common/utils/acc';

export const speedReport = (key: Hk2cnTimePoint, startTime: number, endTime: number) => {
  if (typeof startTime === 'number'
    && typeof endTime === 'number'
    && startTime > 0
    && endTime > 0) {
    aegis.reportTime(key, endTime - startTime);
    acc.accLogReport({
      key,
      msg: '测速上报',
      timeCost: endTime - startTime,
    });
    return;
  }
  acc.accLogReport({
    key: 'speedReportErr',
    msg: '测速点不正确',
    num1: JSON.stringify({ key, startTime, endTime }),
  });
};

export enum Hk2cnTimePoint {
  /** 进入页面 */
  onInit = 'onInit',
  /** 完成订单校验 */
  checkSyncComplete = 'checkSyncComplete',
  /** 校验信息失败，展示错误页面 */
  showFailPage = 'showFailPage',
  /** 命中开户 */
  showOpenCard = 'showOpenCard',
  /** 开始轮询查单 */
  startPolling = 'startPolling',
  /** 轮询查单完成 */
  pollingComplete = 'pollingComplete',
  /** 查单完成，准备进入处理中页面 */
  navigateToResultPage = 'navigateToResultPage',
  /** 进入处理中页面 */
  resultPageOnload = 'resultPageOnload',
  /** 处理中页面展示 */
  resultPageShow = 'resultPageShow',
  /** 查单完成，去签协议 */
  navigateToSignContract = 'navigateToSignContract',
  /** 协议页面展示 */
  signContractPageShow = 'signContractPageShow',
  /** 查单完成，进入订单详情页 */
  navigateToDetailPage = 'navigateToDetailPage',
  /** 订单详情Onload展示 */
  detailPageOnload = 'detailPageOnload',
  /** 订单详情展示 */
  detailPageShow = 'detailPageShow',
}
