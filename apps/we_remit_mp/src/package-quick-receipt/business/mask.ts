export enum MaskType{
  name,
  name_en,
  phone,
}

// 掩码替换字符
const maskReplaceStr = '*';

/**
   * 获取特定长度的同一字符的字符串
   * @param length 特定长度
   * @param str 同一字符
   * @returns 特定长度的同一字符的字符串
   */
function getReplaceStrs(length: number, str: string) {
  return Array.from({ length }).map(() => str)
    .join('');
}

type MaskItemFun = (content: string, pattern?: string) => string;
/**
 * 转掩码策略对象
 */
const maskMap: Record<MaskType, MaskItemFun> = {
  /**
   * 中文名字转掩码
   * @param content 需要转码的内容
   * @param splitPattern 切割的匹配字符
   * @returns 转码后的内容
   */
  [MaskType.name](content: string, splitPattern = ''): string {
    const contentArr = content.split(splitPattern);
    const contentLen = contentArr.length;
    return contentArr.map((item, index) => {
      // 最后一个字显示明文
      if (index === contentLen - 1) {
        return item;
      }
      return maskReplaceStr;
    }).join('');
  },
  /**
   * 中文名字拼音转掩码
   * @param content 需要转码的内容
   * @returns 转码后的内容
   */
  [MaskType.name_en](content: string): string {
    return this[MaskType.name](content, ' ');
  },
  /**
   * 手机号码转掩码，规则：区号不掩码，除区号外的手机号倒数第5位开始展示4位掩码，其他展示明文，例：+86 13235640975，展示 +86 132****0975。
   * @param content 需要转码的内容，例如：+86 13235640975，后端用空格分隔区号，若无区号，则不展示区号
   * @returns 转码后的内容
   */
  [MaskType.phone](content: string): string {
    // 尾部展示长度
    const showEndLen = 4;
    // 掩码长度
    const maskLen = 4;
    let areaCode = ''; // 区号
    let formateContent = content.replace(/^\s+|\s+$/g, ''); // 去掉头尾的空格
    const splitArr = formateContent.split(' '); // 使用空格分隔字符串，分出区号和手机号
    if (splitArr.length > 1) {
      // 若有区号，拆分区号和手机号
      [areaCode] = splitArr;
      formateContent = formateContent.replace(`${areaCode} `, '');
    }
    // 不掩码的前几位
    const startStr = formateContent.substring(0, formateContent.length - showEndLen - maskLen);
    // 需要掩码的数字
    const endStr = formateContent.slice(startStr.length);
    return `${areaCode} ${startStr}${getReplaceStrs(endStr.length - showEndLen, maskReplaceStr)}${endStr.slice(-showEndLen)}`.replace(/^\s+|\s+$/g, '');
  },
};

/**
 * 掩码显示状态
 */
export enum MaskShowStatus {
  HIDDEN = 0, // 不显示掩码
  SHOW = 1, // 显示掩码
}

/**
 * 掩码小图标
 */
export const maskIcons = {
  [MaskShowStatus.HIDDEN]: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/quick-receipt/receipt-card/mingwen.svg',
  [MaskShowStatus.SHOW]: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/quick-receipt/receipt-card/yanma.svg',
};

/**
 * 对数据进行掩码处理
 * @param notMaskStr 未掩码的数据
 * @returns 已掩码数据
 */
const mask = (notMaskStr: string, type: MaskType): string => {
  const maskFun = maskMap[type];
  if (typeof maskFun === 'function') {
    return maskFun.call(maskMap, notMaskStr);
  }
  throw new Error('Unsupport MaskType');
};

export {
  mask,
};
