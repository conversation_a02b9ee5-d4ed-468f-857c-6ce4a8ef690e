import { objToQueryType } from '../../common/utils/util-v2';
import { PAGE } from '../configs/constant';

// 错误页埋点上报
export interface ErrPageReportEvent {
  key: string;
  params?: Record<string, string|number|Record<string, string>>
}

// 按钮类型
export enum BtnType {
  CloseMinipro = 'CloseMinipro', // 关闭小程序
  Relaunch = 'Relaunch', // 重启小程序
  Redirect = 'Redirect', // 重定向跳转
  Navigate = 'Navigate', // 跳转
  CustomService = 'CustomService', // 跳转到联系客服
  SwitchTab = 'SwitchTab'
}

// 到错误页的参数
export interface ToErrPageOptions extends Record<string, string|BtnType|ErrPageReportEvent|undefined>{
  title: string; // 通用错误页大标题
  desc?: string;
  entrance?: string; // 来源页面，兜底取getPrePageName()
  btnText?: string; // 按钮文案，兜底“回首页”
  btnType?: BtnType; // 按钮类型
  btnClickUrl?: string; // 按钮点击跳转
  onShowReportEvent?: ErrPageReportEvent; // 页面浏览埋点上报事件
  onBtnClickReportEvent?: ErrPageReportEvent; // 页面按钮埋点上报事件
}

// 跳转到错页面
export const getErrPageOptionsUrl = (options: ToErrPageOptions): string => `${PAGE.errPage}?${objToQueryType(options as Record<string, string | Record<string, string>>)}`;
