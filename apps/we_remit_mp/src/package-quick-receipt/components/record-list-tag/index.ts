import { getRecordListTag } from '../../business/prom/index';
import { CustomException } from '../../../common/utils/error';
import { quickReceiptBiReport } from '../../business/bi-report';
import { getPrePageName } from '../../../common/utils/page-util';

Component({
  data: {
    text: '',
  },
  lifetimes: {
    attached() {
      this.getTag();
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    async getTag() {
      // 获取唯一命中的小鹅收款记录页挂件配置
      const tagConfig = await getRecordListTag();
      // 如果没有命中活动，则不需要处理
      if (!tagConfig) {
        return;
      }
      const { text } = tagConfig.promComponent;
      if (!text) {
        new CustomException(tagConfig.promComponent, 'recordListTag_configErr', '小鹅订单记录页活动标签配置错误');
        return;
      }
      const activityId = tagConfig.promInfo.ext?.id;
      const { activityKey } = tagConfig.promInfo;
      quickReceiptBiReport('weremit.fastreceipt.records.activity_tag_brow', {
        activityId,
        activityKey,
        ...tagConfig.biReportExtParams,
        entrance: getPrePageName(),
      });
      this.setData({
        text,
      });
      return;
    },
    /**
     * 上报点击事件
    */
    async clickReport() {
      quickReceiptBiReport('weremit.fastreceipt.records.activity_tag_click', {
        activityId: this.data.activityId,
        activityKey: this.data.activityKey,
        ...this.data.biReportExtParams,
        entrance: getPrePageName(),
      });
    },
  },
});
