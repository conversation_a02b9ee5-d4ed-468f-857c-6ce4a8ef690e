/**
 * 按钮类型
*/
export type ButtonList = Array<{
  // 按钮文案
  text: string,
  // 按钮类型
  type?: 'customService' | 'default'
  // 按钮主题
  theme?: 'green' | 'default',
  // 按钮回调函数
  callback?: () => void,
}>;

/**
 * 组件data
 */
type ComponentData = {
  // 组件data
};

interface ComponentProperty{
  /**
   * 是否隐藏
   */
  isShowModal: boolean;
  /**
   * 弹窗文本内容
  */
  modalContent: string;

  /**
   * 按钮组
  */
  buttons: ButtonList,
}

type ComponentPropertyOptions = WechatMiniprogram.Component.PropertyOption & {
  [key in keyof ComponentProperty]: WechatMiniprogram.Component.FullProperty<WechatMiniprogram.Component.PropertyType>;
};

/**
 * 组件方法
*/
type MethodOptions={
  onMaskTap: () => void;
  onButtonTap: (e: WechatMiniprogram.CustomEvent) => void;
};

// 弹窗组件逻辑
Component<ComponentData, ComponentPropertyOptions, MethodOptions>({
  properties: {
    isShowModal: {
      type: Boolean,
      value: false,
    },
    modalContent: {
      type: String,
      value: '',
    },
    buttons: {
      type: Array,
      value: [],
    },
  },
  methods: {
    /**
     * 蒙层点击事件
    */
    onMaskTap() {
      this.triggerEvent('clickMask');
    },

    // 点击按钮事件
    onButtonTap(e: WechatMiniprogram.CustomEvent) {
      const { index } = e.currentTarget.dataset;
      const button = this.properties.buttons[index];
      typeof button?.callback === 'function' && button?.callback();
    },
  },
});
