<view class="relation-ship-modal {{ show ? 'show' : '' }}">
  <view class="content" style="bottom: {{ headerHeight / 2 }}rpx">
    <view class="content-top">
      <view class="text1">你当前选择与汇款人的关系为：</view>
      <view class="text1">
        <text class="text2">「{{ relationShipText }}」</text>
        <text>，系统识别上述选择可能有误，请确认</text>
      </view>
      <view class="text3">注：关系一旦确认，不可修改，选择错误可能导致收款失败</view>
    </view>
    <view class="content-bottom">
      <view class="button-item button-item-cancel" bind:tap="cancel">
        <picker
          data-item="{{item}}"
          data-prop="{{item.prop}}"
          value="{{relationIndex}}"
          range="{{relationsList}}"
          range-key="name"
          bind:change="onPickRealtionship"
        >重新选择
        </picker>
      </view>
      <view class="button-split-line" />
      <view class="button-item button-item-confirm" bind:tap="confirm">确定</view>
    </view>
  </view>
</view>
