import { ComponentWithComputed } from '@tencent/pdd-miniprogram-computed';
import { ActivityCacheKey, getGuideShowStatus, GuideStatus, isGuideShowStatusCache, setGuideShowStatus } from '../../business/global-cache';
import { objectToKVString } from '../utils/style';
/**
 * 组件data
 */
export type ComponentData = {
  cacheStatus: GuideStatus;
  /**
   * 默认样式
   */
  style: AnyObject;
};

/**
 * 组件属性
 */
export type ComponentProperty = {
  /**
   * 隐藏属性
   */
  hidden: boolean;
  /**
   * 引导icon资源链接
   */
  guideImageurl: string;
  /**
   * 引导信息
   */
  guideMessage: string | string[];
  /**
   * 是否缓存
   */
  cache: boolean;
  /**
   * 缓存key
   */
  cacheKey: ActivityCacheKey;
  /**
   * 用户自定义样式
   */
  customStyle: AnyObject;
};


/**
 * 组件方法
 */
export type ComponentMethod = {
  touchstart: (e: AnyObject) => void;
  checkCache: () => void
};

/**
 * 组件watch
 */
export type ComponentWatch = Record<string, (...args: unknown[]) => void>;

export type ComponentComputedDataType = ComponentData & ComponentProperty & {
  [key in keyof ComponentComputed]: ReturnType<ComponentComputed[key]>;
};

export type ComponentComputedMethodType<Type = unknown> = (data: ComponentComputedDataType) => Type;

/**
 * 组件computed
 */
export type ComponentComputed =  {
  getGuideMessage: ComponentComputedMethodType<string>;
};

export type ComponentComputedOptions = Record<string, (data: ComponentData & {
  [K in keyof ComponentProperty]: ComponentProperty[K];
}) => unknown>;

type ComponentPropertyOptions = WechatMiniprogram.Component.PropertyOption & {
  [key in keyof ComponentProperty]: WechatMiniprogram.Component.FullProperty<WechatMiniprogram.Component.PropertyType>;
};


// eslint-disable-next-line max-len
ComponentWithComputed<ComponentData, ComponentPropertyOptions, ComponentMethod, ComponentWatch, ComponentComputedOptions>({
  externalClasses: ['guide-icon', 'guide-image', 'guide-text', 'guide-text-item'],
  options: {
    multipleSlots: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 隐藏属性
     */
    hidden: {
      type: Boolean,
      value: false,
    },
    guideImageurl: {
      type: String,
      value: '',
    },
    guideMessage: {
      type: String,
      optionalTypes: [Array, String],
      value: '',
    },
    cache: {
      type: Boolean,
      value: false,
    },
    cacheKey: {
      type: String,
      value: '',
    },
    customStyle: {
      type: Object,
      value: {},
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    cacheStatus: GuideStatus.SHOW,
    style: {},
  },

  computed: {
    isGuideMessageIsArray(data) {
      return Array.isArray(data.guideMessage);
    },
    getGuideMessage(data) {
      return data.guideMessage;
    },
    isHidden(data) {
      console.log('isHidden', data.cacheStatus === GuideStatus.HIDDEN || data.hidden);

      return data.cacheStatus === GuideStatus.HIDDEN || data.hidden;
    },
    defaultStyle(data) {
      const { style, customStyle } = data;
      return objectToKVString(Object.assign(
        // 样式容器
        {},
        // 默认样式
        style,
        // 调用方自定义样式
        customStyle,
      ));
    },
  },


  /**
   * 组件的方法列表
   */
  methods: {
    touchstart(e) {
      const event = {
        __event: e,
      };
      Object.assign(event, e.detail);
      const { cache, cacheKey }  = this.properties;
      if (cache && !cacheKey) {
        this.setData({
          cacheStatus: GuideStatus.HIDDEN,
        });
        throw new Error('开启缓存时，请设置缓存key，即设置cacheKey属性，详情请看组件内部实现');
      } else if (cache) {
        setGuideShowStatus(this.properties.cacheKey, GuideStatus.HIDDEN);
      }
      this.triggerEvent('touchClose', event);
    },
    checkCache() {
      const { cache, cacheKey }  = this.properties;
      if (cache && !cacheKey) {
        this.setData({
          cacheStatus: GuideStatus.HIDDEN,
        });
        throw new Error('开启缓存时，请设置缓存key，即设置cacheKey属性，详情请看组件内部实现');
      } else if (cache && isGuideShowStatusCache(cacheKey)) {
        this.setData({
          cacheStatus: getGuideShowStatus(cacheKey),
        });
      }
    },
  },

  lifetimes: {
    attached() {
      this.checkCache();
    },
  },
});
