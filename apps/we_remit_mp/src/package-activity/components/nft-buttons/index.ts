import { ComponentWithComputed } from '@tencent/pdd-miniprogram-computed';
import { handleJumpUrl } from '../../../common/business/remit';
import { navigateTo, URL_TYPE } from '../../../common/utils/util-v2';
import CONSTANT from '../../../datas/configs/constant';
import { nftCardPageArticleUrl, PAGE } from '../../configs/constant';
import { activityBiReport } from '../../business/bi-report';

ComponentWithComputed({
  options: {
    multipleSlots: true,
  },
  properties: {
    /**
     * 是否为本人，默认为本人
     * 通过分享卡进入到分享展示页则为非本人
     */
    isSelf: {
      type: Boolean,
      value: true,
    },
    // 是否已经领取过nft卡片，用于分享卡进入区分展示
    isHasNftCard: {
      type: Boolean,
      value: false,
    },
  },
  methods: {
    whereToFind() {
      // 事件上报 - nft活动-藏品详情页【以后在哪找】点击量
      activityBiReport('wetuition.miniapp_nft.nftdetail.wheretofind_click');
      handleJumpUrl(
        URL_TYPE.H5,
        { url: nftCardPageArticleUrl() },
        CONSTANT.PAGE.webView,
      );
    },
    shareNft() {
      // 事件上报 - nft活动-藏品详情页【转发分享】点击量
      activityBiReport('wetuition.miniapp_nft.nftdetail.share_click');
    },
    onClose() {
      // 事件上报 - nft活动-藏品详情页【返回活动页】点击量
      activityBiReport('wetuition.miniapp_nft.nftdetail.goback_click');
      this.triggerEvent('onClose');
    },
    goHome() {
      // 事件上报 - nft活动-藏品分享页【留学缴费首页】点击量
      activityBiReport('wetuition.miniapp_nft.nftsharepage.gohome_click', {
        isHasNftCard: this.data.isHasNftCard,
      });
      wx.reLaunch({
        url: PAGE.tuiIndex,
      });
    },
    goActivity() {
      // 事件上报 - nft活动-藏品分享页【了解活动】点击量
      activityBiReport('wetuition.miniapp_nft.nftsharepage.goactivity_click');
      navigateTo({
        url: PAGE.receiptCard,
      });
    },
    goMyCard() {
      // 事件上报 - nft活动-藏品分享页【我的藏品】点击量
      activityBiReport('wetuition.miniapp_nft.nftsharepage.gomycard_click');
      navigateTo({
        url: PAGE.receiptCard,
      });
    },
    generatePoster() {
      this.triggerEvent('onGeneratePoster');
      // 点击生成海报事件上报
      activityBiReport('wetuition.miniapp_nft.nftdetail.getposter_click');
    },
  },
});
