<page-meta page-style="overflow: {{pageOverflowHidden ? 'hidden' : 'visible'}};background-color: #000000;min-height: 100vh;" />
<view class="activity" hidden="{{!isTopViewItemShow}}">
  <!-- 上半屏 -->
  <view class="top">
    <!-- <rule-popup class="ruleButton" popupVisible="{{rulePopupVisible}}" bindclose="onCloseRulePopup" /> -->
    <!-- 活动已开始/活动已结束/奖品已发完/奖品已领取 -->
    <view class="top-activity-first-screen" hidden="{{!activityStartEndFinishedAccept}}">
      <view class="rules-button" catch:tap="openRulePopup"></view>
      <image class="activity-background" src="{{images.topBackground.IN_PROGRESS}}" bindload="loadTopBackgroundImage" />
      <!-- 立即参与置灰按钮/立即参与按钮/活动已结束按钮/藏品已发完按钮/查看藏品按钮 -->
      <block wx:for="{{images.topActivityFirstScreen}}" wx:key="state">
        <activity-button id="viewButton" disabledDelay="{{1700}}" isOpenDefaultDisabled="{{item.isOpenDefaultDisabled}}" hidden="{{!isActivityOpen || item.state !== activityStatus}}" class="activity-button" type="image" openType="{{item.openType}}" backgroundImage="{{item.url}}" catch:getPhoneNumber="participateNow" catch:click="participateNow" />
      </block>
      <activity-button hidden="{{isActivityOpen}}" class="activity-button" type="image" backgroundImage="{{images.activityClose}}" catch:click="participateNow" />
    </view>
    <!-- 用户已完成任务，奖品待发放 -->
    <view class="ready-to-accept" hidden="{{!readyToAccept}}">
      <view class="rules-button" catch:tap="openRulePopup"></view>
      <image class="activity-background" src="{{images.topBackground.READY_TO_ACCEPT}}" bindload="loadTopBackgroundImage" />
      <view class="box-container">
        <view class="swiper-container" style="{{boxAnimation}}">
          <swiper class="box-swiper" current="{{currentBox}}" indicator-dots="{{false}}" autoplay="{{false}}" duration="{{200}}" circular="{{true}}" bindchange="onSwiperChange">
            <block wx:for="{{boxImageList}}" wx:key="index">
              <swiper-item catchtouchmove="{{canSwiper?'':'catchmove'}}" bindtouchstart="changeBoxSwiperTouchstart" bindtouchend="changeBoxSwiperTouchend">
                <view class="box-animation">
                  <bounce-ani wx:if="{{showBounce}}" bind:bounceend="bounceComplete" isStart="{{isStartBounce && currentBox === index}}" class="bounce-comp ani-comp" originWidth="450" originHeight="525" boxColor="{{boxColor}}" bindload="onBounceImgLoad">
                    <image class="box-image" src="{{item.src}}" />
                  </bounce-ani>
                </view>
              </swiper-item>
            </block>
          </swiper>
        </view>
      </view>
      <!-- 盲盒切换遥感按钮 -->
      <view class="box-change-button">
        <image class="box-telelever" style="{{boxTeleleverAnimation}}" catch:tap="changeBox" src="{{images.telelever}}" />
        <image class="box-cushion" style="{{boxCushionAnimation}}" src="{{images.cushion}}" />
      </view>
      <!-- 进度 -->
      <view class="speed-of-progress"></view>
      <!-- 开启盲盒按钮 -->
      <activity-button hidden="{{!isTopViewItemShow}}" id="openBox" isOpenDefaultDisabled="true" width="352rpx" height="88rpx" class="activity-button" type="image" backgroundImage="{{images.readyToAcceptButton}}" catch:click="openBox" />
      <activity-openbox-guide hidden="{{isActivityOpenboxGuideShow}}" guideImageurl="{{openBoxGuideConfig.guideImageurl}}" guide-image="activity-openbox-guide-icon" guideMessage="{{openBoxGuideConfig.guideMessage}}" bind:touchClose="activityOpenboxGuideTouchClose" />
    </view>
  </view>
  <!-- 下半屏 -->
  <view class="bottom">
    <image class="bottom-background" src="{{images.bottomBackground}}" bindload="loadBottomBackgroundImage" />
    <!-- 查看详情 -->
    <activity-button hidden="{{!isBottomViewItemShow}}" class="activity-button" jumpUrl="{{viewDetailArticleUrl}}" type="default" bind:click="getDetail">
      查看详情
    </activity-button>
  </view>
  <!-- 解决ios可以滑动下半屏出现色差问题 -->
  <view class="fix-screen-chromatic-aberration"></view>
  <!-- 规则弹窗，需检查之 -->
  <rule-popup popupVisible="{{rulePopupVisible}}" bindclose="onCloseRulePop" />
  <!-- 用户无领奖规则弹窗，需检查之 -->
  <ineligible-popup popupVisible="{{ineligiblePopupVisible}}" bindclose="onCloseIneligiblePop" />
  <!-- 动画组件 -->
  <animation bind:qryend="onQryEnd" class="animation" boxColor="{{boxColor}}" bindqryPrizeEnd="dealQryPrizeEnd" bind:bounceImgLoad="onBounceImgLoad" bindaniend="onAniEnd" bind:qryPrizeErr="onQryPrizeErr" bind:nftPopupClose="onNftPopupCloseAfterOpenBox"></animation>
  <!-- nft弹窗 -->
  <view hidden="{{!showNftPopup}}" class="nft-pop-container" wx:if="{{accepted}}">
    <enlarge-ani class="nft-enlarge-comp" duration="{{300}}">
      <nft-popup class="nft-popup" initKey="{{initNftPopupKey}}" showKey="{{showNftPopup}}" delayKey="{{false}}" bind:onClose="onNftPopupClose" />
    </enlarge-ani>
  </view>
</view>
<!-- 用户信息授权 -->
<user-info class="user-info"></user-info>