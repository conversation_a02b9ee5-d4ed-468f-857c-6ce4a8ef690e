/**
 * 分包异步化方法
 */
import type { PromPkg } from '../../package-util/business/prom/prom';
import type { PromDataPkg } from '../../package-util/business/prom/prom-data';
import type { PromUtilPkg } from '../../package-util/business/prom/prom-util';
import type { ChainPkg } from '../../package-util/business/designMode/chain';
import type { StylePkg } from '../../package-util/util/style';
import type { UserInfoPkg } from '../../package-util/util/user-info';
import type { HkShuangDanRouterPkg } from '../../package-activity-hkShuangDan/router/index';
import type { QuickReceiptRecordDetailRouterPkg } from '../../package-quick-receipt/router/record-detail';
import { CustomException } from './error';
import type { HkSpringFestivalPkg } from '../../package-activity-hkSpringFestival/business/pkg';

// 分包异步化错误返回
interface QueryOtherPackacgeErr{
  mod: string,
  errMsg: string,
}

/**
 * 封装分包异步化调用
 * 由于异步化调用传入的url为相对路径，此处只能传入项目绝对路径（以src开头），由方法内部替换
 * @example const [res, err] = await to(queryOtherPackacge('src/package-activity/business/activity'))
 */
const requirePackage = <T = unknown>(url: RequirePathEnum) => new Promise<T>((resolve, reject) => {
  let relativeUrl = '';
  // 仅替换第一个src，将绝对路径改为相对路径
  relativeUrl = url.replace('src', '../..');
  require.async<T>(relativeUrl).then((pkg: T) => {
    resolve(pkg);
  })
    .catch((err: QueryOtherPackacgeErr) => {
      reject(`path ${err?.mod || relativeUrl}, ${err?.errMsg}`);
      new CustomException(err, 'queryOtherPackacge_err', `分包异步化调用path ${err?.mod || relativeUrl}, ${err?.errMsg}失败`);
    });
});

/**
 * 分包路径枚举
*/
enum RequirePathEnum{
  /**
   * 微汇款工具包
  */
  UTIL_STYLE = 'src/package-util/util/style',
  /**
   * 微汇款公共部分活动业务层business
  */
  BUSINESS_PROM = 'src/package-util/business/prom/prom',
  /**
   * 微汇款公共部分活动数据层business
  */
  BUSINESS_PROM_DATA = 'src/package-util/business/prom/prom-data',
  /**
   * 微汇款公共部分活动通用business
  */
  BUSINESS_PROM_UTIL = 'src/package-util/business/prom/prom-util',
  /**
   * 微汇款设计模式-职责链分包
  */
  BUSINESS_DESIGN_MODE_CHAIN = 'src/package-util/business/designMode/chain',
  /**
   * 用户信息授权
  */
  USER_INFO = 'src/package-util/util/user-info',

  /**
   * 港陆双旦活动分包
  */
  HK_SHUANG_DAN_ROUTER = 'src/package-activity-hkShuangDan/router/index',

  /**
   * 小鹅订单详情页路由分包
  */
  QUICK_RECEIT_RECORD_DETAIL_ROUTER = 'src/package-quick-receipt/router/record-detail',

  /**
   * 港陆春节活动分包
   */
  HK_SPRING_FESTI = 'src/package-activity-hkSpringFestival/business/pkg',
}


/* 对外抛出的请求分包方法，内部封装了请求的文件路径和类型声明 */

/**
 * 请求微汇款公共部分业务活动business
*/
export const requirePromPkg = () => requirePackage<PromPkg>(RequirePathEnum.BUSINESS_PROM);

/**
 * 请求微汇款活动数据层分包
*/
export const requirePromDataPkg = () => requirePackage<PromDataPkg>(RequirePathEnum.BUSINESS_PROM_DATA);

/**
 * 请求微汇款bi上报分包
*/
export const requirePromUtilPkg = () => requirePackage<PromUtilPkg>(RequirePathEnum.BUSINESS_PROM_UTIL);

/**
 * 请求微汇款样式分包util
*/
export const requireStylePkg = () => requirePackage<StylePkg>(RequirePathEnum.UTIL_STYLE);

/**
 * 请求微汇款职责链分包
*/
export const requireChainPkg = () => requirePackage<ChainPkg>(RequirePathEnum.BUSINESS_DESIGN_MODE_CHAIN);

/**
 * 用户信息授权
 * @returns
 */
export const requireUserInfoPkg = () => requirePackage<UserInfoPkg>(RequirePathEnum.USER_INFO);

/**
 * 港陆双旦活动路由
*/
export const requireHkShuangDanRouter = () => requirePackage<
HkShuangDanRouterPkg>(RequirePathEnum.HK_SHUANG_DAN_ROUTER);

/**
 * 港陆春节活动分包
*/
export const requireHkSpringFesModule = () => requirePackage<
HkSpringFestivalPkg>(RequirePathEnum.HK_SPRING_FESTI);

/**
 * 小鹅路由分包
*/
export const requireQuickReceiptRecordDetailRouter = () => requirePackage<
QuickReceiptRecordDetailRouterPkg>(RequirePathEnum.QUICK_RECEIT_RECORD_DETAIL_ROUTER);

/**
 * 另外此分包异步化文件也在膨胀
 * 解决方案：
 * 1、对此文件进行拆分
 * 2、封装类型声明MAP，减少内部封装函数调用的工作量
*/
