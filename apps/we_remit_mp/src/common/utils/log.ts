/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * 日志信息
 * <AUTHOR>
 * @date 20180910
 */
import { G_LOG } from '../../configs/index';
// import aegis from '../business/aegis';
import { elk } from './elk';
const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null;

export enum AegisLogType{
  WHITE_INFO = '1',
  INFO = '2', // 白名单上报
  JS_ERROR = '4', // js error上报
  PROMISE_ERROR = '8',  // promise error上报
  AJAX_ERROR = '16',  // ajax error上报
  REPORT = '2048', // report
  CUSTOM_REPORT = '8192' // 自定义事件
}

export const rtlog = {
  info(...args: any) {
    if (!log || G_LOG.CURRENT_LOG_LEVEL < G_LOG.LOG_LEVEL.INFO) return;
    console.info.apply(log, args);
    log.info(args);
    elk({ str4: 'info', msg: args });
  },
  warn(...args: any) {
    if (!log || G_LOG.CURRENT_LOG_LEVEL < G_LOG.LOG_LEVEL.WARN) return;
    console.warn.apply(log, args);
    log.warn(args);
    elk({ str4: 'warn', msg: args });
  },
  error(...args: any) {
    if (!log || G_LOG.CURRENT_LOG_LEVEL < G_LOG.LOG_LEVEL.ERROR) return;
    console.error.apply(log, args);
    log.error(args);
    // args第一位是name，作为自定义上报事件的name
    // aegis.reportEvent({
    //   name: args[0],
    //   msg: args,
    // });
    elk({ str4: 'error', msg: args, level: AegisLogType.CUSTOM_REPORT });
  },
  setFilterMsg(msg: any) {
    // 从基础库2.7.3开始支持
    if (!log || !log.setFilterMsg) return;
    if (typeof msg !== 'string') return;
    log.setFilterMsg(msg);
  },
  addFilterMsg(msg: any) {
    // 从基础库2.8.1开始支持
    if (!log || !log.addFilterMsg) return;
    if (typeof msg !== 'string') return;
    log.addFilterMsg(msg);
  },
};

export default rtlog;
