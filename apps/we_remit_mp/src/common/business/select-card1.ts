import { getPreCardSession } from '../../datas/cgi/remit';
import { MINIPRO, PreCardPara, PRE_CARD_PARA } from '../../datas/configs/constant';
import { CustomException } from '../utils/error';
import { hideLoading, showLoading, to } from '../utils/util-v2';
import log from '../utils/log';
import { dealCommonErr } from './remit';
import { BaseRes } from '../utils/request-base';

export interface SelectBankInfo {
  bank_name: string; // 银行名
  bankacc_type_name: string; // 银行卡类型：储蓄卡 | 信用卡
  bind_tail: string; // 银行卡尾号
  bank_logo_url: string; // 银行卡logo
}

export interface SupportBanks {
  supportBankNames?: string; // 支持的账户类型，格式为竖线分隔：CFT|DEBIT
  forbidBankNames?: string; // 需过滤的账户类型，格式为竖线分隔
}

/** *
 * @example 使用方法如下示例：
 *
 * 1、初始化选卡实例
 * const selectCardInstance = new SelectCard({
 *   afterSelect: (sessionId, bankInfo) => {
 *     // 选卡成功处理逻辑
 *   },
 *   navigateSuc: () => {
 *     // 跳转选卡组件成功处理逻辑，不必传
 *   },
 *   cancel: () => {
 *     // 取消选卡处理逻辑，不必传
 *     // 不传时，默认调用hideLoading
 *   },
 *   dealErr: (err) => {
 *     // 选卡流程报错处理逻辑，不必传
 *     // 不传时，默认调用hideLoading，并弹窗报错
 *   }
 * });
 * 2、在Page onShow() 调用选卡回调
 * Page({
 *   onShow() {
 *     selectCardInstance.selectBack();
 *   }
 * })
 */
export interface ToSelectCardOptions {
  supportBankNames?: string;
  forbidBankNames?: string;
  preCardPara?: PreCardPara;
}

export abstract class BaseSelectCard {
  // 选卡完成
  abstract afterSelect: (sessionId: string, bankInfo: SelectBankInfo) => void;

  // 跳转选卡成功
  abstract navigateSuc?: () => void;

  // 取消选卡
  abstract cancel?: () => void;

  // 选卡流程报错处理
  abstract dealErr?: (err: null | Error | BaseRes |  WechatMiniprogram.GeneralCallbackResult) => void;
}

export class SelectCard<T extends BaseSelectCard>  {
  public sessionId: null | string = null;
  private adapter: T;
  private isToSelectCard = false;
  constructor(adapter: T) {
    this.adapter = adapter;
  }
  /**
   * 去选卡
   * @params preCardPara 预选卡参数，不传使用默认值，默认为空（用于定义选卡组件内授权页的标题及描述文案）
   * @params supportBankNames 支持的账户，默认为空
   * @params forbidBankNames 禁用的账户，默认为空
   * @example 仅支持银行卡时，两个参数无需传值，均为空 { supportBankNames: '', forbidBankNames: ''}（为兼容留学、西联不传值）
   * @example 仅支持零钱时，{ supportBankNames: 'CFT', forbidBankNames: 'DEBIT'}
   * @example 支持零钱+银行卡时，{ supportBankNames: 'CFT|DEBIT', forbidBankNames: ''}
   */
  public async toSelectCard(options: ToSelectCardOptions = {
    supportBankNames: '',
    forbidBankNames: '',
    preCardPara: PRE_CARD_PARA,
  }): Promise<void> {
    showLoading();
    // 清除之前选卡带回的银行卡信息
    getApp().globalData.bank = null;
    const sessionId = await this.getSessionId(options);
    if (!sessionId) {
      return;
    }
    // 跳转选卡组件
    const miniProOptions = Object.assign(MINIPRO.SELECT_CARD, {
      extraData: {
        sessionid: sessionId,
      },
      success: (res: WechatMiniprogram.GeneralCallbackResult) => {
        log.info('跳转选卡组件success', res);
        this.isToSelectCard = true;
        wx.onAppShow((this.selectBackOnShow as WechatMiniprogram.OnAppShowCallback).bind(this));
        if (!this.adapter.navigateSuc) return;
        this.adapter.navigateSuc();
      },
      fail: (err: WechatMiniprogram.GeneralCallbackResult) => {
        new CustomException(err, 'toSelectCardFunc_err', '跳转到选卡组件失败');
        if (!this.adapter.dealErr) {
          // 若实例无处理错误的方法，此处统一弹窗报错处理
          dealCommonErr(err);
          return;
        }
        this.adapter.dealErr(err);
      },
      complete: (res: WechatMiniprogram.GeneralCallbackResult) => {
        log.info('跳转选卡组件complete', res);
      },
    });
    wx.navigateToMiniProgram(miniProOptions);
  }

  public getIsToSelectCard(): boolean {
    return this.isToSelectCard;
  }

  public selectBackOnShow(options: WechatMiniprogram.LaunchOptionsApp) {
    console.log('selectBackOnShow', options);
    if (options.scene === 1038 || options.scene === 1001) {
      if (!this.isToSelectCard) return;
      wx.offAppShow(this.selectBackOnShow.bind(this));
      console.log('撤销监听');
      if (options.referrerInfo
        && options.referrerInfo.appId === 'wxbd687630cd02ce1d'
        && options.referrerInfo.extraData) {
        if (!getApp().globalData) {
          getApp().globalData = {};
        }
        if (options.referrerInfo.extraData.errMsg === 'openSelectPayment:ok') {
          // 选择成功，获取银行卡信息
          getApp().globalData.bank = options.referrerInfo.extraData.card;
        } else {
          // 选择失败，清空银行卡信息，避免Android bug导致重复带入银行卡信息
          getApp().globalData.bank = null;
        }
      }
    }
  }

  /**
   * 选卡回来，在onshow调用
   */
  public async selectBack(): Promise<void> {
    // 当用户取消选卡
    if (!this.isToSelectCard) return;
    const { bank } = getApp().globalData;
    if (!bank) {
      if (!this.adapter.cancel) {
        log.info('select card cancel');
        hideLoading();
        return;
      }
      this.adapter.cancel();
      return;
    }
    // 选卡成功后
    this.adapter.afterSelect(
      this.sessionId as string,
      bank,
    );
    // 选卡成功后，清除globalData
    this.isToSelectCard = false;
    getApp().globalData.bank = null;
  }

  /**
   * 获取sessionId
   */
  public async getSessionId(options: ToSelectCardOptions): Promise<string | null> {
    const tempOptions = {
      support_bank_names: options?.supportBankNames || '',
      forbid_bank_names: options?.forbidBankNames || '',
    };
    const preCardParamTemp: PreCardPara = JSON.parse(JSON.stringify(options.preCardPara || PRE_CARD_PARA));
    const [err, res] = await to(getPreCardSession(Object.assign(preCardParamTemp, tempOptions)));
    if (err || !res || !res.sessionid) {
      new CustomException(err, 'toSelectCardFunc_err', '获取sessionId失败');
      if (!this.adapter.dealErr) {
        // 若实例无处理错误的方法，此处统一弹窗报错处理
        hideLoading();
        dealCommonErr(err);
        return null;
      }
      this.adapter.dealErr(err);
      return null;
    }
    this.sessionId = res.sessionid;
    return this.sessionId;
  }
}
