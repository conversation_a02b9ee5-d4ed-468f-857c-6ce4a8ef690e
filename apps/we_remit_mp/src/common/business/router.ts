
/**
 * 通用路由静态类，用于获取当前页面的url参数、路径和子包名字，需要注意的是，query所获取的值具有跨页面缓存的能力，以解决页面间参数频繁透传的问题
 * 对于query里面的scene，因为二维码场景只能设置这个值，所以会将scene的值安装url参数的方式进行拆解，与query合并在一起，优先使用scene的值
 *
 * @export
 * @class Router
 */
import { ChannelKey } from '../../configs/channel';
import { EventBus } from '../utils/eventbus';
import { objToQueryType } from '../utils/util-v2';
export { ChannelKey } from '../../configs/channel';
export type ChannelType = {
  [key in ChannelKey]?: string;
};
export type ChannelEventParams = {
  type: string,
  target: ChannelType
};
enum EventType {
  change='channel-change'
}
interface PageType{
  query: Record<string, string>,
  path: string,
  subPackage: string
}
export class Router {
  /**
   * 当前页面的原始页面信息，不包括透传过来的query信息，其中subPackage是path中第一个字符串
   *
   * @static
   * @type {({
   *     query: Record<string, string | undefined>,
   *     path: string,
   *     subPackage: string
   *   })}
   * @memberof Router
   */
  static currentPage: PageType = {
    query: {},
    path: '',
    subPackage: '',
  };
  /**
   * 内部静态变量，用于缓存query信息，以达到透传的目的
   *
   * @private
   * @static
   * @type ChannelType
   * @memberof Router
   */
  private static cacheChannel: ChannelType = {};
  /**
   * 内部静态变量，只缓存当前页面的数据，路由更新后会置空
   *
   * @private
   * @static
   * @type ChannelType
   * @memberof Router
   */
  private static cacheCurrntChannel: ChannelType = {};
  /**
   * 获取当前页面url参数，这些参数不会跨页面透传
   *
   * @readonly
   * @static
   * @memberof Router
   */
  public static getQuery(): Record<string, string|undefined>;
  public static getQuery(key: string): string;
  public static getQuery(key?: string): string|Record<string, string|undefined> {
    const { query } = Router.currentPage;

    if (key) {
      return query[key] || '';
    }
    return query;
  }
  /**
   * 获取渠道，优先获取当前页面设置的临时渠道（currentChannel），然后再取透传过来的渠道（channel）
   * 这个渠道一般是从query中满足ChannelKey规则的key中获取，然后进行透传
   * 为了解决小程序码只能使用scene的key值问题，
   * 还有一种情况是，如果key是scene，则会解码后按照url参数（形如：scene=tuition%3Dysh_link_00001）的方法解析scene的值，
   * 并且key后面会加上Channel，与query的渠道命名规则保持一致，比如 scene=tuition%3Dysh_link_00001会解析成 {tuitionChannel:ysh_link_00001}
   * 然后与query进行合并（优先使用scene解析出来的值）
   *
   * @readonly
   * @static
   * @memberof Router
   */
  public static getChannel(): ChannelType;
  public static getChannel(key: ChannelKey): string;
  public static getChannel(key?: ChannelKey): string|ChannelType {
    const data = Router.cacheChannel || {};
    const currentData = Router.cacheCurrntChannel || {};
    const retData = Object.assign({}, data, currentData);
    if (key) {
      return retData[key] || '';
    }
    return retData;
  }
  /**
   * 设置currentData，这个数据只有在当前页面有效，路由更新后会重置
   *
   * @readonly
   * @static
   * @memberof Router
   */
  public static setCurrentChannel(data: ChannelType): void {
    const cacheData = Router.cacheCurrntChannel || {};
    Router.cacheCurrntChannel = Object.assign({}, cacheData, data);
    Router.disPatchChannelChange(cacheData, Router.cacheCurrntChannel);
  }
  /**
   * 路由变化时的监听
   *
   * @static
   * @param {(arg: ChannelType) => void} fn
   * @memberof Router
   */
  public static onChannelChange(fn: (arg: ChannelEventParams) => void): void {
    EventBus.addEventListener(EventType.change, fn, Router);
  }
  /**
   * 注销路由变化的监听
   *
   * @static
   * @param {(arg: ChannelKey) => void} fn
   * @memberof Router
   */
  public static offChannelChange(fn: (arg: ChannelEventParams) => void): void {
    EventBus.removeEventListener(EventType.change, fn, Router);
  }
  /**
   * 页面路由变化的时候调用，用于更新data的cache信息，保证透传的信息每次页面跳转都有更新
   * 同时会清空上一个页面的临时数据currentData
   * 一般在onAppRoute回调中使用
   *
   * @static
   * @returns
   * @memberof Router
   */
  public static changeByAppRoute(options?: WechatMiniprogram.App.LaunchShowOption): ChannelType {
    const { query } = Router.setPage(options);
    const channels = Router.unionChannelAndQuery(query);
    let cacheData = Router.cacheChannel || {};

    // 清空上一个页面的数据
    Router.cacheCurrntChannel = {};
    // 触发渠道更改事件
    Router.disPatchChannelChange(channels, cacheData);
    // 1. 如果url有参数，以url参数为准，并更新内存
    if (channels) {
      cacheData = Object.assign({}, cacheData, channels);
    }
    // 2. 如果url参数没有，从内存取
    Router.cacheChannel = cacheData;
    return Router.cacheChannel;
  }
  /**
   * 获取scene信息，会按照url参数方式解析里面的键值对
   *
   * @public
   * @static
   * @param {string} str
   * @returns {Record<string, string>}
   * @memberof Router
   */
  public static getUrlParams(str: string): Record<string, string> {
    const querys: Record<string, string> = {};
    // url解码
    const decodeStr = decodeURIComponent(str);
    const kvs = decodeStr.split('&');
    kvs.forEach((kv: string) => {
      const pair: string[] = kv.split('=');
      if (pair[0] && pair[1]) {
        [,querys[pair[0]]] = pair;
      }
    });

    return querys;
  }
  /**
   * 根据对应渠道是否有更改来触发更改事件
   *
   * @private
   * @static
   * @param {ChannelType} afterChannel
   * @param {ChannelType} beforeChannel
   * @memberof Router
   */
  private static disPatchChannelChange(afterChannel: ChannelType, beforeChannel: ChannelType) {
    const changeData: ChannelType = {};
    if (beforeChannel) {
      (Object.keys(beforeChannel) as ChannelKey[]).forEach((key) => {
        if (Object.values(ChannelKey).includes(key)) {
          // 保存改动的key值
          if (afterChannel[key] !== beforeChannel[key]) {
            changeData[key] = beforeChannel[key];
          }
        }
      });
    }
    if (Object.keys(changeData).length > 0) {
      // 触发事件
      EventBus.dispatch(EventType.change, changeData, Router);
    }
  }
  /**
   * 设置当前页面原始信息到currentPage中
   *
   * @private
   * @static
   * @returns
   * @memberof Router
   */
  private static setPage(options?: WechatMiniprogram.App.LaunchShowOption) {
    let currentPage: PageType = {
      query: {},
      path: '',
      subPackage: '',
    };
    if (options) {
      currentPage = {
        query: Object.assign({}, options.query),
        path: options.path,
        subPackage: options.path.split('/')[0],
      };
    } else {
      const pageList = getCurrentPages();
      const pageLen = pageList.length;
      const page = pageList[pageLen - 1];

      currentPage = {
        query: Object.assign({}, page.options) as Record<string, string>,
        path: page.route,
        subPackage: page.route.split('/')[0],
      };
    }
    // 对scene值进行特殊处理，因为小程序码场景的参数不能自定义key值，只能使用scene，而有时候我们可能需要传递多个key，这里使用url参数的处理方法
    if (currentPage.query.scene) {
      currentPage.query = Object.assign({}, Router.getChannelByScene(currentPage.query.scene), currentPage.query);
    }
    return Router.currentPage = currentPage;
  }
  /**
   * 获取scene里的渠道信息，会按照url参数方式解析里面的键值对，然后把key值后面加上Channel（主要为了避免scene值超过32个字符，但渠道的key需要是xxxChannel的方式）
   *
   * @private
   * @static
   * @param {string} str
   * @returns {Record<string, string>}
   * @memberof Router
   */
  private static getChannelByScene(str: string): Record<string, string> {
    const querys: Record<string, string> = this.getUrlParams(str);
    return Router.unionChannelAndQuery(querys);
  }
  /**
   * 只取出query中channelkey的值
   *
   * @private
   * @static
   * @param {Record<string, string>} query
   * @returns {Record<string, string>}
   * @memberof Router
   */
  private static unionChannelAndQuery(query: Record<string, string>): Record<string, string>  {
    const channels: Record<string, string> = {};
    Object.values(ChannelKey).forEach((key) => {
      if (query[key]) {
        channels[key] = query[key];
      }
    });
    return channels;
  }
}


/**
 * 记录小程序进入的入口
 */
let lauchRoute = '';
export const recordLaunchRoute = (options: WechatMiniprogram.App.LaunchShowOption): void => {
  lauchRoute = `/${options.path}?${objToQueryType(options.query as Record<string, string>, false)}`;
};

export const getLaunchRoute = (): string => lauchRoute;
