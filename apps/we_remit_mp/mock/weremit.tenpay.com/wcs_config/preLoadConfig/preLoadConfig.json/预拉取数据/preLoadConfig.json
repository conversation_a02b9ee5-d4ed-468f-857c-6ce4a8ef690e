{"entryGrey": {"fastReceipt": {"uinList": "o_vMs5IFiqfbdZLh-0klQ2iCkbfQ,o_vMs5I32432423432", "suffixList": null, "isFullUser": true}, "westernUnion": {"uinList": "o_vMs5IFiqfbdZLh-0klQ2iCkbfQ", "suffixList": "", "isFullUser": true}, "moneyGram": {"uinList": "", "suffixList": null, "isFullUser": true}, "tuition": {"uinList": "", "suffixList": null, "isFullUser": true}}, "entryInfo": {"receiptCollect": {"updateTime": "1611913131438", "operator": "leonw<PERSON><PERSON>", "relateData": null, "status": "1", "key": "receiptCollect", "homeBg": "../../common/images/entry/receipt.png", "homeTit": "全球收款", "homeDesc": "快速收款至境内", "homeUrl": "/pages/entry/receipt-entry", "routeTit": "", "routeDesc": null, "routeUrl": null, "routeLogo": null, "type": "业务集合", "sort": "0", "recordLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon_receipt.png", "recordTitle": "全球收款记录", "recordUrl": null}, "fastReceipt": {"updateTime": "1611913116059", "operator": "leonw<PERSON><PERSON>", "relateData": null, "status": "1", "key": "fastReceipt", "homeBg": "../../common/images/entry/receipt.png", "homeTit": "全球收款", "homeDesc": "快速收款至境内", "homeUrl": "/package-quick-receipt/pages/receipt-card/receipt-card", "routeTit": "小鹅快收", "routeDesc": "接收来自其他汇款机构的汇款", "routeUrl": "/package-quick-receipt/pages/receipt-card/receipt-card", "routeLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon-xiaoe.png", "type": "业务", "sort": "2", "recordLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon_receipt.png", "recordTitle": "全球收款记录", "recordUrl": "/package-quick-receipt/pages/records/record-list?from=entry", "recordTabName": "小鹅快收"}, "moneyGram": {"updateTime": "1611913106522", "operator": "leonw<PERSON><PERSON>", "relateData": null, "status": "1", "key": "moneyGram", "homeBg": "../../common/images/entry/receipt.png", "homeTit": "全球收款", "homeDesc": "快速收款至境内", "homeUrl": "/package-receipt/pages/code/code?showIntro=1&recCh=MoneyGram_boc", "routeTit": "速汇金(MoneyGram)收款", "routeDesc": "接收来自速汇金的汇款", "routeUrl": "/package-receipt/pages/code/code?recCh=MoneyGram_boc", "routeLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon-suhuijin.png", "type": "业务", "sort": "1", "recordLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon_receipt.png", "recordTitle": "全球收款记录", "recordUrl": "/package-receipt/pages/records/record-list/record-list?recCh=MoneyGram_boc&from=entry", "recordTabName": "速汇金收款"}, "westernUnion": {"updateTime": "1611913099654", "operator": "leonw<PERSON><PERSON>", "relateData": null, "status": "1", "key": "westernUnion", "homeBg": "../../common/images/entry/receipt.png", "homeTit": "全球收款", "homeDesc": "快速收款至境内", "homeUrl": "/package-receipt/pages/code/code?showIntro=1&recCh=WesternUnion_spdb", "routeTit": "西联(Western Union)收款", "routeDesc": "接收来自西联的汇款", "routeUrl": "/package-receipt/pages/code/code?recCh=WesternUnion_spdb", "routeLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon-xilian.png", "type": "业务", "sort": "0", "recordLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon_receipt.png", "recordTitle": "全球收款记录", "recordUrl": "/package-receipt/pages/records/record-list/record-list?recCh=WesternUnion_spdb&from=entry", "recordTabName": "西联收款"}, "tuitionCollect": {"updateTime": "1604563383632", "operator": "ileneyang", "relateData": null, "status": "1", "key": "tuitionCollect", "homeBg": "../../common/images/entry/tuition.png", "homeTit": "留学缴费", "homeDesc": "更便捷 更省心", "homeUrl": "/package-tuition/pages/index/index", "routeTit": null, "routeDesc": null, "routeUrl": null, "routeLogo": null, "type": "业务集合", "sort": "1", "recordLogo": null, "recordTitle": "留学缴费记录", "recordUrl": "/package-tuition/pages/records/record-list/record-list"}, "tuition": {"updateTime": "1604563356926", "operator": "ileneyang", "relateData": null, "status": "1", "key": "tuition", "homeBg": "../../common/images/entry/tuition.png", "homeTit": "留学缴费", "homeDesc": "更便捷 更省心", "homeUrl": "/package-tuition/pages/index/index", "routeTit": null, "routeDesc": null, "routeUrl": null, "routeLogo": null, "type": "业务", "sort": "0", "recordLogo": "https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon_tuition.png", "recordTitle": "留学缴费记录", "recordUrl": "/package-tuition/pages/records/record-list/record-list", "recordTabName": "留学缴费"}}, "weRemitMpEntry": [{"updateTime": "1605771040889", "operator": "ileneyang", "relateData": null, "status": "1", "question": "[{\"questionTitle\":\"什么是微汇款留学缴费？\",\"questionDetail\":[{\"detail\":\"微汇款留学缴费是腾讯微汇款面向中国大陆籍用户提供的留学汇款服务。\"}]},{\"questionTitle\":\"留学缴费需要支付手续费吗？\",\"questionDetail\":[{\"detail\":\"使用微汇款留学缴费服务每笔需支付150元手续费，包含服务费及学费全额到账手续费。\"}]},{\"questionTitle\":\"什么是西联收款？\",\"questionDetail\":[{\"detail\":\"西联收款是腾讯官方推出的跨境收款产品，用于收取来自西联（Western Union）的汇款。收款人无需前往西联线下网点办理收款业务，打开“西联收款”即可足不出户自助收款，无需任何手续费，资金即以人民币的形式入账选定银行卡。\"}]}]", "banner": "[]", "showIndexNotice": "0", "indexNoticeTitle": "", "indexNoticeShowNew": "0", "noticeList": "[]", "myConfig": "[{\"myTitle\":\"常用工具\",\"entryList\":[{\"name\":\"帮助中心\",\"img\":\"https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/entry/icon_help.svg?sign=684de7f08e0904bc088127ff21563eed&t=1594953594\",\"type\":\"otherMiniProgram\",\"url\":\"/pages/product/productInfo/product?code=A4793&producttitle=微汇款\",\"appId\":\"wxf75cfcbb5412db15\"}]}]"}]}