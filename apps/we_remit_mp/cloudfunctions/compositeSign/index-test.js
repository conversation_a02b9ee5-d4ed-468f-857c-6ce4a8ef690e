const fs = require('fs');
const { getInsertParamTemp, getTempConfig } = require('./compile-template');
const { refreshTemplate, preLoadTemplate } = require('./load-template');
const log = {
  info: (obj) => {
    console.log(obj);
  }
}

const inputFile = './tmp/test.svg';

preLoadTemplate();

let record = 0;

const svgToBase64 = async (str, config) => {
  if (config.useFonts) {
    const fonts = JSON.parse(config.useFonts);
    fonts.forEach(font => {
      console.log(`./fonts/${font.fileName}`, `${font.familyName}`);
    });
  }
  console.log(config.width, config.height);

  const svgStr = Buffer.from(str);
  fs.writeFileSync(inputFile, svgStr);
  return 'true';
};

const webApiRes = (data) => {
  if (!data) {
    return {
      "isBase64Encoded": false,
      "statusCode": 200,
      "headers": {"Content-Type":"text/javascript"},
      "body": ""
   };
  }
  return {
    "isBase64Encoded": false,
    "statusCode": 200,
    "headers": {"Content-Type":"text/javascript"},
    "body": data
 } 
}
const main = async (event, context) => {
// exports.main = async (event, context) => {
  log.info({
    key: 'event',
    msg: event
  });
  const isWebApi = !!event.headers && !!event.body;
  log.info({
    key: 'iswebapi',
    msg: isWebApi
  });
  record++;
  log.info({
    key: 'compositeSign record',
    msg: record,
  });
  const params = isWebApi ? JSON.parse(event.body) : event;
  log.info({
    key: 'compositeSign params',
    msg: params,
  });
  const content = await getInsertParamTemp(params.templateId, params);
  const config = await getTempConfig(params.templateId);
  refreshTemplate(config.templateId); // 异步更新模板文件
  const res = await svgToBase64(content, config);
  if (!res) {
    log.error({
      key: 'compositeSign error',
      msg: event
    });
    if (isWebApi) {
      return webApiRes(false);
    } else {
      return {
        status: 1
      };
    }
  }
  if (isWebApi) {
    return webApiRes(res);
  } else {
    return {
      status: 0,
      data: res
    };
  }
};

main({templateId: 'tuition_remittanceProof', pay_time: '2019-09-20 10:34:13', student_last_name: 'yang', student_first_name: 'yali', student_id: '10'});