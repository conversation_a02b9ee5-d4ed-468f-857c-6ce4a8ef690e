const { getTemplate, getTemplateConf, } = require('./load-template');

const promiseSignId = 'fastReceipt_promiseSign';

const getTemplateData = async (templateId) => {
  let id = '';
  if (!templateId) {
    // 兼容初始版本，签署承诺函未传templateId的调用，默认为promiseSignId
    id = promiseSignId;
  } else {
    id = templateId;
  }
  let res = await getTemplate(id);
  const config = await getTemplateConf(id);
  return {
    template: res,
    config
  }
}

const relationshipType = {
  10: '配偶',
  20: '父母',
  30: '子女',
  40: '兄弟姐妹',
  50: '祖父母，外祖父母',
  60: '子孙女，外子孙女'
};

/**
 * 获取模板配置信息
 * @param {*} templateId 
 * @returns 
 */
const getTempConfig = async (templateId) => {
  const { config } = await getTemplateData(templateId);
  return config;
}

/**
 * 获取替换变量后的模板
 * @param {*} templateId 
 * @param {*} params 
 * @returns 
 */
const getInsertParamTemp = async (templateId, params) => {
  const {template, config} = await getTemplateData(templateId);
  if (config.templateId === promiseSignId || config.templateId.indexOf(promiseSignId) > -1) {
    // 兼容承诺函旧的逻辑：处理亲属关系、签名图片、
    if (params.relationship) {
      params.relationship = `(${String(params.relationship).replace('0', '')}) ${relationshipType[params.relationship]}`;
    }
    if (params.sign) {
      params.sign = `data:image/png;base64,${params.sign}`;
    }
  }
  const regx = /{{([^}]*)}}/g;
  const matchs = template.match(regx);
  const data = {}; // 将缺少字段置为''
  let content = template;
  matchs.forEach(item => {
    const key = item.replace(/^{{|}}$/gm, '');
    data[key] = params[key] || '';
    content = content.replace(`${item}`, data[key]);
  });
  return content;
}

module.exports = {
  getInsertParamTemp,
  getTempConfig,
}
