// eslint-disable-next-line @typescript-eslint/no-require-imports
const getTestCase = require('../../../../../../test/base/data');

function mockOrder(options) {
  const baseData = {
    big_pay_finish_flag: '0',
    create_time: '2023-02-01 13:00:04',
    currency: 'CAD',
    currency_point_num: '2',
    currency_symbol: '$',
    edit_flag: '0',
    listid: '5002911117631202302018775660009',
    pay_amt: '20002',
    pay_share_flag: '0',
    refund_amt: '0',
    school_name_en: 'Columbia International College',
    state: '40050',
    student_name: '*江',
    tuition_amt: '0',
    user_tuition_amt: '10000',
  };
  return Object.assign({}, baseData, options, {
    edit_flag: options.edit_flag || '0',
    big_pay_finish_flag: options.big_pay_finish_flag || '0',
    school_name_en: `${options.$index}${baseData.school_name_en}${+options.$index === 0 ? '长文本长文本长文本长文本长文本长文本' : ''}`,
    listid: options.$index,
  });
}
module.exports = (ctx) => {
  const page = ctx.request.body.page_no;
  const testList = getTestCase();
  const hisList = [];
  let hasMore = true;
  const maxNum = 10;
  for (let i = maxNum * page;i < (maxNum * page + maxNum);i++) {
    const item = testList[i];
    if (!item) {
      hasMore = false;
      break;
    }
    const order = mockOrder({
      state: item.code,
      edit_flag: item.editFlag ? '2' : '0',
      $index: i,
    });
    hisList.push(order);
  }
  return {
    data: {
      retcode: '0',
      retmsg: 'ok',
      has_next_page: hasMore ? '1' : '0',
      his_list: hisList,
    },
  };
};
