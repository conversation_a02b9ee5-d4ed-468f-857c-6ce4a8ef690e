import { defineStore } from 'pinia';
import { useLogin } from './composition';
import { PersistedStateKey, createPersistConfig } from '../common';
// 类比vue-use里的useStorage,如果是与store或者业务或者其他的东西都没有依赖的，可以考虑抽到公共的

export const useUserStore = defineStore('user', () => ({
  ...useLogin(),
}), {
  persist: createPersistConfig([
    {
      key: PersistedStateKey.LOGIN_DATA,
      expire: 60 * 5 * 1000,
      paths: ['loginData'],
    },
  ]),
});
