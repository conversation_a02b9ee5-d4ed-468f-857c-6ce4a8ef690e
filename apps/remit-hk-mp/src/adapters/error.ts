/**
 * 异常处理
 */
import { BaseException, HttpExceptions, HttpResponse } from '@tencent/ppd-common/src/request-core';

import {
  UniRequestConfig,
} from '@tencent/ppd-uni-common/src/request/adaptors';
import { CommonResponse } from './request';

/**
 * 业务异常类
 */
export class BusinessExceptions extends BaseException<HttpResponse<
UniRequestConfig,
CommonResponse,
UniApp.RequestSuccessCallbackResult,
CommonResponse
>>  {
  name = 'BusinessExceptions';
  /**
   * 业务错误状态码
   */
  retcode: string;

  /**
   * 业务错误信息
   */
  retmsg: string;

  constructor(retcode: string, retmsg: string, rawData: HttpResponse<
  UniRequestConfig,
  CommonResponse,
  UniApp.RequestSuccessCallbackResult,
  CommonResponse
  >, message = retmsg) {
    super(message, rawData);
    this.retcode = retcode;
    this.retmsg = retmsg;
  }
}

export {
  BaseException,
  HttpExceptions,
};
