/**
 * 全局扩展字段
 */
const mtaGlobalConfig = {
  // --------sdk事件上报用到的参数----------
  sid: '500735886',
  cid: '500735887',

  // --------cli录入A/BC所用到的参数-----------------
  auth: {
    groupId: 10086, // 在海雀的Id（可以找海雀要）
    appKey: 'rta_access', // 数据源
  },
  scanInfo: {
    // out: '', // 输出路径，如果有，将scan到的结果输出
    which: 'BC',  // 扫A或BC，默认都扫
    // node_modules: false,  // 是否包含scan node_modules
    logic: '', // 逻辑上报埋点信息，除了dom打点之外，在js逻辑中上报的点，需要通过这个文件描述，才能scan到
    tempIdForA: 117, // 录入A时携带的模板Id（注：由于海雀真正生成事件A.B.C时，用的是B.C携带的扩展字段，所以这里实际没有用。推动海雀支持中）
    tempIdForBC: '117,152,403', // 录入BC时携带的模板Id
    firstClassNameForBC: 'comp', // 录入BC所属的一级分类
    secondClassNameForBC: 'list', // 录入BC所属的二级分类
    secondClassDescForBC: '组件', // 录入BC所属的二级分类描述，创建二级分类时有用
    productor: 'sakuragu', // 产品负责人
  },
  group: 'collection', // 业务，也是录入A的一级分类，找sam在海雀提前配好
  groupDesc: '小鹅快收', // 项目描述，也是录入A的二级分类描述
  project: 'fastreceipt', // 项目名，也是录入A的二级分类名
  projectDesc: '自动录入海雀demo', // 项目描述，也是录入A的二级分类描述
};

export default mtaGlobalConfig;

export const BIParamsConfig = {
  org: { field: 'org', desc: '机构名称', isnull: '1', ruleId: 225 },
  country: { field: 'country', desc: '国家', isnull: '1', ruleId: 225 },
  index: { field: 'index', desc: '索引', isnull: '1', ruleId: 255 },
};
