<template>
  <view
    v-if="allowShow"
    class="p-24rpx"
  >
    <view class="mb-24rpx">
      分享卡片标题
      <p-input
        v-model="title"
        placeholder="输入分享卡片的标题"
        border="surround"
        data-name="title"
        @change="changeTitle"
      />
    </view>
    <view class="mb-24rpx">
      分享卡片图片
      <p-input
        v-model="imageUrl"
        placeholder="输入分享卡片的图片链接"
        border="surround"
        data-name="imageUrl"
        @change="changeImage"
      />
    </view>
    <view class="mb-24rpx">
      分享卡片路径
      <p-input
        v-model="path"
        placeholder="输入分享卡片的路径"
        border="surround"
        data-name="path"
        @change="changePath"
      />
    </view>
    <view class="mt-42rpx flex items-center justify-center">
      <p-button
        type="primary"
        class="flex-1 m-auto"
        :custom-style="{
          width: '100%',
        }"
        size="large"
        open-type="share"
      >
        生成分享卡片
      </p-button>
    </view>
  </view>
</template>
<script setup lang="ts">
import PInput from '@tencent/fui-component-library/src/components/p-input/index.vue';
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import login from '@/adapters/login';
const allowShow = ref(false);
const title = ref('');
const imageUrl = ref('');
const path = ref('');
const shareInfo = ref({
  title: title.value,
  imageUrl: imageUrl.value,
  path: path.value,
});

onLoad(() => {
  wx.hideShareMenu();
  login.getOpenid().then((openid) => {
    if (openid
    && ['o_vMs5C9wbwuFmIu5xowyYBxk8hs',
      'o_vMs5KlYJRUbAqtT8X7kiUFc3OE',
      'o_vMs5GHyIiU_usXhC4h_RZFwKOI',
      'o_vMs5IFiqfbdZLh-0klQ2iCkbfQ'].includes(openid)) {
      allowShow.value = true;
    }
  });
});

const changeTitle = (e) => {
  title.value = e;
};
const changeImage = (e) => {
  imageUrl.value = e;
};
const changePath = (e) => {
  path.value = e;
};

onShareAppMessage(() => {
  shareInfo.value = {
    title: title.value,
    imageUrl: imageUrl.value,
    path: path.value,
  };
  return shareInfo.value;
});
</script>
<style lang="scss" scoped>
</style>
