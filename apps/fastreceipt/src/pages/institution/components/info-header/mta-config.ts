import { BIParamsConfig as c } from '../../../../../mta-config-global';
const actName = { field: 'act_name', desc: '活动名称', isnull: '1', ruleId: 225 };

export default [{
  key: 'institution_item_card',
  desc: '机构卡片',
  params: {
    img: '',
    productor: 'sakuragu',
    developer: 'ileneyang',
  },
  statList: [{
    subgroupCode: 'remit_button',
    subgroupDesc: '去汇款按钮',
    rule: [
      c.org,
      c.country,
      { field: 'redirectWay', desc: '跳转方式', isnull: '1', ruleId: 225 },
      { field: 'index', desc: '当前机构处理列表的排序位置', isnull: '1', ruleId: 225 },
    ],
  }],
},
{
  key: 'activity_tag',
  desc: '活动小标签',
  params: {
    img: '',
    productor: 'sakuragu',
  },
  statList: [{
    subgroupCode: 'img',
    subgroupDesc: '图片标签',
    rule: [c.org, actName],
  }],
},
];
