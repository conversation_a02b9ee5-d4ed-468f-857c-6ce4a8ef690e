<!--
 * @Author: ileneyang <EMAIL>
 * @Date: 2023-10-18 15:07:03
 * @LastEditors: ileneyang <EMAIL>
 * @LastEditTime: 2023-10-18 19:57:19
 * @FilePath: /ppd-uniapp/apps/fastreceipt/src/components/privacy-dialog/index.vue
-->
<template>
  <PrivacyDialog
    :is-show-fail-toast="isShowFailToast"
    @register-listener-err="registerListenerErr"
    @open-privacy-contract-err="openPrivacyContractErr"
    @show-privacy-dialog-err="showPrivacyDialogErr"
    @trigger-show-dialog-repeat-err="triggerShowDialogRepeatErr"
  />
</template>
<script setup lang="ts">
import PrivacyDialog from '@tencent/ppd-uni-component/packages/privacy-dialog-polyfill/index.vue';
import { CustomException } from '@/adapters/report';
defineProps({
  isShowFailToast: {
    type: Boolean,
    default: true,
  },
});

const registerListenerErr = (err) => {
  new CustomException(err, 'onRegisterListenerErr', '注册微信回调监听失败');
};
const openPrivacyContractErr = (err) => {
  new CustomException(err, 'onOpenPrivacyContractErr', '跳转小程序隐私指引失败');
};

const showPrivacyDialogErr = (err) => {
  new CustomException(err, 'showPrivacyDialogErr', '隐私弹窗展示错误');
};

const triggerShowDialogRepeatErr = (err) => {
  new CustomException(err, 'triggerShowDialogRepeatErr', '重复触发隐私弹窗展示错误');
};
</script>
