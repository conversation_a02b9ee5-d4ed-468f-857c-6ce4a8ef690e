/**
 * 路由参数
 */
type AppRoute = {
  notFound: boolean;
  openType: string;
  page: {
    window: object;
  };
  path: string;
  query: OnLoadOptions;
  renderer: string;
  scene: number;
  singlePageData: object;
  webviewId: number;
};

/**
 * onAppRoute的回调函数
 */
type OnAppRouteCallBack = (route: AppRoute) => void;

declare namespace WechatMiniprogram {
  interface Wx {
    /**
     * 微信小程序隐藏的api，可以监听小程序路由的变化，稳定性未知
     * @param route
     * @returns
     */
    onAppRoute: (listener: OnAppRouteCallBack) => void;
  }
}
