
import { DEFAULT_CONFIG_FILE_BASE_URL } from './default-config';
import {
  RequestOptions,
  Request,
} from '@tencent/ppd-common/src/request-core';
import {
  UniRequestAdaptor,
  UniRequestConfig,
} from '@tencent/ppd-uni-common/src/request/adaptors';
import { setServerTimeDiff } from '../time';
import reportInstance from '../report';

const configRequestInstance = new Request(new UniRequestAdaptor());
configRequestInstance.setDefaultConfig({
  baseURL: DEFAULT_CONFIG_FILE_BASE_URL,
  config: {
    method: 'GET',
    header: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  },
});

configRequestInstance.interceptors.response.use((httpResponse) => {
  const { rawResponse } = httpResponse;
  // 记录服务端时间差
  // 注意：这里不能是有本地缓存的数据，不然响应头的Date可能不准确
  setServerTimeDiff(rawResponse?.header?.Date);
  return httpResponse;
});
interface ConfigOptions {
  /**
   * 是否使用缓存
   */
  isUseCache: boolean
}

type ConfigCache<T extends any> = Record<string, Promise<T>>;
const configCache: ConfigCache<any> = {};
export const request = <Response>(
  options: RequestOptions<UniRequestConfig> & ConfigOptions,
): Promise<Response> => {
  // 优先从缓存获取
  const cache = configCache[options.url] as Promise<Response> | undefined;
  if (!cache || !options.isUseCache) {
    // 将promise缓存起来，避免发起多次请求
    configCache[options.url] = configRequestInstance.request(options)
      .then((response) => {
        reportInstance.reportInfo({
          key: 'RequestCosConfig',
          msg: '',
          // 配置的文件名
          ext1: `${DEFAULT_CONFIG_FILE_BASE_URL}${options?.url}`,
          // 配置的版本
          ext2: response?.rawResponse?.header?.ETag,
          // 配置的缓存情况
          ext3: response?.rawResponse?.header?.['X-Cache-Lookup'],
        });
        return response.data as Response;
      }) as Promise<Response>;
  }
  // 配置拉取失败，则清除缓存
  configCache[options.url].catch(() => {
    delete configCache[options.url];
  });
  return configCache[options.url];
};
