import { BusCheckLoginCacheAdaptor, BusReLoginAdaptor } from './reLogin';
import { DEFAULT_SUCCESS_RETCODE } from './code';
import { DEFAULT_REQUEST_CONFIG } from './default-config';
import {
  Request,
} from '@tencent/ppd-common/src/request-core';
import {
  UniRequestAdaptor,
  UniRequestConfig,
} from '@tencent/ppd-uni-common/src/request/adaptors';
import {
  createReLoginInterceptor,
  ReLoginRequestOptions,

} from '@tencent/ppd-uni-common/src/request/interceptors/reLogin-interceptor';
import { BusinessExceptions } from '../error';
import { setServerTimeDiff } from '../time';
import { busLoginRequest } from './login';
import { setLastRequestSucTime } from '@tencent/weremit-common';

/**
 * 基础返回内容
 */
export interface CommonResponse {
  /**
   * 响应状态码
   */
  retcode: string;
  /**
   * 响应信息
   */
  retmsg: string;
}

const requestInstance = new Request(new UniRequestAdaptor<CommonResponse>());

requestInstance.setDefaultConfig(DEFAULT_REQUEST_CONFIG);

// 使用登陆拦截器
const reLoginInterceptor = createReLoginInterceptor(new BusReLoginAdaptor(), new BusCheckLoginCacheAdaptor(true));
requestInstance.interceptors.request.use(reLoginInterceptor.request);
requestInstance.interceptors.response.use(reLoginInterceptor.response);

requestInstance.interceptors.request.use(busLoginRequest);

requestInstance.interceptors.response.use((httpResponse) => {
  const response = httpResponse.data;
  const { rawResponse } = httpResponse;
  // 记录服务端时间差
  // 注意：这里不能是有本地缓存的数据，不然响应头的Date可能不准确
  setServerTimeDiff(rawResponse?.header?.Date);
  // 记录上一次成功请求的时间（成功指未触发登陆态失效），仅记录cgi-bin接口请求的时间
  // @ts-ignore
  httpResponse.options?.isNeedLogin && setLastRequestSucTime();
  // 对业务异常进行特殊处理
  if (response.retcode !== DEFAULT_SUCCESS_RETCODE) {
    // 组装错误信息
    return Promise.reject(new BusinessExceptions(
      response.retcode,
      response.retmsg,
      // 错误数据原串
      httpResponse,
    ));
  }
  return httpResponse;
});

/**
 * 请求封装，增加类型提示
 * @param options 请求参数
 * @returns
 */
export const request = <Response>(
  options: ReLoginRequestOptions<UniRequestConfig>,
): Promise<Response> => requestInstance.request(options)
    .then(response => response.data as Response & CommonResponse);

/**
 * 导出relogin实例
*/
export const { reLoginInstance } = reLoginInterceptor;

export { useSingleRequest } from './single-request';
