import { LoggerService, PpdUniDomainToken } from '@tencent/ppd-uni-domain';
import { Service } from 'typedi';
import report, { CustomException } from '@/adapters/report';
import { biClick as click, biBrow as brow } from '@tencent/fit-bi-sdk';
@Service(PpdUniDomainToken.LOGGER_SERVICE_TOKEN)
export class CustomLoggerService implements LoggerService {
  CustomException = CustomException;
  event(key: string, msg?: string, ext?: Record<string, any>) {
    report.reportEvent({
      key,
      message: msg,
      ...ext,
    });
  };
  warn(key: string, msg?: string, ext?: Record<string, any>) {
    this.info(key, msg, ext);
  };
  error(err: any, key: string, msg?: string) {
    new CustomException(err, key, msg);
  };
  info(key: string, msg?: string, ext?: Record<string, any>) {
    report.reportInfo({
      key,
      msg: msg ?? '',
      ...ext,
    });
  };
  biBrow(key: string, ext?: Record<string, any> | undefined) {
    brow(key, ext);
  };
  biClick(key: string, ext?: Record<string, any> | undefined) {
    click(key, ext);
  }
}
