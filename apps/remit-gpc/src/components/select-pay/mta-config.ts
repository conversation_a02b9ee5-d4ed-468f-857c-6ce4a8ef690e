
const isSelect = { field: 'is_select', desc: '是否选择', isnull: '1', ruleId: 225 };
const currentWay = { field: 'current_way', desc: '当前收款方式', isnull: '1', ruleId: 225 };
const cardName = { field: 'cardName', desc: '银行卡名字', isnull: '1', ruleId: 225 };
const accountType = { field: 'accountType', desc: '账号类型', isnull: '1', ruleId: 225 };
export default [{
  key: 'cardchange_popup',
  desc: '收款方式切换浮层',
  params: {
    img: '',
    productor: 'sakuragu',
    developer: 'jeakeyliang',
  },
  statList: [{
    subgroupCode: 'cardchange_button',
    subgroupDesc: '更换其他收款银行卡按钮',
  }, {
    subgroupCode: 'yes_button',
    subgroupDesc: '确认按钮',
    rule: [isSelect],
  },
  {
    subgroupCode: 'no_button',
    subgroupDesc: '取消按钮',
  }, {
    subgroupCode: 'popup',
    subgroupDesc: '弹层',
    rule: [
      currentWay,
    ],
  }, {
    subgroupCode: 'item',
    subgroupDesc: '收款方式选项',
    rule: [
      cardName,
    ],
  }, {
    subgroupCode: 'change_suc',
    subgroupDesc: '更换收款方式成功',
    rule: [
      accountType,
    ],
  }],
}];
