import { CustomException } from '@/adapters/report';
import { computed, ref } from 'vue';
import { isValidatedInt, to } from '@/adapters/functions';
import { navigateToPreviewSignPage } from '@tencent/weremit-common/route/package-gpc-uni';
import { CreType } from '@/domain/user';
import Container from 'typedi';
import { MaterialService, UploadMaterialReq } from '@/domain/material';
import { commonErrModal, hideLoading, showLoading } from '@/adapters/uni-api-common';
import { FileMaterialsKey, MaterialType } from '@/domain/material/entities/materialEntity.type';
import { biBrow, biClick } from '@tencent/fit-bi-sdk';
import { RecordService } from '@/domain/order/recordService';
import { Relationship, RelationshipService } from '@/domain/material/service/relationship.service';
/**
 * 订单信息
 */
export interface OrderInfo {
  listId: string;
  remitterName: string;
  name: string;
  idNo: string;
  creType: CreType;
  ageGap: number; // 汇款人收款人年龄差绝对值
  materialType: string;
  dispatchBusType: string;
}

/**
 * 关系列表
 */
const materialService = Container.get(MaterialService);
export function useSignCommitment(
  orderInfo: OrderInfo,
  type: MaterialType, initRelationshipId: number | undefined, btnText: string,
) {
  // 计时器运行标记位
  let isTimerRunning = false;
  let timer: ReturnType<typeof setInterval> | null = null;
  // 当前索引
  const currentIndex = ref<undefined | number>();
  // 倒计时
  const countdown = ref(3);
  // 是否勾选协议
  const isCheckProtocol = ref(false);

  const relationshipList = RelationshipService.getRelationshipList(type !== MaterialType.NEED_SIGN_NO_PURPOSE);

  const isSelfSelected = computed(() => RelationshipService.getIsSelf(currentRelationship.value));
  // 初始化选中项
  const initRelationshipIndex = relationshipList.findIndex(item => item.value === initRelationshipId);
  currentIndex.value = initRelationshipIndex > -1 ? initRelationshipIndex : undefined;

  const relationType = type === MaterialType.NEED_SIGN_NO_PURPOSE ? 'no_purpose_relation' : 'commitment_relation';

  /** 当前选择的关系 */
  const currentRelationship = computed(() => {
    if (currentIndex.value === undefined || !relationshipList[currentIndex.value]) {
      return;
    }
    return relationshipList[currentIndex.value].value;
  });
  /**
   * 需提交的数据
   */
  const materialData = computed<UploadMaterialReq>(() => ({
    listid: orderInfo.listId,
    material_array: [{
      material_type: materialService.getMaterialTypeVal([FileMaterialsKey.SIGN_COMMITMENT]),
      relationship: currentRelationship.value,
      // 为了优化承诺函的签署体验，不再需要用户手写名字，所以不需要传签名的图片了，直接传空数组
      // PRD：https://doc.weixin.qq.com/doc/w3_AJAAPQawAFQTH0B14KhSQuzRIFOVJ?scode=AJEAIQdfAAoH12vb43AJAAPQawAFQ
      file_array: [],
    }],
  }));
  /**
   * 根据年龄差校验亲属关系是否正确
   * 1. 年龄差 < 15 或 > 50 时，父母、子女关系提示
   * 2. 年龄差 < 30 时，祖父母/外祖父母/孙子女/外孙子女关系提示
   * 配偶 = 10、父母 = 20、子女 = 30、兄弟姐妹 = 40、祖父母/外祖父母 = 50、子孙女/外子孙女 = 60
   */
  const isValidatedRelationship = computed(() => {
    if (!currentRelationship.value) {
      return true;
    }
    if (!isValidatedInt(orderInfo.ageGap)) {
      new CustomException({ ageGap: orderInfo.ageGap }, 'relation_ship_validate_err', '亲属关系校验异常');
      return true;
    }
    let isRelationShipValidated = true;
    switch (currentRelationship.value) {
      case 20:
      case 30:
        isRelationShipValidated = Number(orderInfo.ageGap) >= 15 && Number(orderInfo.ageGap) <= 50;
        break;
      case 50:
      case 60:
        isRelationShipValidated = Number(orderInfo.ageGap) >= 30;
        break;
      default:
        break;
    }
    return isRelationShipValidated;
  });
  // 按钮文案
  const buttonText = computed(() => {
    if (isValidatedRelationship.value || countdown.value <= 0) {
      return btnText;
    }
    return `${btnText}（${countdown.value}s）`;
  });
  // 表单是否校验通过
  const isFormReady = computed(() => {
    // 如果未勾选协议或者未选择关系，不做后面的判断，直接返回false
    if (!isCheckProtocol.value || !currentRelationship.value) {
      return false;
    }
    // 仅关系异常才需要判断倒计时
    if (!isValidatedRelationship.value && countdown.value > 0) {
      return false;
    }

    return true;
  });

  onMounted(() => {
    biBrow('promise_sign.brow', {
      materialType: orderInfo.materialType,
      creType: orderInfo.creType,
      relationType,
    });
  });
  /**
   * 关系变更
   * @param id 关系id
   */
  function onRelationshipChange(item: Relationship, index: number) {
    currentIndex.value = index;
    biClick('promise_sign.relationship_change', {
      materialType: orderInfo.materialType,
      creType: orderInfo.creType,
      relationType,
      isValidatedRelationship: isValidatedRelationship.value ? '1' : '2',
      relationship: currentRelationship.value?.toString() ?? '',
    });
    if (isSelfSelected.value) return;
    // 仅有异常的关系，且倒计时不重复的情况下，进行倒计时
    if (!isValidatedRelationship.value && countdown.value >= 0 && !isTimerRunning) {
      isTimerRunning = true;
      timer = setInterval(() => {
        countdown.value = countdown.value - 1;
        if (countdown.value <= 0) {
          timer && clearInterval(timer);
        }
      }, 1000);
    }
  };
  /**
   * 协议勾选
   * @param data 选择数据
   */
  function onProtocolCheck(data: {checked: boolean}) {
    biClick('promise_sign.protocol_check', {
      materialType: orderInfo.materialType,
      creType: orderInfo.creType,
      relationType,
      checked: data.checked ? '1' : '2',
      relationship: currentRelationship.value?.toString() ?? '',
    });
    isCheckProtocol.value = data.checked;
  };
  /**
   * 点击预览
   */
  function onPreviewClick(protocol) {
    biClick('promise_sign.preview_click', {
      materialType: orderInfo.materialType,
      creType: orderInfo.creType,
      relationType,
      relationship: currentRelationship.value?.toString() ?? '',
    });
    navigateToPreviewSignPage({
      creType: orderInfo.creType,
      id_no: orderInfo.idNo,
      name: orderInfo.name,
      relationship: currentRelationship.value?.toString() || '',
      remit_name: orderInfo.remitterName,
      previewType: protocol.key,
    });
  }
  /**
   * 提交数据
   * @returns 是否提交成功
   */
  async function submitData() {
    showLoading();
    const [err] = await to(materialService.uploadMaterial(materialData.value));
    hideLoading();
    if (err) {
      commonErrModal(err);
      new CustomException(err, 'upload_material_err', '上传材料失败');
      return false;
    }
    biClick('promise_sign.submit_suc', {
      materialType: orderInfo.materialType,
      creType: orderInfo.creType,
      relationType,
      relationShip: currentRelationship.value?.toString() ?? '',
      isValidatedRelationship: isValidatedRelationship.value ? '1' : '2',
    });
    // 设置标记位，让订单列表更新数据
    RecordService.resetRecordStatusHasChanged();
    return true;
  }
  onUnmounted(() => {
    timer && clearInterval(timer);
  });
  return {
    isSelfSelected,
    relationshipList,
    onRelationshipChange,
    onProtocolCheck,
    isValidatedRelationship,
    isFormReady,
    buttonText,
    currentRelationship,
    currentIndex,
    onPreviewClick,
    orderInfo,
    submitData,
    materialData,
  };
}
