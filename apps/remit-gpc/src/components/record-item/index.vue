<!--
 * @Date: 2023-03-13 11:29:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-04-16 11:32:15
 * @FilePath: /ppd-uniapp/apps/tuition/src/pages/home/<USER>
 * @Description:
-->
<template>
  <view
    v-stat.click="'record-item.item'"
    :data-stat="JSON.stringify({list_state: orderItem.state.code, need_ele_types: needEleTypes})"
    class="order-item flex items-center justify-center"
    :style="orderItemStyle"
  >
    <view class="order-detail mr-16rpx flex-1">
      <view class="flex items-start justify-between flex-1">
        <text class="text-ellipsis text4-sem color-text-title flex-1 w-0 max-w-320rpx">
          {{ orderItem.name }}
        </text>
        <view class="amount relative number5 color-line-activate ml-40rpx">
          <text class="absolute top-0 left--24rpx">
            {{ orderItem.symbol }}
          </text>
          <text class=" number5 ">
            {{ orderItem.amount }}
          </text>
        </view>
      </view>
      <view class="flex items-center justify-between mt-16rpx">
        <text class="caption1-reg color-text-auxiliary min-w-230rpx">
          {{ orderItem.time }}
        </text>
        <text
          class="caption1-sem color-primary-normal break-all"
          :class="stateStyle"
        >
          {{ stateText }}
        </text>
      </view>
    </view>
    <p-icon
      class="color-icon-schematic"
      name="arrow-right"
      size="32"
    />
  </view>
</template>

<script setup lang="ts">
import { StateType, ListState } from '@/domain/order/entities/stateEntity.type';
interface OrderItem {
  name: string,
  amount: string,
  time: string,
  state: {
    code: ListState,
    type: StateType,
    title: string,
    getDetailInfo: () => Promise<{
      title: string,
      needEleTypes?: string,
      type?: StateType
    }>
  },
  symbol: string
}
const props = withDefaults(defineProps<{
  orderItem: OrderItem,
  orderItemStyle?: Record<string, string>,
}>(), {
  orderItemStyle: () => ({}),
});
// 文案样式列表
const styleMap = {
  [StateType.SUCCESS]: 'color-text-auxiliary',
  [StateType.FAIL]: 'color-state-error',
  [StateType.WAITING]: 'color-state-info',
  [StateType.WARN]: 'color-state-alert',
};

// 默认状态文案为空，拉取到以后再设置
const stateText = ref('');
const needEleTypes = ref('');
const stateStyle = ref('');
watch(() => props.orderItem, () => {
  props.orderItem.state.getDetailInfo().then((info) => {
    stateText.value = info.title;
    needEleTypes.value = info.needEleTypes || '';
    if (info.type) {
      stateStyle.value = styleMap[info.type];
      return;
    }
    stateStyle.value = styleMap[props.orderItem.state.type] || styleMap[StateType.SUCCESS];
  });
}, { immediate: true });


</script>

<style scoped lang="scss">
.order-item {
  padding: 32rpx 32rpx 32rpx 32rpx;
  // height: 160rpx;
  border-radius: 8rpx;
  box-sizing: border-box;
}
.text-ellipsis {
  overflow: hidden;//溢出隐藏
  white-space: nowrap; // 强制一行显示
  text-overflow: ellipsis;// 显示。。。省略号
}
</style>
