/**
 * 订单场景枚举
 * 用于标识当前订单命中了什么状态
*/
export enum RecordScene{

  /* ====== 未在小鹅落单枚举 开始 ====== */
  /**
   * 互联未落单
   * 对应互联未落单，在hold表中无记录
   */
  HU_LIAN_NOT_PLACE_ORDER = 'huLianNotPlaceOrder',
  /**
   * 待收款人发起收款
   * log_state为10
  */
  WAIT_RECEIVER_RECEIVE = 'waitReceiverReceive',

  /**
   * 汇款人取消订单
   * log_state为34
  */
  REMITTER_CANCEL_ORDER = 'remitterCancelOrder',
  /**
   * 机构余额不足 - 取消订单
   * log_state为35
  */
  ORG_BALANCE_NOT_ENOUGH_CANCEL_ORDER = 'orgBalanceNotEnoughCancelOrder',

  /**
   * 除34和35外，未在小鹅落单的订单关闭状态
  */
  GPC_NOT_PLACE_RECORD_FAIL = 'gpcNotPlaceRecordFail',

  /* ====== 已在小鹅落单枚举 开始 ====== */

  /**
   * 小鹅发起收款
   * 对应10前缀状态码（特殊10状态码场景在下面有单独的枚举）
  */
  GPC_RECEIVING = 'gpcReceiving',

  /**
   * 待收款人更换收款方式
   * 10050状态
   * 或 20013状态
  */
  RECEIVER_CHANGE_PAYMENT_METHOD = 'receiverChangePaymentMethod',

  /**
   * 审核中
   * 对应状态为20开头（特殊的20状态场景在下面有单独的枚举）
  */
  VERIFYING = 'verifying',

  /**
   * 待收款人补充信息
   * 20000状态
  */
  RECEIVER_SUPPLEMENT_INFORMATION = 'receiverSupplementInformation',

  /**
   * 待收款人提交材料
   * 20020状态
  */
  RECEIVER_SUBMIT_MATERIALS = 'receiverSubmitMaterials',

  /**
   * 银行处理中
   * 对应前缀为40的状态码（特殊的40状态场景在下面有单独的枚举）
  */
  BANK_PROCESSING = 'bankProcessing',

  /**
   * 审核通过后待收款人更换收款方式
   * 对应40080状态
  */
  VERIFY_SUC_TO_RECEIVER_CHANGE_PAYMENT_METHOD = 'verifySucToReceiverChangePaymentMethod',

  /**
   * 收款成功
   * 对应状态为50开头
  */
  RECEIVE_SUC = 'receiveSuc',

  /**
   * 收款失败，且前置状态为发起收款
   * 当前状态码为60前缀
   * 且 失败状态码为10前缀
  */
  WAIT_RECEIVE_TO_RECEIVE_FAIL = 'waitReceiveToReceiveFail',

  /**
   * 收款失败，且前置状态为审核中，且触发了审核失败的场景
   * 当前状态码为60前缀
   * 且 失败状态码为20前缀
   * 且 触发了审核失败（上传过材料）
  */
  VERIFY_FAIL_TO_RECEIVE_FAIL = 'verifyFailToReceiveFail',

  /**
   * 收款失败，且前置状态为审核中，且未触发审核失败的场景
   * 当前状态码为60前缀
   * 且 失败状态码为20前缀
   * 且 未触发审核失败（未提交过材料）
  */
  VERIFY_TO_RECEIVE_FAIL_WITHOUT_VERIFY_FAIL = 'verifyToReceiveFailWithoutVerifyFail',

  /**
   * 收款失败，且前置状态为银行处理中
   * 当前状态码为60前缀
   * 且 失败状态码为40前缀
  */
  BANK_PROCESSING_TO_RECEIVE_FAIL = 'bankProcessingToReceiveFail',

  /**
   * 兜底场景，匹配不到任何场景时会命中兜底场景
  */
  DEFAULT = 'default'

}
