/**
 * 订单流程实体，返回当前订单的流程列表
 */

import { filterInitTime, getBeijingTime, getServerTime } from '@/adapters/time';
import { TranDtlQryRes } from '../../repository/trans-repository.interface';
import { FAIL_CODE, StateEntity } from '../stateEntity';
import { ListState } from '../stateEntity.type';
import { Container } from 'typedi';
import { MaterialService } from '@/domain/material';
import { FileMaterialsKey } from '@/domain/material/entities/materialEntity.type';
import { MaterialViewService } from '@/domain/material/service/material-view.service';
import { getAccountText } from '@/adapters/functions/text';
import { CustomException } from '@/adapters/report';
import { ProcessState } from './recordProcessEntity';
import { RqrAccountType } from '@/data-source/user/card';
import { RecordService } from '@/domain/order/recordService';
import { MaterialTextItem } from '@/domain/material/repository/material-view.repository.interface';
import { NoticeService } from '@/domain/config/noticeService';
import { RetrievalDtlEntity } from '@/domain/material/entities/retrievalDtlEntity';
import { AuditType } from '@/data-source/material/retrieval';

/**
 * 进度图主按钮
 */
export enum ProcessItemAction{
  /**
   * 换卡，对应10050
   */
  changeCard,
  /**
   * 材料、亲属关系
   */
  signCommitment,
  /**
   * 重新绑卡
   */
  rebindCard,
  /**
   * 提交事后调单材料
   */
  postRetrieveDoc,
  /**
   * 查看事后调单进度
   */
  checkRetrieveProgress,
  /**
   * 绑默认卡
   */
  changeDefaultCard,
  /**
   * 展示零钱不可用弹窗
   */
  showBalanceFailPopup,
  /**
   * 签协议、补充九要素
   */
  branchSign,
  /**
   * 回乡证影印件缺失
   */
  needHrpPhotocopy,
  /**
   * 同pair关系有待处理订单
   */
  goPairUploadFile
}
/**
 * 进度状态
 */
export enum ProcessItemStatus{
  /**
   * 等待
   */
  waiting,
  /**
   * 提示
   */
  warn,
  /**
   * 成功
   */
  success,
  /**
   * 失败
   */
  fail
}
/**
 * 操作类型
 */
export interface ProcessItemActionConfig{
  text: string,
  type: ProcessItemAction,
  payload?: Record<string, any>
}
/**
 * 进度条子项
*/
export interface ProcessItemConfig {
  title: string,
  // 描述
  desc?: string,

  // 状态
  status: ProcessItemStatus,

  // 时间
  time?: string,
  // 操作
  action?: ProcessItemActionConfig,
  // 如果处于前置状态的配置
  preConfig?: Partial<ProcessItemConfig>,
  // 标签
  tag?: string
}
/**
 * 订单流程项
 */
export class RecordProcessItemEntity {
  /**
   * 材料文案
   */
  private materialText: MaterialTextItem['recordText'] | undefined;

  /**
   * 调单实体
  */
  private retrievalDtlEntity?: RetrievalDtlEntity;

  constructor(
    /**
     * 订单数据
     */
    private recordData: TranDtlQryRes,
    /**
     * 订单状态实体
     */
    private state: StateEntity,
    /**
     * 当前阶段状态值
     */
    private itemState: ListState,
    /**
     * 失败状态值
     */
    private failState?: ListState,
  ) {
    if (this.recordData.material_type) {
      this.materialText = MaterialViewService.getMaterialTextByType(
        this.recordData.material_type,
        this.recordData.remitter_name,
        this.recordData.list_state,
        this.recordData.need_sign_contract || 0,
      )?.recordText;
    }
    if (this.recordData.retrievalDtlInfo) {
      this.retrievalDtlEntity = new RetrievalDtlEntity(this.recordData.retrievalDtlInfo);
    }
  }
  /**
   * 从审批批次数组里面获取最新批次的审批时间
   */
  private get auditTimeFromAuditBatchInfo() {
    return  this.recordData.audit_batch_info?.reduce((pre, cur) => {
      if (!pre) {
        return cur;
      }
      return cur.audit_batch_index > pre.audit_batch_index ? cur : pre;
    }, this.recordData.audit_batch_info[0])?.audit_batch_create_time || '';
  }
  /**
   * 20 状态下的最新审核时间
   */
  private get lastAuditTimeIn20()  {
    return filterInitTime([
      this.recordData?.audit_time,
      this.recordData?.create_time,
      this.recordData?.basic_ext_pend_time,
      this.recordData?.open_account_fail_time,
      this.auditTimeFromAuditBatchInfo,
    ].reduce((pre, cur) => {
      const curTime = new Date(cur || '').getTime();
      const preTime = new Date(pre || '').getTime();
      if (!preTime) {
        return cur;
      }
      if (!curTime) {
        return pre;
      }
      return curTime > preTime ? cur : pre;
    }, '') || '');
  }
  /**
   * 20 状态下的最新审核时间
   */
  private get verifySuccessTime()  {
    return  filterInitTime(this.recordData.audit_final_time)
    || filterInitTime(this.recordData.complete_account_time || '')
    || filterInitTime(this.recordData.audit_time)
    || filterInitTime(this.recordData.create_time);
  }
  /**
   * 判断是否是因为更名导致资金分发失败
   */
  private get isChangeBankForChangeName() {
    return +this.recordData.fail_code === FAIL_CODE.NAME_CHANGE;
  }

  /**
   * 判断是否是因为没有绑定中国大陆银行卡导致零钱超限收款失败
   */
  private get isCollectChangeFailedAndNeedBindCard() {
    return  +this.recordData.fail_code === FAIL_CODE.COLLECT_CHANGE_FAILED_AND_NEED_BIND_CARD;
  }
  /**
   * 银行卡全名（包括卡尾号）
   */
  private get bankCardFullName() {
    return getAccountText(this.recordData.bank_name, this.recordData.bank_account, true);
  }

  /**
   * 协议和九要素配置
   */
  private get contractStateConfig(): ProcessItemConfig {
    const text = '根据相关合规要求，请您于24小时内完善个人信息，完成才能继续收款，超时资金将原路退回';
    switch (true) {
    //  只需要区分9要素和协议文案即可 不用细分组合状态
      case this.state.contractState.isNeed9Ele:
        return {
          title: '请完善个人信息',
          desc: text,
          time: filterInitTime(this.recordData.basic_ext_pend_time || ''),
          status: ProcessItemStatus.warn,
          action: {
            text: '去完善',
            type: ProcessItemAction.branchSign,
          },
        };
      case this.state.contractState.isNeedSign:
        return {
          title: '请签署协议',
          desc: '请于24小时内阅读并签署收款服务协议，超时未签署资金将原路退回',
          status: ProcessItemStatus.warn,
          time: filterInitTime(this.recordData.basic_ext_pend_time || ''),
          action: {
            text: '去签署',
            type: ProcessItemAction.branchSign,
          },
        };
      case this.state.contractState.isNeedRelationship:
        return {
          title: '请确认汇款关系',
          desc: '根据合作银行要求，请在24小时确认汇款关系，避免因超时导致款项被退回',
          status: ProcessItemStatus.warn,
          time: filterInitTime(this.recordData.basic_ext_pend_time || ''),
          action: {
            text: '去确认',
            type: ProcessItemAction.signCommitment,
          },
        };

      case this.state.contractState.isOnlyNeedHrpPhotocopy:
        return {
          title: '请上传证件照片',
          desc: RecordService.checkIsConfirmHrpPhotocopy()
            ? '暂未检测到证件照片。如已提交，请耐心等待；如未提交，请您按照指引完成提交' : '根据指引在微信内上传或更新证件照片，上传后点击“已上传，继续收款”',
          status: ProcessItemStatus.warn,
          time: filterInitTime(this.recordData.basic_ext_pend_time || ''),
          action: {
            text: '如何上传',
            type: ProcessItemAction.needHrpPhotocopy,
          },
        };
    }
    return {
      title: '处理中',
      status: ProcessItemStatus.waiting,
      time: filterInitTime(this.recordData.create_time),
    };
  }
  /**
   * 银行处理中时间
   */
  private get bankProcessTime() {
    return filterInitTime(this.recordData.remit_process_time)
    || filterInitTime(this.recordData.audit_final_time)
    || filterInitTime(this.recordData.create_time);
  }
  /**
   * 收款成功时间
   */
  private get successTime() {
    return this.recordData.remit_final_time;
  }
  /**
   * 承诺函、材料配置
   */
  private get waitSignStateConfig(): ProcessItemConfig[] {
    // 是否因材料失败
    const isWaitSignFail = +(this.failState ?? '') === +ListState.WAIT_SIGN;
    const realState = {
      title: this.materialText?.tit || '请提交证明材料',
      status: ProcessItemStatus.warn,
      time: filterInitTime(this.auditTimeFromAuditBatchInfo)
        || filterInitTime(this.recordData.audit_time),
      desc: this.materialText?.desc,
      action: {
        text: this.materialText?.btnText ?? '请提交',
        type: ProcessItemAction.signCommitment,
      },
      preConfig: {
        title: '请提交证明材料',
        status: isWaitSignFail ? ProcessItemStatus.warn : ProcessItemStatus.success,
        desc: '',
        time: filterInitTime(this.recordData?.audit_batch_info?.find(item => item.audit_batch_index === 1)?.audit_batch_create_time || '')
        || filterInitTime(this.recordData.audit_time) || this.recordData.create_time,
      },
    };
    // 如果提交过材料，非失败场景，增加
    if (this.recordData.audit_times > 1 && !isWaitSignFail) {
      return [
        {
          title: '请提交证明材料',
          status: ProcessItemStatus.success,
          time: filterInitTime(this.recordData?.audit_batch_info?.find(item => item.audit_batch_index === 1)?.audit_batch_create_time || '')
        || filterInitTime(this.recordData.audit_time) || this.recordData.create_time,
        },
        {
          title: '审核失败',
          status: ProcessItemStatus.fail,
          time: this.recordData.audit_final_time,
          desc: `${this.recordData.audit_fail_reason || ''}`,
        },
        realState,
      ];
    }
    return [realState];
  }
  /**
   * 审核中配置
   */
  private get verifyConfig(): ProcessItemConfig[] {
    const isFail = (this.failState ?? '').toString().slice(0, 2) === '20';
    const materialService = Container.get(MaterialService);
    const realState: ProcessItemConfig[] = [{
      title: '审核中',
      status: ProcessItemStatus.waiting,
      time: this.lastAuditTimeIn20,
      desc: (+this.recordData.audit_times > 0
        && +this.recordData.material_type
        !== materialService.getMaterialTypeVal([FileMaterialsKey.SIGN_COMMITMENT]))
        ? '预计24小时内完成审核' // 审核文案
        : '', // 只有提交过材料时，才会展示此描述文案（单签署承诺函不属于提交材料）
      preConfig: {
        desc: '',
      },
    }];
    // 如果提交过材料，增加
    if (this.recordData.audit_times > 0 && +this.recordData.material_type
      !== materialService.getMaterialTypeVal([FileMaterialsKey.SIGN_COMMITMENT])) {
      realState.unshift({
        title: '请提交证明材料',
        status: ProcessItemStatus.success,
        time: filterInitTime(this.auditTimeFromAuditBatchInfo)
        || filterInitTime(this.recordData.audit_time) || this.recordData.create_time,
      });
    }
    if (isFail && this.recordData.audit_times > 0) {
      realState.push({
        title: '审核失败',
        status: ProcessItemStatus.fail,
        time: this.recordData.audit_final_time,
        desc: `${this.recordData.audit_fail_reason || ''}`,
      });
    }
    return realState;
  }
  /**
   * 获取成功状态的标签
   */
  private get successTag() {
    const bankProcessTime = new Date(this.bankProcessTime).getTime();
    const successTime = new Date(this.successTime).getTime();
    if (successTime - bankProcessTime < 60 * 1000) {
      return '秒级到账';
    }
    return '';
  }
  /**
   * 获取换卡文案
   */
  private get changeCardDesc() {
    if (+this.recordData.rqr_account_type === +RqrAccountType.ONLY_CFT) {
      return '请更换收款方式为零钱。请于24小时内完成更换，超时资金将原路返回';
    }
    return '不支持收款到零钱。请于24小时内完成更换，超时资金将原路返回';
  }

  /**
   * 流程配置
   */
  public async getProcessConfig(): Promise<ProcessItemConfig[]> {
    const processItemConfig: Record<ProcessState, ProcessItemConfig| ProcessItemConfig[]> = {
      [ListState.PRE_PROCESSING]: {
        title: '发起收款',
        status: ProcessItemStatus.waiting, // 用于标记主状态颜色
        time: this.recordData.create_time,
        desc: '请留意手机短信的收款进度通知',
        preConfig: {
          desc: '',
        },
      },
      [ListState.PRE_PROCESSING_REMIT_ACCEPT_HOLD]: await this.getPreProcessingConfig(),
      [ListState.WAIT_RECEIVE]: {
        title: '发起收款',
        status: ProcessItemStatus.waiting, // 用于标记主状态颜色
        time: this.recordData.create_time,
        desc: '请留意手机短信的收款进度通知',
        preConfig: {
          desc: '',
        },
      },
      [ListState.ACCOUNT_UNABLE_CHANGE]: {
        title: '请更换收款方式',
        status: ProcessItemStatus.warn,
        desc: this.changeCardDesc,
        time: this.recordData.create_time,
        action: {
          text: '去更换',
          type: ProcessItemAction.changeCard,
        },
        preConfig: {
          time: filterInitTime(this.recordData.recv_account_pend_time || ''),
        },

      },

      [ListState.WAIT_CONTRACT_RECEIVE]: this.contractStateConfig,
      [ListState.CHANGE_BANK_ACCOUNT]: {
        title: '请更换收款方式',
        status: ProcessItemStatus.warn,
        desc: '收款卡不支持。请于24小时内完成更换，超时资金将原路返回',
        preConfig: {
          time: filterInitTime(this.recordData.open_account_fail_time),
        },
        action: {
          text: '去更换',
          type: ProcessItemAction.changeCard,
        },
      },
      // 20020 状态都取最新批次的时间
      [ListState.WAIT_SIGN]: this.waitSignStateConfig,
      [ListState.VERIFYING]: this.verifyConfig,
      [ListState.VERIFY_SUC]: {
        title: '审核成功',
        status: ProcessItemStatus.waiting,
        time: filterInitTime(this.recordData.audit_final_time)
          || filterInitTime(this.recordData.audit_time)
          || filterInitTime(this.recordData.create_time), // 优先使用审核中时间做兜底
        preConfig: {
          // 这里如果经历了调单，就会有 audit_final_time
          // 而且这个时间可能会比 complete_account_time 更晚，所以先取 audit_final_time 然后取 complete_account_time
          time: this.verifySuccessTime, // 优先使用审核中时间做兜底
        },
      },
      [ListState.CHANGE_BANK]: {
        title: this.isChangeBankForChangeName ? '请重新绑定收款方式' : '请更换收款方式',
        status: ProcessItemStatus.warn,
        desc: this.isChangeBankForChangeName ? '微信实名信息与账户名不符，请于24小时内重新绑定收款方式进行更新，超时资金将原路返回' : '收款卡不支持，请于24小时内更换收款方式，超时资金将原路返回',
        time: filterInitTime(this.recordData.pay_fail_time || '')
        || filterInitTime(this.recordData.remit_process_time)
        || '',
        action: {
          text: this.isChangeBankForChangeName ? '去绑定' : '去更换',
          type: this.isChangeBankForChangeName ? ProcessItemAction.rebindCard : ProcessItemAction.changeCard,
        },
      },
      [ListState.BANK_PROCESS]: {
        title: await this.getBankProcessTitle(),
        status: ProcessItemStatus.waiting,
        desc: '银行处理中，订阅公众号到账秒提醒',
        time: '',
        preConfig: {
          desc: '',
          time: this.bankProcessTime,
        },
      },
      [ListState.RECEIVE_SUC]: {
        title: `已到账至${this.bankCardFullName}`,
        status: ProcessItemStatus.success,
        desc: '后续收款自动到账',
        time: this.successTime,
        tag: this.successTag,
      },
      [ListState.RECEIVE_FAIL]: {
        title: '收款失败',
        status: ProcessItemStatus.fail,
        desc: this.getFailStateDescConfig(),
        time: filterInitTime(this.recordData.remit_final_time),
        action: await this.getFailStateBtnConfig(),

      },
    };
    const config = processItemConfig[this.itemState];
    if (!config) {
      throw new CustomException(this.itemState, 'process_config_empty', '进度配置找不到');
    }
    // 取最后的配置，如果不是当前状态，需要使用preConfig来覆盖
    const lastConfig = config instanceof Array ? config[config.length - 1] : config;
    if (config instanceof Array) {
      config[config.length - 1] = lastConfig;
      return config;
    }
    return [lastConfig];
  }
  // 银行处理中文案
  private async getBankProcessTitle() {
    if (this.state.mainState === ListState.BANK_PROCESS) {
      const notice = await NoticeService.getFastReceiptNotice({
        branchType: this.recordData.branch_type ?? '',
        listState: this.recordData.list_state,
        page: 'recordDetail',
      });
      if (getServerTime() - getBeijingTime(this.verifySuccessTime).getTime() > 10 * 60 * 1000) {
        return '银行处理中';
      }

      if (!!notice.validConfig) {
        return '银行处理中';
      }
      return '预计1分钟到账';
    }

    return '银行处理中';
  }

  /**
   * 失败状态文案配置
  */
  private getFailStateDescConfig(): string {
    // 零钱超限收款失败
    if (this.isCollectChangeFailedAndNeedBindCard) {
      return '零钱账户收款受限，请根据指引提升零钱账户限额';
    }

    /**
     * 订单预处理阶段失败
    */
    if (this.state.isRetrievalFail) {
      /**
       * 如果是00100状态下取消订单，则优先返回取消收款文案
      */
      if (this.state.isPreProcessCancelOrder) {
        return '已取消收款，资金将原路退回汇款机构';
      }


      if (!this.recordData.retrievalDtlInfo) {
        return '收款失败，资金将原路退回，如有疑问可联系客服咨询';
      }

      const retrievalDtlEntity = new RetrievalDtlEntity(this.recordData.retrievalDtlInfo);
      // 调单停留在首次待提交状态
      if (retrievalDtlEntity.isWaitFirstTimeSubmit) {
        return `你与汇款人${this.recordData.remitter_name}的证明材料还未提交，收款失败，提交并审核成功后可接收后续来自TA的汇款。`;
      }
      // 调单停留在审核中状态
      if (retrievalDtlEntity.isAuditing) {
        return `你与汇款人${this.recordData.remitter_name}的证明材料仍在审核中，预计24小时后可知审核结果，若审核成功后可接收后续来自TA的汇款。`;
      }
      // 订单重新提交
      if (retrievalDtlEntity.isWaitResubmit) {
        return `你与汇款人${this.recordData.remitter_name}的证明材料审核失败，请重新提交材料，审核成功后可接收后续来自TA的汇款。`;
      }
      return '收款失败，资金将原路退回，如有疑问可联系客服咨询';
    }

    return this.recordData.fail_reason;
  }

  /**
   * 订单预处理阶段配置
   * 根据前置卡主的调单详情进行判断
  */
  private async getPreProcessingConfig(): Promise<ProcessItemConfig[]> {
    if (this.failState === ListState.PRE_PROCESSING_REMIT_ACCEPT_HOLD) {
      return [];
    }

    const defaultConfig = [{
      title: '发起收款',
      desc: '请留意收款进度通知',
      status: ProcessItemStatus.waiting,
    }];

    if (!this.recordData.retrievalDtlInfo) {
      return defaultConfig;
    }

    const retrievalDtlEntity = new RetrievalDtlEntity(this.recordData.retrievalDtlInfo);

    let actionType = ProcessItemAction.postRetrieveDoc;
    let payload = {};
    if (retrievalDtlEntity.auditType === AuditType.IN_PROCESS_AUDIT_LIST) {
      payload = {
        listid: this.recordData.audit_listid,
      };
      actionType = ProcessItemAction.goPairUploadFile;
    }

    /**
     * 首次待提交状态
    */
    if (retrievalDtlEntity.isWaitFirstTimeSubmit) {
      return [{
        title: '请提交证明材料',
        desc: `你与汇款人${this.recordData.remitter_name}的证明材料还未提交，提交并审核成功后可以继续接收TA的汇款。请在48小时内提交，避免收款失败。`,
        status: ProcessItemStatus.warn,
        action: {
          text: '去提交',
          type: actionType,
          payload,
        },
      }];
    }

    /**
       * 审核中状态
      */
    if (retrievalDtlEntity.isAuditing) {
      return [{
        title: '请提交证明材料',
        status: ProcessItemStatus.success,
      },
      {
        title: '审核中',
        desc: `你与汇款人${this.recordData.remitter_name}的汇款证明材料仍在审核中，预计等待24小时审核成功后可继续收款。`,
        status: ProcessItemStatus.waiting,
      }];
    }

    /**
       * 待重新提交材料状态
      */
    if (retrievalDtlEntity.isWaitResubmit) {
      return [{
        title: '请提交证明材料',
        status: ProcessItemStatus.success,
      }, {
        title: '审核中',
        status: ProcessItemStatus.success,
      }, {
        title: '审核失败',
        status: ProcessItemStatus.fail,
        time: filterInitTime(this.retrievalDtlEntity?.createTime || ''),
        desc: `${this.retrievalDtlEntity?.auditFailedReason || ''}`,
      }, {
        title: '重新提交证明材料',
        desc: `你与汇款人${this.recordData.remitter_name}的证明材料审核失败，请在48小时内重新提交材料，避免收款失败。材料审核成功后可继续收款。`,
        time: filterInitTime(this.retrievalDtlEntity?.createTime || ''),
        status: ProcessItemStatus.warn,
        action: {
          text: '去提交',
          type: actionType,
          payload,
        },
      },
      ];
    }

    return defaultConfig;
  }

  /**
   * 失败状态按钮配置
   */
  private async getFailStateBtnConfig(): Promise<ProcessItemActionConfig | undefined> {
    // 非错误状态，不进行后续流程，避免资源消耗
    if (this.itemState !==  ListState.RECEIVE_FAIL) {
      return;
    }

    /**
     * 如果是因有前置调单而失败
    */
    if (this.state.isRetrievalFail) {
      console.log('getFailStateBtnConfig_isRetrievalFail', this.retrievalDtlEntity);
      if (!this.retrievalDtlEntity) {
        return;
      }

      // 前置调单是事中调单
      if (
        this.retrievalDtlEntity.auditType === AuditType.IN_PROCESS_AUDIT_LIST
        && (this.retrievalDtlEntity.isWaitFirstTimeSubmit || this.retrievalDtlEntity.isWaitResubmit)
      ) {
        if (!this.recordData.audit_listid) {
          new CustomException(this.recordData, 'getFailStateBtnConfig_auditListidEmpty', '失败按钮配置audit_listid为空');
          return;
        }
        const payload = { listid: this.recordData.audit_listid };
        return {
          text: '去提交',
          type: ProcessItemAction.goPairUploadFile,
          payload,
        };
      }


      // 前置调单是事后调单
      if (this.retrievalDtlEntity.auditType === AuditType.AFTER_PROCESS_AUDIT_LIST) {
        if (this.retrievalDtlEntity.isWaitFirstTimeSubmit || this.retrievalDtlEntity.isWaitResubmit) {
        // 跳转到事后调单页
          return {
            text: '去提交',
            type: ProcessItemAction.postRetrieveDoc,
          };
        }
        if (this.retrievalDtlEntity.isAuditing) {
          return {
            text: '查看进度',
            type: ProcessItemAction.checkRetrieveProgress,
          };
        }
      }
    }

    if (+this.recordData.fail_code === +FAIL_CODE.COLLECT_CHANGE_FAILED_AND_NEED_BIND_CARD) {
      return {
        text: '如何提额',
        type: ProcessItemAction.showBalanceFailPopup,
      };
    }
    if (+this.recordData.fail_code === +FAIL_CODE.BANK_NO_EMPTY) {
      return {
        text: '去更换',
        type: ProcessItemAction.changeDefaultCard,
      };
    }
  }
}
