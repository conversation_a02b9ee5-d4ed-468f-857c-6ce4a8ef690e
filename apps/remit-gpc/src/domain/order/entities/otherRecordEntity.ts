import { OtherViewStateEntity } from './otherViewStateEntity';
import { OrderAttribute, SharerTranDtError, SharerTranDtRes } from '@/data-source/record/sharer_trandtl_qry';
import { OtherViewProcessEntities } from './process-entities/otherViewProcessEntity';
import { GetTransferInfoResponse } from '@/data-source/record/get_transfer_info';
import { filterInitTime } from '@/adapters/time';


export class OtherViewRecordEntity {
  // 状态实体;
  state: OtherViewStateEntity;

  otherViewProcessEntities: OtherViewProcessEntities;

  private originData: SharerTranDtRes|undefined;

  private transferInfoData: GetTransferInfoResponse|undefined;

  constructor(recordInfo: {
    sharerTranData?: SharerTranDtRes,
    transferInfoData?: GetTransferInfoResponse,
    errCode?: SharerTranDtError
  }) {
    this.otherViewProcessEntities = new OtherViewProcessEntities();
    this.originData = recordInfo.sharerTranData;
    this.transferInfoData = recordInfo.transferInfoData;
    this.state = new OtherViewStateEntity({
      data: this.originData, errCode: recordInfo.errCode,
    });
  }

  /**
   * 是否为本人订单
  */
  get isSelfRecord() {
    return this.originData?.user_attribute === OrderAttribute.SELF;
  }

  /**
   * 是否命中订单关系不明确状态
  */
  get isMatchRecipientNotSure() {
    return this.originData?.user_attribute === OrderAttribute.NOT_SURE;
  }

  /**
   * 创建文字链时间
  */
  get linkTime() {
    return filterInitTime(this.transferInfoData?.link_create_time || '');
  }

  /**
   * 订单受理时间
  */
  get createTime() {
    return filterInitTime(this.originData?.create_time || '');
  }

  /**
   * 稠州开户失败时间
  */
  get openAccountFailTime() {
    return filterInitTime(this.originData?.open_account_fail_time || '');
  }

  /**
   * 审核时间
  */
  get auditTime() {
    return filterInitTime(this.originData?.audit_time || '');
  }

  /**
   * 审核成功时间
  */
  get auditFinalTime() {
    return filterInitTime(this.originData?.audit_final_time || '');
  }

  /**
   * 银行处理中时间
  */
  get payProcessTime() {
    return filterInitTime(this.originData?.pay_process_time || '');
  }

  /**
   * 汇款终状态时间(失败/成功)
  */
  get payFinalTime() {
    return filterInitTime(this.originData?.pay_final_time || '');
  }

  /**
   * 关单时间
  */
  get closeTime() {
    return filterInitTime(this.originData?.close_time || '');
  }

  /**
   * 待提交要素时间
  */
  get basicExtPendTime() {
    return filterInitTime(this.originData?.basic_ext_pend_time || '');
  }

  /**
   * 付汇失败换卡时间
  */
  get payFailTime() {
    return filterInitTime(this.originData?.pay_fail_time || '');
  }

  /**
   * 命中待提交材料时间
  */
  get needSubmitMaterialTime() {
    return filterInitTime(this.originData?.need_submit_material_time || '');
  }
  /**
   * 收款方式不可用时间
   */
  get recvAccountPendTime() {
    return filterInitTime(this.originData?.recv_account_pend_time || '');
  }

  /**
   * 用户完成提交九要素或完成协议签署的时间，提交目前无需审核，提交了信息就可以成功扭转状态了，如果不需要补充要素或者签协议，也会有这个时间
   */
  get completeAccountTime() {
    return filterInitTime(this.originData?.complete_account_time || '');
  }

  /**
   * 20 状态下的最新审核时间
   */
  get lastAuditTimeIn20() {
    return filterInitTime([
      this.originData?.audit_time,
      this.originData?.create_time,
      this.originData?.basic_ext_pend_time,
      this.originData?.open_account_fail_time,
      this.originData?.need_submit_material_time,
      this.originData?.complete_account_time,
    ].reduce((pre, cur) => {
      const curTime = new Date(cur || '').getTime();
      const preTime = new Date(pre || '').getTime();
      if (!preTime) {
        return cur;
      }
      if (!curTime) {
        return pre;
      }
      return curTime > preTime ? cur : pre;
    }, '') || '');
  }

  getProcessList() {
    return this.otherViewProcessEntities.getProcessList(this.state.getRecordScene(), this);
  }
}
