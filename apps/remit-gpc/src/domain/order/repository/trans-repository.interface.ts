import { RecordDetailRes } from '@/data-source/record/trandtl_qry';
import { BaseRes } from '@tencent/weremit-common/global-data/type';

/** 订单详情页接口返回 */
export type TranDtlQryRes = RecordDetailRes;

/**
 * 收款记录信息查询仓储类型定义
 */
export interface TransRepositoryInterface {
  /** 订单详情接口 */
  tranDtlQry(param: {
    /** 订单号 */
    listId: string;
  }): Promise<[err: BaseRes | null, res?: RecordDetailRes]>;
}
