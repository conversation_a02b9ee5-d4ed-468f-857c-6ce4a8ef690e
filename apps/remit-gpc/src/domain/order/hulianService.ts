import { LogState, OrderCheckState, SyncQueryReq, SyncQueryRes, UserState, syncQuery } from '@/data-source/order/order';
import { MaskType, mask, to } from '@/adapters/functions';
import { BaseRes } from '@tencent/weremit-common/global-data/type';
import { BaseStateEntity } from './entities/baseStateEntity';
/**
 * 入口页面
 */
export enum DealPage {
  RECEIPT_ENTRY = '短信定制收款页',
  BANK_CARD = '选卡页',
  COLLECT_INFO = '九要素页',
  SIGN_CONTRACT = '签署协议',
}

/** 互联订单状态 */
export enum HulianOrderState {
  /** 订单未落单，订单还未到达互联侧，需要引导用户手动刷新 */
  NO_ORDER = 'NO_ORDER',
  /** 手机号凭证失效，解析失败 */
  ERROR_MOBILE_INVALID = 'ERROR_MOBILE_INVALID',
  /** 系统错误 */
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  /** 非收款人查单 */
  ERROR_WRONG_PERSON = 'ERROR_WRONG_PERSON',
  /** 订单已关单/关单中 */
  ERROR_EXPIRED = 'ERROR_EXPIRED',
  /** 订单超时关单 */
  ERROR_TIMEOUT = 'ERROR_TIMEOUT',
  /** 订单已被汇款人取消 */
  ERROR_CANCEL = 'ERROR_CANCEL',
  /** 机构余额不足情况下订单被取消 */
  ORG_BALANCE_NOT_ENOUGH_CANCEL_ORDER = 'orgBalanceNotEnoughCancelOrder',
  /** 校验年龄\地区等不合法关单 */
  ERROR_ILLEGAL = 'ERROR_ILLEGAL',
  /** 姓名不匹配关单 */
  ERROR_NAME_MISMATCH = 'ERROR_NAME_MISMATCH',
  /** 手机号校验不一致 */
  ERROR_MOBILE_CHECK_FAIL = 'ERROR_MOBILE_CHECK_FAIL',
  /** 姓名校验不一致 */
  ERROR_NAME_CHECK_FAIL = 'ERROR_NAME_CHECK_FAIL',
  /** 需补充九要素/协议 */
  NEED_CONTRACT = 'NEED_CONTRACT',
  /** 需签署承诺函/材料 */
  NEED_SIGN_FILES = 'NEED_SIGN_FILES',
  /** 需换收款方式 */
  CHANGE_ACCOUNT = 'CHANGE_ACCOUNT',
  /** 收款成功 */
  RECEIVE_SUC = 'RECEIVE_SUC',
  /** 收款流程内，收款失败 */
  RECEIVED_FAILED = 'RECEIVED_FAILED',
  /** 收款流程内，签署协议后处理中状态 */
  CONTRACT_PROCESS = 'CONTRACT_PROCESS',
  /** 收款后(>20020)的状态 */
  AFTER_RECEIVED = 'AFTER_RECEIVED',
  /** 订单已同步，但在20020前的状态，需要引导用户操作 */
  SYNCED = 'SYNCED',
  /** 用户未开户 */
  NO_ACCOUNT = 'NO_ACCOUNT',
  /** 用户已开户，无手机号 */
  NO_MOBILE = 'NO_MOBILE',
  /** 订单待收款状态 */
  WAIT_RECEIVE = 'WAIT_RECEIVE',
}

/** 互联接口错误码 */
export enum HulianOrderErrCode {
  WRONG_PERSON = '**********', // 错误的收款人点进来
  NO_ORDER = '**********', // 互联来的订单尚未落单
  MOBILE_INVALID = '**********', // 手机号凭证失效，解析失败
}

export interface OrderStateRes {
  result: HulianOrderState,
  err?: BaseRes,
  data: SyncQueryRes | null,
}
export class HulianService {
  /**
   * 查询 sync_query 接口然后判断出当前互联订单状态
   * @param data sync_query 接口请求参数
   * @returns 互联订单状态
   */
  public static async getOrderState(data: SyncQueryReq): Promise<OrderStateRes> {
    const [err, res] = await to(syncQuery(data));
    return this.getOrderStateBySyncQueryRes(err, res!);
  }
  /**
   * 根据 sync_query 的接口返回结果判断订单状态
   * @param err 请求报错
   * @param res 请求结果
   * @returns 订单状态
   */
  public static async getOrderStateBySyncQueryRes(err: Error | null, res: SyncQueryRes) {
    if (err) {
      // 根据错误码判断订单状态
      return {
        result: this.getStateByErrCode(err),
        err: err as unknown as BaseRes,
        data: null,
      };
    }
    // 根据查单数据判断订单状态
    return {
      result: this.getStateByOrderInfo(res),
      data: res,
    };
  }

  public static getErrorPageTitle(state, orderInfo) {
    return {
      [HulianOrderState.ERROR_WRONG_PERSON]: '请使用正确的微信收款',
      [HulianOrderState.ERROR_EXPIRED]: '超过48小时未确认收款，资金已原路退回汇款人',
      [HulianOrderState.ERROR_TIMEOUT]: '超过48小时未确认收款，资金已原路退回汇款人',
      [HulianOrderState.ERROR_CANCEL]: '该笔订单已被汇款机构取消，若有疑问可经汇款人咨询对应汇款机构',
      [HulianOrderState.ORG_BALANCE_NOT_ENOUGH_CANCEL_ORDER]: '该笔订单已被汇款机构取消，若有疑问可经汇款人咨询对应汇款机构',
      [HulianOrderState.ERROR_ILLEGAL]: '收款失败，资金将原路退回至汇款人',
      [HulianOrderState.ERROR_NAME_MISMATCH]: `实名信息(${mask(orderInfo?.receiver_name || '', MaskType.name)})与收款人姓名不一致`,
      [HulianOrderState.ERROR_MOBILE_CHECK_FAIL]: `授权手机号(${mask(orderInfo?.auth_phone || '', MaskType.phone)})与此订单收款手机号不一致，请使用正确手机号收款`,
      [HulianOrderState.ERROR_NAME_CHECK_FAIL]: `实名信息(${mask(orderInfo?.receiver_name || '', MaskType.name)})与收款人姓名不一致`,
    }[state];
  }

  /** 根据查单数据判断订单状态 */
  private static getStateByOrderInfo(orderInfo: SyncQueryRes) {
    const logState = Number(orderInfo.log_state);
    const userState = Number(orderInfo.user_state);
    const checkState = Number(orderInfo.check_state);

    // 订单已同步（已收款）的处理
    if ([LogState.SYNCED].includes(logState)) {
      return this.getOrderStateByOrderInfo(orderInfo);
    }
    // 机构余额不足-取消订单
    if ([LogState.ORG_BALANCE_NOT_ENOUGH_CANCEL_ORDER].includes(logState)) {
      return this.getOrderStateByLogState(logState);
    }

    // 登记簿状态非待关联和已同步状态的订单处理
    if (![LogState.WAIT_LOG, LogState.SYNCED].includes(logState)) {
      return this.getOrderStateByLogState(logState);
    }

    // 订单校验失败的处理（手机号、姓名校验不通过）
    if (![OrderCheckState.SUC].includes(checkState)) {
      return this.getOrderStateByCheckState(checkState);
    }

    // 订单按用户状态进行处理（未开户、无手机号）
    if (![UserState.HAS_ACCOUNT].includes(userState)) {
      return this.getOrderStateByUserState(userState);
    }

    // 其他订单未关联情况，按待收款状态处理，让用户再次授权手机号收款
    return HulianOrderState.WAIT_RECEIVE;
  }

  private static getOrderStateByOrderInfo(orderInfo: SyncQueryRes) {
    const stateEntity = new BaseStateEntity(orderInfo.state, (orderInfo.need_sign_contract || 0));
    if (stateEntity.isNeedSignContract) {
      return HulianOrderState.NEED_CONTRACT;
    }
    if (stateEntity.isNeedWaitSign) {
      return HulianOrderState.NEED_SIGN_FILES;
    }
    if (stateEntity.isAccountUnableWaitChange || stateEntity.isChangeBankAccount || stateEntity.isChangeBankCard) {
      return HulianOrderState.CHANGE_ACCOUNT;
    }
    if (stateEntity.isSucceed) {
      return HulianOrderState.RECEIVE_SUC;
    }
    if (stateEntity.isFailed) {
      return HulianOrderState.RECEIVED_FAILED;
    }
    if (stateEntity.isSignedContractButStillState) {
      return HulianOrderState.CONTRACT_PROCESS;
    }
    if (stateEntity.isAfterReceivedState) {
      return HulianOrderState.AFTER_RECEIVED;
    }
    return HulianOrderState.SYNCED;
  }

  private static getOrderStateByLogState(logState: LogState) {
    return {
      [LogState.CLOSED]: HulianOrderState.ERROR_EXPIRED,
      [LogState.CLOSING]: HulianOrderState.ERROR_EXPIRED,
      [LogState.CLOSED_TIMEOUT]: HulianOrderState.ERROR_TIMEOUT,
      [LogState.CANCEL]: HulianOrderState.ERROR_CANCEL,
      [LogState.ORG_BALANCE_NOT_ENOUGH_CANCEL_ORDER]: HulianOrderState.ORG_BALANCE_NOT_ENOUGH_CANCEL_ORDER,
      [LogState.CLOSED_ILLEGAL]: HulianOrderState.ERROR_ILLEGAL,
      [LogState.CLOSED_NAME_MISMATCH]: HulianOrderState.ERROR_NAME_MISMATCH,
    }[logState];
  }

  private static getOrderStateByCheckState(checkState: OrderCheckState) {
    return {
      [OrderCheckState.MOBILE_FAIL]: HulianOrderState.ERROR_MOBILE_CHECK_FAIL,
      [OrderCheckState.NAME_FAIL]: HulianOrderState.ERROR_NAME_CHECK_FAIL,
    }[checkState];
  }

  private static getOrderStateByUserState(userState: UserState) {
    if (userState === UserState.NO_ACCOUNT) {
      return HulianOrderState.NO_ACCOUNT;
    }
    if (userState === UserState.NO_MOBILE) {
      return HulianOrderState.NO_MOBILE;
    }
  }

  /**
   * 处理接口错误码
   * @param err
   * @returns
   */
  private static getStateByErrCode(err) {
    // 处理接口错误码
    switch ((err as unknown as BaseRes)?.retcode) {
      case HulianOrderErrCode.NO_ORDER:
        return HulianOrderState.NO_ORDER;
      case HulianOrderErrCode.WRONG_PERSON:
        return HulianOrderState.ERROR_WRONG_PERSON;
      case HulianOrderErrCode.MOBILE_INVALID:
        return HulianOrderState.ERROR_MOBILE_INVALID;
    }
    return HulianOrderState.SYSTEM_ERROR;
  }
}
