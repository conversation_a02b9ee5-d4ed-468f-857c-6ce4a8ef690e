
import { Service } from 'typedi';
import { BaseEntity } from '@tencent/ppd-uni-domain';
import { ContractType, ProtocolBranchConfig } from '../repository';
// 视图层协议配置
export interface BranchTypeItem {
  branch: ContractType;// 渠道
  isSigned: boolean;// 是否签约 默认未签约
}

@Service({ transient: true })
export class SignContractEntity extends BaseEntity {
  public branchTypeList: ContractType[] = [];
  constructor(
    protocolBranchConfigList: ProtocolBranchConfig[],
    branchType?: string | ContractType[],
    hasOrder?: string | number | boolean,
  ) {
    super();
    let branchTypeList: ContractType[] = [];
    // 对配置进行排序
    const configList = this.sortBranchConfigList(protocolBranchConfigList);
    // 参数归一
    if (Array.isArray(branchType)) {
      branchTypeList = branchType;
    } else if (branchType) {
      branchTypeList = branchType ? branchType.split('|') as ContractType [] : [];;
    }
    let hasOrderBool = true;
    if (hasOrder === '0' || !hasOrder) {
      hasOrderBool = false;
    }

    if (branchTypeList.length === 1) {
      // log.info('签署协议：有指定协议且只有一个');
      // // 有指定协议且只有一个
      this.branchTypeList = branchTypeList;
      return;
    }
    // const branchTypeList = branchType ? branchType.split('|') as ContractType [] : [];// 渠道协议数组
    const weightBranchList = configList.map(item => item.branch);// ['4639','4638', '1001', '1002']

    if (hasOrderBool && branchTypeList) {
      // 有单有多个协议 按照后端协议为准 前端负责排序
      // 排序后的协议列表（包括不显示的）
      branchTypeList = this.sortBranchList(branchTypeList, weightBranchList);
    } else if (!hasOrderBool) {
      // 无单 前端根据配置和后端返回来排序和过滤
      branchTypeList = this.formatBranchConfigList(
        protocolBranchConfigList,
        branchTypeList,
      );
    }
    this.branchTypeList = branchTypeList;
  }
  /**
   * 从已经排序的配置列表中挑选出
   * @param branchTypeArray
   * @param sortList
   * @returns
   * 例子: sortBranchList([4,5,1,2,3],[1,2,3]) = [1,2,3,4,5]
   */
  private sortBranchList(branchTypeArray: ContractType [], sortList: ContractType[]): ContractType [] {
    const originList = [...branchTypeArray];// 先浅拷贝一份
    const output: ContractType [] = [];
    sortList.forEach((key) => { // 已经排序过的数组
      const index = originList.indexOf(key);
      if (index > -1) {
        // 说明有
        const deleteItemList = originList.splice(index, 1);
        output.push(...deleteItemList);
      }
    });
    return [...output, ...originList];
  }
  /**
   * 过滤显示和排序
   * @param configList
   * @param protocolBranchList
   * @returns
   */
  private formatBranchConfigList(
    configList: ProtocolBranchConfig[],
    protocolBranchList: ContractType [],
  ): ContractType[] {
    const filterList = configList.filter(item => protocolBranchList.includes(item.branch) && +item.isShow === 1);
    return this.sortBranchConfigList(filterList).map(item => item.branch);
  }
  private sortBranchConfigList(configList: ProtocolBranchConfig[]): ProtocolBranchConfig[] {
    return configList.sort((a, b) => (+a.weight > +b.weight ? -1 : 1));
  }
}
