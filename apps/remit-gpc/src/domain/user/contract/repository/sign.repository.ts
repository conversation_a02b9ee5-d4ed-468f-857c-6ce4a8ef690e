

// 协议类型
export enum ContractType {
  CHOUZHOU_VER_1 = '4638', // 稠州1.0协议
  GUANGGONG_VER_1 = '4639', // 广工1.0协议
  CHOUZHOU_VER_2 = '1001', // 稠州2.0协议
  GUANGGONG_VER_2 = '1002', // 广工2.0协议
  HENGFENG_VER_1 = '1003', // 恒丰1.0协议
}
// 前端渠道协议配置
export interface ProtocolBranchConfig{
  branch: ContractType;// 渠道
  weight: string;// 权重
  isShow: '1'|'0';// 是否显示
}
export interface SignContractRepository{
  getProtocolBranchConfigList: () => Promise<ProtocolBranchConfig[]>
}

