import { to } from '@/adapters';
import { CustomException } from '@/adapters/report';
import { queryIsSubscribe } from '@/data-source/user/subscribe';

/**
 * 关注公众号Service
*/
export class SubscribeService {
  /**
   * 请求关注公众号singlePromise
  */
  private static queryIsSubscribeSinglePromise: null|Promise<boolean> = null;

  /**
   * 获取用户关注公众号状态
   * 懒汉+单例模式，如果此前有singlePromise了，则返回singlePromise，否则
   * @params forceRefreshFlag 强制刷新标志位，若为true则将刷新singlePromise
  */
  public static async getUserSubscribeState(forceRefreshFlag = false) {
    if (this.queryIsSubscribeSinglePromise && !forceRefreshFlag) {
      return this.queryIsSubscribeSinglePromise;
    }
    // 如果reject清空promise
    this.queryIsSubscribeSinglePromise = this.queryIsSubscribe();
    return this.queryIsSubscribeSinglePromise;
  }

  /**
   * 调用后台接口查询关注公众号状态
   * 这个return null;改成reject
  */
  private static async queryIsSubscribe() {
    const [err, res] = await to(queryIsSubscribe());
    if (err || !res) {
      new CustomException(err, 'queryIsSubscribe_err', '获取用户是否关注公众号失败');
      throw (err);
    }
    return +res?.is_subscribe === 1;
  };
}
