<template>
  <view
    v-stat.brow="'cre_pic_authorization.brow'"
    class="flex flex-col items-center justify-center h-100vh pl-44rpx pr-44rpx bg-white"
  >
    <view class="flex-1">
      <view class="title3-sem color-text-title mt-80rpx text-center">
        证件影印件授权
      </view>
      <view class="text4-reg color-text-title text-center mt-40rpx">
        根据合作银行与财付通反洗钱要求，完成收款需授权你的证件影印件。
      </view>
    </view>
    <view class="flex flex-col items-center justify-center mb-128rpx">
      <UserAgree
        v-if="isNeedSignUserInfoProtocol"
        v-model="isProtocolChecked"
        v-stat.brow="'cre_pic_authorization.user_protocol'"
        @check-change="onProtocolCheck"
      />
      <view class="mt-32rpx">
        <payment-password-verify
          :disabled="isBtnDisabled"
          :get-sign-func="getSignParamsFunc"
          @success="verifySuccess"
          @get-sign-fail="onGetSignFail"
          @start-verify="startVerify"
          @end-verify="endVerify"
        >
          <view
            v-stat.click="'cre_pic_authorization.confirm'"
            :data-stat="JSON.stringify({ disabled: isBtnDisabled ? 1 : 0 })"
          >
            <p-button
              type="primary"
              size="large"
              :disabled="isBtnDisabled"
              :custom-style="{
                width: '368rpx',
              }"
            >
              授权
            </p-button>
          </view>
        </payment-password-verify>
      </view>
      <text
        v-stat.click="'cre_pic_authorization.cancel'"
        class="text5-sem color-primary-normal p-20rpx mt-12rpx lh-48rpx"
        @click="cancelVerify"
      >
        暂不授权
      </text>
    </view>
    <safe-bottom />
  </view>
</template>
<script setup lang="ts">
import SafeBottom from '@tencent/ppd-uni-component/packages/safe-bottom/index.vue';
import PaymentPasswordVerify from '@tencent/ppd-uni-component/packages/payment-password-verify/index.vue';
import UserAgree from '../user-agree/index.vue';
import { PaymentPasswordVerifySuccessParams } from '@tencent/ppd-uni-component/packages/payment-password-verify/types';
import { useKycForm } from './kyc';
const props = defineProps<{
  isNeedSignUserInfoProtocol: boolean;
}>();
const emit = defineEmits<{(e: 'submit', data: PaymentPasswordVerifySuccessParams): void, (e: 'cancel')}>();

const { isBtnDisabled,
  isProtocolChecked, onProtocolCheck, getSignParamsFunc, onGetSignFail, startVerify, endVerify } = useKycForm(props);

const verifySuccess = (data: PaymentPasswordVerifySuccessParams) => {
  emit('submit', data);
};

const cancelVerify = () => {
  emit('cancel');
};
</script>

<style scoped lang="scss">
</style>
