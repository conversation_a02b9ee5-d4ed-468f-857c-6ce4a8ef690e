import { ContractType } from '@/domain/user';
import { ComponentData } from '@tencent/ppd-uni-component/packages/rich-text/type';

export interface MultipleProp{
  type: MultipleContractType,
  index: number,
  title: string,
  content: string,
  contractText: string,
  contractData: Record<string, ComponentData>;
  contractTypes: ContractType[],
}

export interface SingleProp {
  type: ContractType,
  content: string[],
  contentData: Record<string, ComponentData>
}

// 多协议签署类型
export enum MultipleContractType {
  /**
   * 财付通负责信息传输的渠道，如：稠州、恒丰
   * 在多渠道签署流程中，多个银行渠道一起签署
   */
  ContractInfoType = 'ContractInfoType',
  /**
   * 财付通负责资金分发的渠道：广工
   */
  ContractFundType = 'ContractFundType',
}
