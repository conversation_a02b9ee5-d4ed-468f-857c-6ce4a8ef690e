<template>
  <view>
    <WeremitAuthAndSelectCardPage v-if="showAuthAndSelectCard" />
    <UserLinkCommon />
  </view>
</template>

<script setup lang="ts">
import { provide, ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { EOpenAccountBusChannel, IToUniOpenAccountPageParams } from '@tencent/weremit-common/route/package-gpc-uni';
import WeremitAuthAndSelectCardPage from './components/weremit-auth-and-select-card/index.vue';
import { useOrderRouterPage } from '../compositions/use-order-router-page';
import { IToAuthAndSelectCardOptions } from '../compositions/type';
import reportInstance from '@/adapters/report';
import { biBrow } from '@tencent/fit-bi-sdk';
import { decodeObjectData } from '@/adapters/object';
import UserLinkCommon from '@/components/user-link-common/index.vue';

const showAuthAndSelectCard = ref(false);
const toAuthAndSelectCardOptions = ref<IToAuthAndSelectCardOptions>({});

const urlParams = ref<IToUniOpenAccountPageParams>({});
// 极简开户实验
provide('urlParams', urlParams);
onLoad((options) => {
  const decodedOptions = decodeObjectData(options);
  urlParams.value = decodedOptions as unknown as IToUniOpenAccountPageParams;
  const {
    orderRouterPageController,
  } = useOrderRouterPage(decodedOptions, {
    showAuthAndSelectCard,
    toAuthAndSelectCardOptions,
  });
  switch (urlParams.value.quickReceiptChannel) {
    case EOpenAccountBusChannel.HK2CN:
      orderRouterPageController.toHk2cnRouter({});
      break;
    case EOpenAccountBusChannel.MESSAGE:
      orderRouterPageController.toHulianRouter({});
      break;
    default:
      orderRouterPageController.toStandardRouter({});
      break;
  }
  const pages = getCurrentPages();
  biBrow('order-router-page.brow', {
    entrance: pages?.[pages?.length - 2]?.name || pages?.[pages?.length - 2]?.route,
  });
});

onShow(() => {
  reportInstance.reportInfo({
    key: 'order-router-page-onShow',
  });
  // 这里的逻辑收拢到 useAuthAndSelectCardPage 中
});

</script>
