/**
 * 联动收款页面hooks
*/
import { OpenAccountSpeedReporter, OpenAccountTimePoint } from '@/hooks/speed-report';
import { AccountService } from '@/domain/user/accountService';
import { UserState } from '@/domain/user/type';
import { cardHooks } from '../../compositions/receive/cardHooks';
import { bankHooks } from '../../compositions/receive/bankHooks';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { phoneHooks } from '../../compositions/receive/phoneHooks';
// import { pageDataHooks } from '../../compositions/receive/pageDataHooks';
import { hideLoading, showLoading, showToast } from '@/adapters/uni-api-common';
import { ExperimentService } from '@/domain/experiment';
import { Chain, Task } from '@/adapters/design-mode/chain';
import { startUserLinkProcess } from '@/hooks/user-link';
import { UserLinkScene, UserLinkSubScene } from '@tencent/weremit-common';

import { mpConnectHooks, WeRemitReturnExtraData } from '@/hooks/mp-connect/index';

export enum AfterOpenAccountTask {
  NameListSelect = 'NameListSelect',
  SubscribePopup = 'SubscribePopup',
  PromPopup = 'PromPopup'
}

const isCreatedIdWithPolyphonicWord = ref(false);

const isGoCreatedId = ref(false);


export const useConnectReceive = (changeCardRef) => {
  const {
    initCard,
    cardInfo,
    changeNameEn,
  } = cardHooks();

  const {
    updateMobileSuc,
  } = phoneHooks();

  const changeCardSuc = () => {
    showToast({ icon: 'success', title: '更换成功' });
    initUserState();
  };

  const {
    isPaymentMethodPopupVisible,
    handleHasCardChoosePaymentMethod,
    handlePaymentMethodPopupSelectOtherCard,
    updateBindCardResult,
    closePaymentMethodPopup,
  } = bankHooks(changeCardRef);

  const {
    getNavigateToMiniProgramExtraData,
    setReturnDataIntoClipboard,
  } = mpConnectHooks();

  const navigateToMiniProgramExtraData = ref<WeRemitReturnExtraData>();

  const afterOpenAccountPromShow = ref(false);
  let afterOpenAccountChain  = getChain();
  function getChain() {
    return new Chain<AfterOpenAccountTask>({
      taskList: [
        new Task<AfterOpenAccountTask>(AfterOpenAccountTask.NameListSelect, undefined, {
          beforeStart: async (task) => {
          // 如果不需要多音字，直接resolve，否则弹出多音字弹窗
            if (cardInfo?.value?.hasPolyphonicWord) {
              isCreatedIdWithPolyphonicWord.value = true;
              return;
            }
            task.resolve();
          },
        }),
      ],
      onComplete() {
        afterOpenAccountChain = getChain();
      },
    });
  }

  /**
   * 【核心】用户状态，对应收款名片页的四种展示状态
  */
  const userState = ref<UserState>(UserState.UNKNOWN);

  /**
   * 首次加载名片标志位
   * 非首次加载名片时，页面是静默更新的
  */
  let firstLoadCard = true;

  /**
   * onLoadHooks阶段执行的方法
  */
  onLoad(() => {
    console.log('【线下汇款】onLoad');
    // 初始化用户状态
    initUserState();
  });

  /**
   * onShow阶段执行的方法
  */
  onShow(() => {
    console.log('【线下汇款】onShow');
    (async () => {
      console.log('【线下汇款】onShow', firstLoadCard);
      /**
       * 3、页面onShow阶段重新初始化用户状态
       * 页面onLoad阶段也有一次初始化用户状态操作，为避免重复请求，这里有一个firstLoadCard标志位
       * TODO 这里有重复执行的问题，处理完选卡返回/九要素后，还会走到这个init，会多一次接口请求
      */
      if (!firstLoadCard) {
        await initUserState();
      }

      dealReportAfterOpenAccount();

      // 5、处理创建名片返回后自动展示弹层逻辑
      dealAutoShowPopupAfterOpenAccount();

      // 6、将开户标志位置为false
      isGoCreatedId.value = false;
    })();
  });

  /**
   * 处理创建名片返回后自动展示弹层逻辑
   *
   * TODO 当前的if只判断了选卡返回，没有判断用户的名片状态
   * 当前是在职责链各任务里兜住了状态判断。可以将此逻辑写到if里
  */
  const dealAutoShowPopupAfterOpenAccount = () => {
    // 如果命中从创建名片页返回
    if (isGoCreatedId.value) {
      // TODO 多音字弹窗
      afterOpenAccountChain.run();
    }
  };

  const dealReportAfterOpenAccount = () => {
    const isMatchSimpleOpenAccount = ExperimentService.getSimpleOpenAccountExperimentEntity().doesMatch;
    if (isGoCreatedId.value && !isMatchSimpleOpenAccount && [
      UserState.HAS_CARD_AND_ORDER,
      UserState.HAS_CARD_WITHOUT_ORDER,
    ].includes(userState.value)) {
      OpenAccountSpeedReporter.setTimePointInfo(
        'standard',
        OpenAccountTimePoint.onEnd,
      );
      OpenAccountSpeedReporter.onEndReport('standard');
    }
  };

  /**
   * 初始化用户状态
   * 对应旧名片页的init的地方
  */
  const initUserState = async () => {
    console.log('【线下汇款】initUserState', isGoCreatedId.value);
    // 开户流程返回时，展示loading
    if (isGoCreatedId.value) {
      // 注：从开户流程返回时，原生项目里可能还有一些hideLoading会执行，会导致showLoading失败。故此处给showLoading加一个延时
      setTimeout(() => {
        showLoading();
      }, 30);
    }
    const originUserState = await AccountService.getUserState();
    console.log('【线下汇款】initUserState', originUserState);

    if (originUserState === UserState.WITHOUT_CARD) {
      isGoCreatedId.value = true;
      // 自动开启开户流程
      startUserLinkProcess(UserLinkScene.STANDARD, undefined, UserLinkSubScene.OFFLINE_REMIT_CONNECT);
      return;
    }

    if (isGoCreatedId) {
      setTimeout(() => {
        hideLoading();
      }, 35);
    }

    // 如果命中了要展示收款名片的状态
    if ([UserState.HAS_CARD_AND_ORDER, UserState.HAS_CARD_WITHOUT_ORDER].includes(originUserState)) {
      // 展示/更新收款名片
      await showCard();

      /**
       * 初始化要回传给第三方的数据
       * 含初始化剪贴板和小程序回跳数据
      */
      initReturnDataToOfflineRemit();
    }
    firstLoadCard = false;
    // 获取用户状态
    userState.value =  originUserState;
  };

  /**
   * 重置用户状态
   * 将用户状态扭转会初始态
  */
  const resetUserState = () => {
    userState.value = UserState.UNKNOWN;
  };

  /**
   * 展示/更新收款名片方法
   * 对应dealHasCard方法和showCardInfo方法
  */
  const showCard = async () => {
    await initCard();
  };

  const initReturnDataToOfflineRemit = async () => {
    navigateToMiniProgramExtraData.value = await getNavigateToMiniProgramExtraData();
    console.log('【线下汇款】initReturnDataToOfflineRemit', navigateToMiniProgramExtraData.value);
    setReturnDataIntoClipboard();
  };

  return {
    userState,
    cardInfo,
    isPaymentMethodPopupVisible,
    initUserState,
    resetUserState,
    changeNameEn,
    updateMobileSuc,
    handleHasCardChoosePaymentMethod,
    handlePaymentMethodPopupSelectOtherCard,
    updateBindCardResult,
    changeCardSuc,
    closePaymentMethodPopup,
    afterOpenAccountPromShow,
    showCard,
    navigateToMiniProgramExtraData,
    initReturnDataToOfflineRemit,
  };
};
