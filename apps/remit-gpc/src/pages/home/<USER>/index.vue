<template>
  <view
    class="page observer-container min-h-full flex-col flex justify-between"
  >
    <view class="top">
      <main-title
        custom-title="便捷汇款，快速到账"
        custom-title-tips="支持全球60+国家及地区汇至中国"
      />
      <view
        v-stat.brow="'remit_container.brow'"
        class="panel-card pt-8rpx pb-32rpx pl-32rpx pr-32rpx"
      >
        <view
          class="mt-40rpx border-b-1"
          :class="sellErrorList.length ? 'border-state-error' : 'border-state-inactive'"
        >
          <view class="flex justify-between text5-reg color-N-N700">
            <view>汇出地｜币种</view>
            <view>汇出金额</view>
          </view>
          <view class="mt-24rpx pt-20rpx pb-20rpx flex justify-between color-text-title">
            <view
              class="flex items-center"
              @click="onClickRemitCountryInfo"
            >
              <image
                :src="remitCountryInfo?.logo"
                class="w-56rpx h-56rpx"
              />
              <view class="ml-16rpx mr-16rpx whitespace-nowrap flex items-center">
                <text
                  class="font-600"
                  :class="remitCountryInfoFontStyle"
                >
                  {{ remitCountryInfo?.nameCn }} |
                </text>
                <text
                  class="number5"
                  :class="remitCountryInfoFontStyle"
                >
                  &nbsp;{{ remitCountryInfo?.currencyCode }}
                </text>
              </view>
              <p-icon
                size="32rpx"
                class="color-icon-primary"
                name="arrow-down"
              />
            </view>
            <amount-input
              ref="sellAmountInputRef"
              class="w-290rpx text-right"
              :input-class-step="{
                10: 'number4',
                11: 'number5',
              }"
              :max="sellAmountMax"
              :placeholder="sellPlaceholder"
              :placeholder-class="sellPlaceholderClass"
              :model-value="sellAmount"
              :digit="remitCountryInfo.decimals"
              @update:model-value="onSellAmountInputChange"
              @blur="onSellAmountInputBlur"
              @focus="onSellAmountInputFocusFunc"
            />
          </view>
        </view>
        <view
          v-if="sellErrorList.length > 0"
          v-stat.brow="'remit_container.outward_overlimit'"
          class="mt-16rpx text-24rpx lh-32rpx text-right color-state-error"
        >
          {{ sellErrorList[0].msg }}
        </view>
        <view
          class="border-b-1"
          :class="[
            buyErrorList.length ? 'border-state-error' : 'border-state-inactive',
            buyErrorList.length ? 'mt-24rpx' : 'mt-40rpx',
          ]"
        >
          <view class="flex justify-between text5-reg color-N-N700">
            <view>收款地｜币种</view>
            <view>{{ isSiRate ? '预估最多可收到' : '预估可收到' }}</view>
          </view>
          <view class="mt-24rpx pt-20rpx pb-20rpx flex justify-between color-text-title">
            <view class="flex items-center">
              <image
                :src="receiveCountryInfo?.logo"
                class="w-56rpx h-56rpx"
              />
              <view class="ml-16rpx mr-16rpx text-36rpx lh-48rpx">
                <text class="font-600">
                  {{ receiveCountryNameCn }} |
                </text>
                <text class="number5 font-500">
                  {{ receiveCountryInfo?.currencyCode }}
                </text>
              </view>
            </view>
            <amount-input
              ref="buyAmountInputRef"
              class="w-290rpx text-right"
              :input-class-step="
                {
                  10: 'number4',
                  11: 'number5',
                }
              "
              :max="buyAmountMax"
              :placeholder="buyPlaceholder"
              :placeholder-class="buyPlaceholderClass"
              :model-value="buyAmount"
              :digit="receiveCountryInfo.decimals"
              @update:model-value="onBuyAmountInputChange"
              @blur="onBuyAmountInputBlur"
              @focus="onBuyAmountInputFocusFunc"
            />
          </view>
        </view>
        <view
          v-if="buyErrorList.length > 0"
          v-stat.brow="'remit_container.receive_overlimit'"
          style="color: #F3363B"
          class="mt-16rpx text-24rpx lh-32rpx text-right color-state-error"
        >
          {{ buyErrorList[0].msg }}
        </view>
        <view
          class="w-432rpx h-88rpx mt-48rpx ml-auto mr-auto
          lh-88rpx text-center border-rd-8rpx
        color-text-white-title text-34rpx font-600"
          :class="{
            'bg-primary-disable': !isRemitButtonEnable,
            'bg-primary-normal': isRemitButtonEnable,
          }"
          @click="onClickRemitButton"
        >
          开始汇款
        </view>
      </view>
      <view class="panel-card clear-bg">
        <prom
          :prom-style="''"
          :page-id="PageId.remitHome"
          :position-id="PositionId[PageId.remitHome].BANNER"
          :default-entities="[]"
          :ext-info="{ country: remitCountryInfo.key }"
        />
      </view>
      <view class="panel-card mt-24rpx">
        <guide-list type="remitList" />
      </view>
    </view>
    <view class="bottom">
      <bottom-link />
      <safe-bottom />
    </view>
  </view>
</template>

<script setup lang="ts">
import { PageId, PositionId } from '@/data-source/prom/dp-config';
// 组件
import safeBottom from '@tencent/ppd-uni-component/packages/safe-bottom/index.vue';
import PIcon from '@tencent/fui-component-library/src/components/p-icon/index.vue';
import AmountInput from '@tencent/ppd-uni-component/packages/amount-input/index.vue';
import MainTitle from '@/components/main-title/index.vue';
import Prom from '@/components/prom/index.vue';
import GuideList from '../components/guide/index.vue';
import BottomLink from '../components/bottom-link/index.vue';
import { NavigateType, SharedStorageKey, getCurPageOptions, globalStorage, uniGpcRemitRoute } from '@tencent/weremit-common';
import { EmitEvent, HideHotArea, navigateToCountrySelect } from '@tencent/weremit-common/route/package-fastreceipt-uni';
import { biClick } from '@tencent/fit-bi-sdk';
import { CountryItem, getCountryConf } from '@tencent/ppd-uni-common/src/country';
import { onShareAppMessage, onShow } from '@dcloudio/uni-app';
import { useInstitutionInquiry } from '../compositions/remit/useInstitutionInquiry';
import { isValidatedInt } from '@/adapters';
import { AmountInputRef } from '@tencent/ppd-uni-component/packages/amount-input/types';
import { getShareData } from '@/adapters/share';
import { createRemitHomeUrl } from '@tencent/weremit-common/route/package-gpc-uni';

function getCountryKeyFromStorage() {
  // 获取缓存数据
  const storageData = globalStorage.getItem<CountryItem>(SharedStorageKey.SELECTED_COUNTRY);
  if (storageData) {
    return storageData.key;
  }
}

const sellAmountInputRef = ref<AmountInputRef>();
const buyAmountInputRef = ref<AmountInputRef>();

const {
  remitCountryInfo,
  receiveCountryInfo,
  currentInquiryInfo,
  updateCountryCode,
  sellAmount,
  sellAmountMax,
  buyAmount,
  buyAmountMax,
  sellErrorList,
  buyErrorList,
  sellPlaceholder,
  buyPlaceholder,
  isRemitInfoValid,
  onSellAmountInputChange,
  onBuyAmountInputChange,
  onSellAmountInputBlur,
  onBuyAmountInputBlur,
  onSellAmountInputFocus,
  onBuyAmountInputFocus,
} = useInstitutionInquiry({
  countryCode: getCountryKeyFromStorage(),
  sellAmountInputRef,
  buyAmountInputRef,
});

const isSiRate = computed(() => !!currentInquiryInfo.value?.hasInstitutionRate);

/**
 * 汇入国家的中文名称
 */
const receiveCountryNameCn = computed(() => (['HKG', 'MAC'].includes(remitCountryInfo.value?.key) ? `${receiveCountryInfo.value?.nameCn}内地` : receiveCountryInfo.value?.nameCn));
/**
 * 如果汇出金额的占位符是数字，则使用默认值number样式，否则使用text样式
 */
const sellPlaceholderClass = computed(() => {
  if (isValidatedInt(sellPlaceholder.value)) {
    return 'color-N-N700';
  }
  return 'text4-reg color-N-N700';
});

/**
 * 如果接收金额的占位符是数字，则使用默认值number样式，否则使用text样式
 */
const buyPlaceholderClass = computed(() => {
  if (isValidatedInt(buyPlaceholder.value)) {
    return 'color-N-N700';
  }
  return 'text4-reg color-N-N700';
});
/**
 * 开始汇款按钮是否可点击
 */
const isRemitButtonEnable = computed(() => isRemitInfoValid.value);
/**
 * 根据汇入国家的名称长度，决定是否使用小一号的字体
 */
const remitCountryInfoFontStyle = computed(() => {
  const shouldUseSmallerFontSize = Number(remitCountryInfo?.value?.nameCn?.length) > 4;
  return shouldUseSmallerFontSize ? 'text-28rpx' : 'text-36rpx';
});
let isFirstShow = true;
// 页面onshow的时候，从storage中获取
onShow(() => {
  const optionsCode = getCurPageOptions().countryCode;
  // 如果首次打开页面，并且url参数里面有值，则从将url的值写入缓存
  if (isFirstShow && optionsCode) {
    const countryInfo = getCountryConf(optionsCode);
    // 只有国家参数符合要求，才写入缓存
    if (countryInfo) {
      globalStorage.setItem(SharedStorageKey.SELECTED_COUNTRY, countryInfo);
    }
  }
  const countryKey = getCountryKeyFromStorage();
  if (countryKey) {
    updateCountryCode(countryKey);
  }
  isFirstShow = false;
});

// 选择国家点击
function onClickRemitCountryInfo() {
  if (remitCountryInfo) {
    navigateToCountrySelect({}, NavigateType.navigateTo,  {
      /** 接受页面的事件 */
      [EmitEvent.changeCurrency]: (countryCode: string) => {
        updateCountryCode(countryCode);
      },
    });
    biClick('remit_container.sendingregion_selection', { country: remitCountryInfo.value?.key ?? '' });
  }
}
/**
 * 点击开始汇款按钮，带上汇款国家、币种、金额信息跳转到机构列表页
 */
function onClickRemitButton() {
  if (!isRemitButtonEnable.value) {
    return;
  }
  let sellAmountParam = `${sellAmount.value}`;
  // 如果用户没有输入值则尝试取汇出金额默认值
  if (!sellAmount.value && isValidatedInt(sellPlaceholder.value)) {
    sellAmountParam = sellPlaceholder.value.replaceAll(',', '');
  }
  uniGpcRemitRoute.navigateToInstitution({
    countryCode: remitCountryInfo?.value?.key,
    hideHotArea: HideHotArea.HIDE,
    sellAmount: sellAmountParam,
  });
  biClick('remit_container.remit_button', {
    sellAmount: sellAmount.value,
    sellCurrency: remitCountryInfo.value?.currencyCode,
    sellCountry: remitCountryInfo.value?.key,
    buyAmount: buyAmount.value,
    buyCurrency: 'CNY',
    buyCountry: 'CHN',
  });
}
/**
 * 汇出金额输入框获得焦点时进行上报
 */
function onSellAmountInputFocusFunc() {
  biClick('remit_container.remit_input_out', {
    country: remitCountryInfo?.value?.key,
  });
  onSellAmountInputFocus();
}
/**
 * 收款金额输入框获得焦点时进行上报
 */
function onBuyAmountInputFocusFunc() {
  biClick('remit_container.remit_input_receive', {
    country: remitCountryInfo?.value?.key,
  });
  onBuyAmountInputFocus();
}

/**
 * 自定义分享图片
 * 注意 onShareAppMessage 只有在页面级别才会生效，组件中不生效
 */
onShareAppMessage((res) => {
  if (res.from === 'button') {
    return getShareData(res);
  }
  // 这里拆分页面后，分享的数据 hardcode 为收款相关
  return getShareData(res, {
    title: '邀请你使用微汇款',
    path: createRemitHomeUrl({
      entryChannel: 'all_all_share_remitterpage',
      entryScene: 'gpcHomeShare',
    }),
    imageUrl: 'https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/common/images/fastreceipt-share/share3.png',
  });
});
</script>

<style scoped lang="scss">
.page {
  background-color: #F5F5F5;
}

.panel-card {
  border-radius: 16rpx;
  background-color: #fff;
  position: relative;
  width: 686rpx;
  margin-top: 0;
  margin-bottom: 24rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

.clear-bg{
  background:rgba(0,0,0,0)
}

</style>
