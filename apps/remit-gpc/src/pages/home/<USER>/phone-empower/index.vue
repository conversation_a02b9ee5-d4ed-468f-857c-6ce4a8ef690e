<!--
 * @Date: 2023-03-13 11:29:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-04-08 17:36:10
 * @FilePath: /ppd-uniapp/apps/tuition/src/pages/home/<USER>
 * @Description:
-->
<template>
  <view
    v-stat.brow="'phone_empower.container'"
    class="empower"
  >
    <p-popup
      :show="isShow"
      mode="bottom"
      round="16"
      :custom-style="{
        'max-height': '100vh'
      }"
      :overlay-style="{
        background: 'transparent'
      }"
    >
      <view
        class="h-100vh"
        style="height: calc(100vh - env(safe-area-inset-bottom))"
      >
        <view class="pay-warning flex flex-col items-center justify-center pl-63rpx pr-63rpx h-full">
          <view class="flex-1 flex flex-col items-center">
            <view class="text-40 lh-56 Semibold mt-200rpx">
              手机号码授权
            </view>
            <text class="text5-reg color-text-primary text-center mt-40rpx">
              授权微汇款业务获取微信绑定的手机号码，用于创建收款名片
            </text>
          </view>
          <view class="flex items-center justify-center mb-68rpx flex-col">
            <view v-stat.click="'phone_empower.empower_button'">
              <auth-phone
                type="primary"
                class="text4-sem"
                :custom-style="{width: '432rpx', height: '88rpx'}"
                @phone-number-suc="handleGetPhoneNumberSuc"
              >
                <text class="text4-sem">
                  授权
                </text>
              </auth-phone>
            </view>
            <text
              v-if="isBackBtnVisible"
              v-stat.click="'phone_empower.back_button'"
              class="text5-sem color-primary-normal p-22rpx mt-10rpx"
              @click="back"
            >
              返回上一页
            </text>
          </view>
        </view>
      </view>
    </p-popup>
  </view>
</template>

<script setup lang="ts">
import AuthPhone from '@tencent/ppd-uni-component/packages/auth-phone/index.vue';

const emit = defineEmits<{
  (e: 'back', v: boolean): void,
  (e: 'getPhoneNumber', data: WechatMiniprogram.CustomEvent): void
}>();
withDefaults(defineProps<{
  isShow: boolean,
  isBackBtnVisible?: boolean,
}>(), {
  isBackBtnVisible: false,
});

function back() {
  emit('back', false);
}

const handleGetPhoneNumberSuc = (data) => {
  emit('getPhoneNumber', { detail: { iv: data.iv, encryptedData: data.encryptedData } } as unknown as WechatMiniprogram.CustomEvent<{
    iv: string;
    encryptedData: string;
  }>);
};
</script>

<style scoped lang="scss">
.text5-reg {
  font-style: normal;
  font-weight: 400;
  font-size: 34rpx;
  line-height: 48rpx;
}
</style>
