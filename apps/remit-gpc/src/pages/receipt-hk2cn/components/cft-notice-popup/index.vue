<template>
  <view>
    <p-popup
      :show="isShowPopup"
      mode="bottom"
      round="30"
      closeable
      :close-on-click-overlay="true"
      @close="close"
    >
      <!-- 标题 -->
      <view class="w-full pt-48rpx pb-32rpx px-47rpx">
        <view class="title4-sem">
          微信支持直接收款到零钱啦
        </view>
        <view class="title4-sem">
          多种收款方式更便捷
        </view>
      </view>
      <!-- 图片 -->
      <view class="mt-56rpx mb-88rpx flex flex-col items-center">
        <image
          class="h-302rpx w-382rpx mb-104rpx"
          src="https://7765-we-remit-6opkr-1301335959.tcb.qcloud.la/package-gpc-uni/pages/receipt-hk2cn/components/loose_change.png"
        />
        <p-button
          class="inline-block"
          type="primary"
          size="large"
          @click="changeRemitMode"
        >
          更改收款方式
        </p-button>
      </view>
    </p-popup>
  </view>
</template>

<script setup lang="ts">
import { biBrow, biClick } from '@tencent/fit-bi-sdk';

const props = withDefaults(defineProps<{
  isShowPopup: boolean, // 是否显示弹层
}>(), {
  isShowPopup: false,
});

const emit = defineEmits<(e: 'close' | 'changeRemitMode') => void>();

// 关闭弹层
const close = () => {
  emit('close');
  biClick('cash_remind_popup.close_button');
};

// 更改收款方式
const changeRemitMode = () => {
  emit('changeRemitMode');
  biClick('cash_remind_popup.cardchange_button');
};

watch(() => props.isShowPopup, (newval: boolean) => {
  if (newval) {
    biBrow('cash_remind_popup.popup');
  }
});
</script>

<style lang="scss">
</style>
