/**
 * 日志上报adapter
 */

import { CustomException, reportInstance } from 'mainPack/package-main-uni/adapters/report';

export enum Hk2cnTimePoint {
  /** 进入页面 */
  onInit = 'onInit',
  /** 完成订单校验 */
  checkSyncComplete = 'checkSyncComplete',
  /** 校验信息失败，展示错误页面 */
  showFailPage = 'showFailPage',
  /** 命中开户 */
  showOpenCard = 'showOpenCard',
  /** 开始轮询查单 */
  startPolling = 'startPolling',
  /** 轮询查单完成 */
  pollingComplete = 'pollingComplete',
  /** 查单完成，准备进入处理中页面 */
  navigateToResultPage = 'navigateToResultPage',
  /** 进入处理中页面 */
  resultPageOnload = 'resultPageOnload',
  /** 处理中页面展示 */
  resultPageShow = 'resultPageShow',
  /** 查单完成，去签协议 */
  navigateToSignContract = 'navigateToSignContract',
  /** 协议页面展示 */
  signContractPageShow = 'signContractPageShow',
  /** 查单完成，进入订单详情页 */
  navigateToDetailPage = 'navigateToDetailPage',
  /** 订单详情Onload展示 */
  detailPageOnload = 'detailPageOnload',
  /** 订单详情展示 */
  detailPageShow = 'detailPageShow',
}

export declare type TObject = {
  [index in string]: string | number;
};

/**
 * 上报测速
 * @param key 标识
 * @param startTime 开始时间
 */
export function reportSpeed(key: string, startTime?: string | number) {
  const time = +(startTime ?? 0);
  if (time > 0) {
    const duration = +new Date() - time;
    reportInstance.reportCustomSpeed({
      key,
      duration,
      ext1: JSON.stringify({ startTime, now: +new Date() }),
    });
    return;
  }
};

export {
  CustomException,
};

export default reportInstance;
