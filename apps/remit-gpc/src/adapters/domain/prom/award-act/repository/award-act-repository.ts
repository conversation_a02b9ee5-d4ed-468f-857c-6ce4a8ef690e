import { Service } from 'typedi';
import {
  GpcCommonDomainToken,
  AwardActRepositoryInterface,
  QryUserActStateParams,
  UserActStatus,
} from '@tencent/ppd-gpc-common';
import { to } from '@tencent/ppd-common/src/utils';
import { CustomException } from '@/adapters/report';
import { qryUserActState } from '@/data-source/prom/award-act';
import { MemoryCache } from '@tencent/ppd-uni-domain';

@Service(GpcCommonDomainToken.AWARD_ACT_REPOSITORY_TOKEN)
export class AwardActRepository implements AwardActRepositoryInterface  {
  /**
   * 查询用户活动状态
  */
  @MemoryCache({
    cachePendingOnly: true,
  })
  async getUserActStatus(param: QryUserActStateParams): Promise<UserActStatus> {
    const [requestErr, response] = await to(qryUserActState(param));
    if (requestErr) {
      new CustomException(requestErr, 'qryUserActState_error', '查询用户活动状态失败');
      throw (requestErr);
    }
    return response.state;
  }
}
