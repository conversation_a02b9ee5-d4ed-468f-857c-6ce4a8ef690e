import { MaybeComputedRef } from '@vueuse/core';
import { useStorage as useStorageCommon, UseStorageOptions } from '@tencent/ppd-uni-common/src/hooks/useStorage';
import { useStorageInstance } from './storage';
import { UseStorageKeys } from './storage-keys';


/**
 * 响应式storage
 * @param key localStorage的key
 * @param defaults 默认值，当取不到值时使用
 * @param ttl_ms 超时时间，如果为undefined则永久有效
 * @param options useStorage的参数
 * @returns 响应式的storage值
 */
export const useStorage = <T>(
  key: UseStorageKeys,
  defaults: MaybeComputedRef<T>,
  ttl_ms?: number,
  options?: UseStorageOptions<T>,
) => useStorageCommon(
    key,
    defaults,
    {
      getItem(key: UseStorageKeys) {
        return useStorageInstance.getItem(key);
      },
      setItem(key: UseStorageKeys, value: string) {
        useStorageInstance.setItem(key, value, ttl_ms);
      },
      removeItem(key: UseStorageKeys) {
        useStorageInstance.removeItem(key);
      },
    },
    options,
  );
