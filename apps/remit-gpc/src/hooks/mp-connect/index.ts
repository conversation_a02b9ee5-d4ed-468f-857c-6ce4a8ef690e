
/**
 * 小程序链接hooks
*/
import { CustomException } from '@/adapters/report';
import { CardService } from '@/domain/user/cardService';
import { useUserStore } from '@/store/modules/user';
import { onLoad } from '@dcloudio/uni-app';
import * as crypto from '@tencent/ppd-uni-common/src/crypto';

type Sm4Crypto = crypto.Sm4Crypto;

// 来源小程序appid
const enum SourceMpAppId{
  OFFLINE_REMIT = 'wx8a446e16cbf843ca'
}

/**
 * 回传数据应用场景枚举
*/
const enum ConnectScene{
  /**
   * 微汇款联动开户场景
  */
  WEREMIT_CONNECT_RECEIVE = 'WEREMIT_CONNECT_RECEIVE',
}

/**
 * 来源小程序拉起微汇款时传入的extraData
*/
interface SourceMpExtraData{
  sm4Iv?: string;
  sm4Key?: string;
  connectScene: ConnectScene;
}

/**
 * 微汇款前端回传数据
*/
export interface WeRemitReturnExtraData{
  openid: string;  // 微汇款openid
  connectScene: ConnectScene;
}

/**
 * 微汇款写入剪贴板数据结构
*/
export interface WeRemitSetClipboardData{
  name_cn?: string; // 中文名
  name_en?: string; // 英文名（合并姓和名）
  given_name_en?: string,
  family_name_en?: string,
  phone_with_code?: string; // 带区号的手机号
  account_type?: string; // 卡类型
  bank_name?: string;  // 银行名称
  bank_account?: string; // 银行账号
}


export const mpConnectHooks = () => {
  let sourceMpAppId: string;

  let sourceMpExtraData: SourceMpExtraData;

  let sm4CryptoInstance: Sm4Crypto;

  /**
   * 获取小程序联动参数
  */
  const initMpConnectInfoOnAppShow = (onAppShowOptions: Record<string, any>) => {
    console.log('【线下汇款】initMpConnectInfo', onAppShowOptions);
    sourceMpAppId = onAppShowOptions?.referrerInfo?.appId as SourceMpAppId;
    sourceMpExtraData = onAppShowOptions?.referrerInfo?.extraData as SourceMpExtraData;
    if (sourceMpExtraData.sm4Iv && sourceMpExtraData.sm4Key) {
      sm4CryptoInstance = new crypto.Sm4Crypto({
        sm4Key: sourceMpExtraData.sm4Key,
        sm4Iv: sourceMpExtraData.sm4Iv,
      });
    }
  };

  onLoad(() => {
    // 获取小程序启动参数
    initMpConnectInfoOnAppShow(wx.getEnterOptionsSync());
  });

  /**
   * 获取跳回上一个小程序的extraData
   * 注意：ExtraData是通过小程序纯前端传输的，可配合wx.navigateBackMiniProgram API使用
  */
  const getNavigateToMiniProgramExtraData = async () => {
    console.log(
      '【线下汇款】getNavigateToMiniProgramExtraData',
      sourceMpAppId === SourceMpAppId.OFFLINE_REMIT,
      sourceMpExtraData.connectScene === ConnectScene.WEREMIT_CONNECT_RECEIVE,
    );
    // 如果命中线下汇款自汇自收场景
    if (
      sourceMpAppId === SourceMpAppId.OFFLINE_REMIT
      && sourceMpExtraData.connectScene === ConnectScene.WEREMIT_CONNECT_RECEIVE
    ) {
      const userStore = useUserStore();
      const openid = await userStore.getOpenid() as string;
      const extraData: WeRemitReturnExtraData = {
        openid,
        connectScene: ConnectScene.WEREMIT_CONNECT_RECEIVE,
      };
      return extraData;
    }
  };

  /**
   * 设置返回数据到剪切板
  */
  const setReturnDataIntoClipboard = async () => {
    console.log(
      '【线下汇款】setReturnDataIntoClipboard',
      sourceMpAppId === SourceMpAppId.OFFLINE_REMIT,
      sourceMpExtraData.connectScene === ConnectScene.WEREMIT_CONNECT_RECEIVE,
    );
    // 如果命中线下汇款自汇自收场景
    if (
      sourceMpAppId === SourceMpAppId.OFFLINE_REMIT
      && sourceMpExtraData.connectScene === ConnectScene.WEREMIT_CONNECT_RECEIVE
    ) {
      const { cardInfo } = await CardService.getCardInfo();
      const data: WeRemitSetClipboardData = {
        name_cn: cardInfo?.cardData.name,
        name_en: cardInfo?.cardData.name_en,
        given_name_en: cardInfo?.cardData.given_name_en,
        family_name_en: cardInfo?.cardData.family_name_en,
        phone_with_code: cardInfo?.cardData.auth_phone,
        account_type: cardInfo?.currRecvAccount?.account_type,
        bank_name: cardInfo?.currRecvAccount?.bank_name,
        bank_account: cardInfo?.currRecvAccount?.bank_account,
      };
      let dataToSetClipboard = JSON.stringify(data);

      console.log('【线下汇款】setReturnDataIntoClipboard dataToSetClipboard: ', dataToSetClipboard);
      try {
        if (sm4CryptoInstance) {
          dataToSetClipboard = sm4CryptoInstance.sm4Encode(dataToSetClipboard);
        }
      } catch (e) {
        new CustomException(e, 'MpConnectSetClipboardDataError', 'setReturnDataIntoClipboard sm4Encode error');
      }

      console.log('【线下汇款】setReturnDataIntoClipboard sm4Encode dataToSetClipboard: ', dataToSetClipboard);

      dataToSetClipboard = `感谢你使用跨境汇款门店汇款，你的收款人信息已安全加密复制到你的剪贴板:${dataToSetClipboard}`;

      uni.setClipboardData({
        data: dataToSetClipboard,
        fail: (err) => {
          new CustomException(err, 'MpConnectSetClipboardDataError', 'setReturnDataIntoClipboard setClipboardData error');
        },
      });
    }
  };

  return {
    getNavigateToMiniProgramExtraData,
    setReturnDataIntoClipboard,
  };
};
