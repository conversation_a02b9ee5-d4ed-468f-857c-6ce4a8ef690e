/**
 * 【核心】收集信息核心方法
 * 将prepare告知要采集的信息映射为采集职责链
*/
import type { UserLinkController as UserLinkControllerType } from '../userLinkController';
// import { Chain } from '@/adapters/design-mode/chain';
// import { TaskType } from '../task/index';
import { H2cShareInfo, ToAddFormEleEnum, ToAddOtherEleEnum, ToAuthEle } from '@/data-source/user-link/base';
import { CustomException } from '@/adapters/report';
import { MatchCode, UserState } from '@/data-source/user-link';
import { getTask, TaskInstance } from '../task/task-config';
import { UserLinkScene } from '@tencent/weremit-common';
import { PrepareForUserErrCode } from '@/domain/user-link';


export function getCollectInfoTaskList(UserLinkController: UserLinkControllerType) {
  const { prepareForUserEntity, userLinkProcessDataEntity } = UserLinkController;
  // const currentTask = ref<TaskInstance<TaskType>|null>(null);
  if (!prepareForUserEntity) {
    new CustomException(null, 'userLink_dealWithCollectInfo_missData', '账号关联采集要素缺失必要信息');
    return;
  }

  const taskList: TaskInstance<any>[] = [];

  let hasCollectPhoneTask = false;

  (() => {
    /**
     * 展示订单信息任务，条件：
     * 1、互联入口进入
     * 2、第一次循环
    */
    if (
      UserLinkController.userLinkProcessDataEntity?.userLinkScene === UserLinkScene.HULIAN
      && UserLinkController.userLinkProcessDataEntity?.loopTimes === 1
    ) {
      const doesNeedAuthPhone = prepareForUserEntity.matchCode === MatchCode.NAME_MATCH_PHONE_NOT_MATCH
      && prepareForUserEntity.userInfo?.user_state === UserState.OPENED;

      const showHulianOrderTask = getTask(UserLinkController, 'SHOW_HULIAN_ORDER',  {
        prepareForUserEntity,
        doesNeedAuthPhone,
      });
      taskList.push(showHulianOrderTask);
    }
    if (
      UserLinkController.userLinkProcessDataEntity?.userLinkScene === UserLinkScene.HK2CN
      && UserLinkController.userLinkProcessDataEntity?.loopTimes === 1
    ) {
      const showHK2CNOrderTask = getTask(UserLinkController, 'SHOW_HK2CN_ORDER',  {
        h2cShareInfo: prepareForUserEntity.h2cShareInfo as H2cShareInfo,
      });
      taskList.push(showHK2CNOrderTask);
    }

    /**
     * 判断是否要采集协议
    */
    if (prepareForUserEntity.doesUerNeedSignContract) {
      // 初始化采集协议任务
      const collectionProtocolTask = getTask(UserLinkController, 'COLLECT_PROTOCOL', {
        protocolList: prepareForUserEntity.toSignContract,
      });
      taskList.push(collectionProtocolTask);
    }

    /**
     * 手机号解密失败重新采集手机号任务
    */
    if (prepareForUserEntity.prepareForUserError?.retcode === PrepareForUserErrCode.ERR_DECRYPT_AUTH_PHONE_FAIL) {
      hasCollectPhoneTask = true;
      const collectMatchOrderPhoneTask = getTask(UserLinkController, 'RECOLLECT_PHONE', {
      });
      taskList.push(collectMatchOrderPhoneTask);
      // 采集手机号是阻断式的，要先提交一波数据由后台匹配
      return;
    }

    /**
     * 手机号匹配不通过采集手机号任务
    */
    if (prepareForUserEntity.matchCode === MatchCode.NAME_MATCH_PHONE_NOT_MATCH) {
      hasCollectPhoneTask = true;
      userLinkProcessDataEntity?.addMatchPhoneErrorTimes();
      const collectMatchOrderPhoneTask = getTask(UserLinkController, 'COLLECT_MATCH_ORDER_PHONE', {
        phoneTail: prepareForUserEntity.holdListInfo?.recv_phone_tail,
        matchErrorTimes: userLinkProcessDataEntity?.matchPhoneErrorTimes,
      });
      taskList.push(collectMatchOrderPhoneTask);
      // 采集手机号是阻断式的，要先提交一波数据由后台匹配
      return;
    }

    /**
     * 判断是否要采集基础支付授权信息
    */
    if (prepareForUserEntity.toAuthEleArray.includes(ToAuthEle.TO_AUTH_KYC)) {
      // 初始化采集基础支付授权信息任务
      const collectionAuthKycTask = getTask(UserLinkController, 'COLLECT_AUTH_INFO', undefined);
      taskList.push(collectionAuthKycTask);

      /**
       * 采集kyc是阻断式提交
       * 采集到后先提交一波
      */
      return;
    }

    /**
     * 判断是否要采集收款方式
    */
    if (
      prepareForUserEntity.addEleArray.includes(ToAddOtherEleEnum.TO_ADD_RECV_METHOD)
  && !prepareForUserEntity.toAuthEleArray.includes(ToAuthEle.TO_AUTH_KYC)
    ) {
      const collectRecvMethodTask = getTask(UserLinkController, 'COLLECT_RECV_METHOD', undefined);
      taskList.push(collectRecvMethodTask);
    }

    /**
     * 采集用户基础信息手机号
    */
    if (
      prepareForUserEntity.addEleArray.includes(ToAddOtherEleEnum.TO_ADD_PHONE)
      && !hasCollectPhoneTask
    ) {
      // 初始化采集手机号任务
      const collectionPhoneTask = getTask(UserLinkController, 'COLLECT_USER_BASE_INFO_PHONE', undefined);
      taskList.push(collectionPhoneTask);
    }

    /**
     * 判断是否要补充表单类型要素信息
     * 表单类型要素信息是指如下列表这种，在九要素表单页进行采集的信息
    */
    if (
      (prepareForUserEntity.addEleArray.some(item => Object.values(ToAddFormEleEnum)
        .includes(item as ToAddFormEleEnum)))
    && !prepareForUserEntity.toAuthEleArray.includes(ToAuthEle.TO_AUTH_KYC) // 用户已授权authInfo
    ) {
      const collectFormEleInfoTask = getTask(
        UserLinkController,
        'COLLECT_FORM_ELE_INFO',
        {
          toAddEleArray: prepareForUserEntity.addEleArray,
          retryError: prepareForUserEntity.prepareForUserError,
          creType: prepareForUserEntity.userInfo?.cre_type,
        },
      );
      taskList.push(collectFormEleInfoTask);
    }
  })();

  /**
   * 注：
   * 执行完这个阶段代码，职责链中就注入了相应的任务
   * 执行到职责链对应节点时，将「激活」任务
  */

  return taskList;
};
