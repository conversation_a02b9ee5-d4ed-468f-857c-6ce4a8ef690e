import { CommonResponse, request } from '@/adapters/request';
import { BindCardReq, CreType } from '@/data-source/user/card';
import { ContractType } from '@/domain/user';
export enum ElementType {
  CollectInfo = 1, // 九要素信息
  SignContract = 2, // 签署协议
}

// 地址信息
export interface AddressInfo {
  address_code: string;
  address_region: string;
  address_detail: string;
}

export interface JobInfo {
  job_code: string;
}


export interface QryContractRes{
  to_sign_contract_array: ContractType[]; // 用户未签署的协议，若无未签署的协议，返回空数组
  cre_type: CreType; // 证件类型
  branch_type_array?: string[]// 需签署协议渠道数组，TODO：九要素后端全量后，此字段去掉，前端可下一版本删除
  has_order?: '0' | '1';// 是否有单 //0-无单 1-有单
  /**
   * 待补充的九要素，0表示无需收集。用bit位标识，某位为1则需要收集
     第0位：TO_ADD_ADDRESS
     第1位：TO_ADD_BIRTH
     第2位：TO_ADD_SEX
     第3位：TO_ADD_CRE_BEGIN_DATE
     第4位：TO_ADD_CRE_END_DATE
     第5位：TO_ADD_JOB
     第6位：TO_ADD_PHONE
     第7位：TO_ADD_KYC_AUTH
     第8位：TO_ADD_WX_OCR 回乡证影印件
   */
  to_add_ele?: number; // 待补充的九要素，0表示无需收集。用bit位标识，某位为1则需要收集
  /**
   * 具体所需收集的信息, 0表示无需收集。用bit位标识，某位为1则需要收集
   * 第0位：TO_ADD_RELATIONSHIP 需补充收汇关系
   */
  to_add_pair_ele?: number;
}

export type SignContractReq = {
  element_type: ElementType;
  branch_type?: string; // 签署的渠道协议，4638 - 稠州1.0，4639 - 广工1.0，TODO：九要素后端全量后，此字段去掉，前端可下一版本删除
  to_sign_contract_array?: ContractType[]; // 签署的协议类型，签协议时必传，补充九要素不传
  listid?: string; // 小鹅订单号，跟单时传
} & Omit<BindCardReq, 'session_id'>;

export type SignContractRes = {
  /**
   * 用户更换信息标记，bit位标记
   * 第0位 - 仅更换银行卡
   * 第1位 - 仅需更新订单姓名
   * 第2位 - 需更新订单姓名+默认姓名
   * 第3位 - 用户表姓名与实名不一致。需要展示提示
   */
  update_default: number;
} & CommonResponse;

// 查询协议签署状态，若传小鹅订单号，则查询此单的待补充信息
export function qryContract(listid?: string): Promise<QryContractRes> {
  return request({
    url: '/remit_gpc/qry_contract.fcgi',
    data: listid ? { listid } : {},
  });
}

// 查询已签署过的渠道协议
export function qrySignedContract(): Promise<QrySignedContractRes> {
  return request({
    url: '/remit_gpc/qry_signed_contract.fcgi',
  });
}

// 查询已签署过的渠道协议
export function signContract(params: SignContractReq): Promise<SignContractRes> {
  return request({
    url: '/remit_gpc/sign_contract.fcgi',
    data: params,
    config: {
      header: {
        'content-type': 'application/json',
      },
    },
  });
}

export interface QrySignedContractRes{
  branch_type_array?: ContractType[]; // 已签署过的协议列表，如: ['4638']
}
