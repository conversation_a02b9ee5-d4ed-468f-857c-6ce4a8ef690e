import { CardInfoData } from '@/domain/user/type';
export enum TemplateType {
  Receipt,
  Remit,
}
export interface TemplateConfig {
  fields: {
    key: string,
    label: string
  }[],
  headerText: string,
  footerText: string
}
/**
 * 收款人视角复制信息模板
 */
const receiveTemplate: TemplateConfig = {
  fields: [{
    key: 'auth_phone',
    label: '· Phone number (微信绑定手机号)',
  }, {
    key: 'last_name',
    label: '· Last name (姓)',
  }, {
    key: 'first_name',
    label: '· First name (名)',
  }, {
    key: 'name_en',
    label: '· Full name (姓名)',
  }],
  headerText: `* 我的微信收款账号是什么？
What is my recipient account in Weixin？`,
  footerText: `
* 如何汇款到我的微信？
How to remit money to my Weixin？
  前往微汇款官方合作汇款机构发起汇款
  https://weremit.tenpay.com/c/rZMRbnb-NB33vxTw3N-ARw1`,
};


/**
 * 汇款人复制信息模板
 */
const remitTemplate: TemplateConfig = {
  fields: [{
    key: 'auth_phone',
    label: '· Phone number (微信绑定手机号)',
  }, {
    key: 'last_name',
    label: '· Last name (姓)',
  }, {
    key: 'first_name',
    label: '· First name (名)',
  }, {
    key: 'name_en',
    label: '· Full name (姓名)',
  }],
  headerText: `* 收款人的收款账号是什么？
What information should I fill in when sending a remittance?`,
  footerText: `
* 如何汇款到微信？
How to remit money to Weixin？
  前往微汇款官方合作汇款机构发起汇款
  https://weremit.tenpay.com/c/rZMRbnb-NB33vxTw3N-ARw1`,
};


export function getCopyData(cardInfo: CardInfoData, type: TemplateType = TemplateType.Remit) {
  let fieldText = '';
  const template = type === TemplateType.Remit ? remitTemplate : receiveTemplate;

  template.fields.forEach((item) => {
    if (cardInfo[item.key]) {
      fieldText = `${fieldText}
${item.label}
  ${cardInfo[item.key]}`;
    }
  });
  return `${template.headerText}${fieldText}
${template.footerText}`;
}
