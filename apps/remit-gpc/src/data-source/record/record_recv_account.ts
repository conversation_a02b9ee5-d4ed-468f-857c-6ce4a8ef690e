/**
 * 维护订单流程中与收款方式相关的方法
*/

import { request } from '@/adapters/request';

export interface RevAccountCheckResponse{
  support_bank_names: string; // 格式为竖线分隔：CFT|DEBIT|
}

export interface UserInfoNew {
  user_name_new?: string;
}

/**
 * 订单内换卡入参
*/
export interface UpdateRecordCardReq {
  /**
   * 订单号
  */
  listid: string;

  /**
   * 选卡sessionId
  */
  session_id: string;

  /**
   * 用户授权标记
   * bit位
   * 第0位 - 同意更换姓名
  */
  has_auth_flag?: number;
}

/**
 * 订单内换卡返回值
*/
export interface UpdateRecordCardRes {
  /**
   * 用户更换信息标记，bit位标记
   * 第0位 - 更新默认卡
   * 第1位 - 仅需更新订单姓名
   * 第2位 - 需更新订单姓名+默认姓名
   */
  update_default: number;
  user_info_new?: UserInfoNew;
}

/**
 * 订单内换卡
 */
export const updateRecordCard = (params: UpdateRecordCardReq): Promise<UpdateRecordCardRes> => request({
  url: '/remit_gpc/update_list_card.fcgi',
  data: params,
});

/**
 * 订单内换卡更换默认卡
 * 背景：返回update_default为1，即用户订单中的收款方式和用户收款名片中收款方式不同，将出现弹窗告知用户是否更换默认收款账户
 */
export const updateDefaultCard = (listid: string): Promise<void> => request({
  url: '/remit_gpc/update_user_default_card.fcgi',
  data: { listid },
});

/**
 * 查询支持的账号类型
*/
export const revAccountCheck = (listid: string):
Promise<RevAccountCheckResponse> => request({
  url: '/remit_gpc/recv_account_check.fcgi',
  data: {
    listid,
  },
});
