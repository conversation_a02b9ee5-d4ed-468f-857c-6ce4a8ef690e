/**
 * TODO 和sync、Check比对下
*/

/**
 * 非九要素表单收集的要素信息枚举
*/
export enum ToAddOtherEleEnum{
  /**
   * 手机号
  */
  TO_ADD_PHONE = 'TO_ADD_PHONE', // 手机号
  /**
   * 影印件
  */
  TO_ADD_PHOTO = 'TO_ADD_PHOTO', // 影印件

  /**
   * 回乡证影印件
   * TODO 确认下 TO_ADD_WX_OCR和TO_ADD_PHOTO区别
  */
  TO_ADD_WX_OCR = 'TO_ADD_WX_OCR',

  /**
   * 收款方式
  */
  TO_ADD_RECV_METHOD = 'TO_ADD_RECV_METHOD', // 收款方式
}
/**
 * 通过九要素表单手机的要素信息枚举
 */
export enum ToAddFormEleEnum{
  /**
   * 地址
  */
  TO_ADD_ADDRESS = 'TO_ADD_ADDRESS',
  /**
   * 生日
  */
  TO_ADD_BIRTH = 'TO_ADD_BIRTH', // 生日
  /**
   * 性别
  */
  TO_ADD_SEX = 'TO_ADD_SEX', // 性别
  /**
   * 证件有效期开始时间
  */
  TO_ADD_CRE_BEGIN_DATE = 'TO_ADD_CRE_BEGIN_DATE',  // 证件有效期开始时间
  /**
   * 证件有效期结束时间
  */
  TO_ADD_CRE_END_DATE = 'TO_ADD_CRE_END_DATE',  // 证件有效期结束时间
  /**
   * 职业
  */
  TO_ADD_JOB = 'TO_ADD_JOB', // 职业
}

/**
 * 所需收集的实名信息枚举
 */
export type ToAddEleInfoEnum = ToAddFormEleEnum | ToAddOtherEleEnum;

/**
 * 补充九要素枚举数组
 * 此顺序严格对应后台接口文档顺序，可用于解析命中补充哪些要素
*/
export const toAddEleInfoArray = [
  ToAddFormEleEnum.TO_ADD_ADDRESS, // 地址
  ToAddFormEleEnum.TO_ADD_BIRTH, // 生日
  ToAddFormEleEnum.TO_ADD_SEX, // 性别
  ToAddFormEleEnum.TO_ADD_CRE_BEGIN_DATE,  // 证件有效期开始时间
  ToAddFormEleEnum.TO_ADD_CRE_END_DATE,  // 证件有效期结束时间
  ToAddFormEleEnum.TO_ADD_JOB, // 职业

  /**
   * TODO 优化项
   * 把九要素类型表单页的枚举和非九要素的拆分开
  */
  ToAddOtherEleEnum.TO_ADD_PHONE, // 手机号
  ToAddOtherEleEnum.TO_ADD_PHOTO, // 影印件
  ToAddOtherEleEnum.TO_ADD_WX_OCR, // 回乡证影印件
  ToAddOtherEleEnum.TO_ADD_RECV_METHOD, // 收款方式
];

/**
 * 所需授予的权限
*/
export enum ToAuthEle{
  /**
   * 需要授权获取基础支付KYC
  */
  TO_AUTH_KYC = 'TO_AUTH_KYC'
}

/**
 * 所需授予的权限数组
 * 此顺序严格对应后台接口文档顺序
*/
export const toAuthEleArray = [
  ToAuthEle.TO_AUTH_KYC,
];

export interface ToAddEleInfo {
  /**
   * 具体所需收集的信息, 0表示无需收集。用bit位标识，某位为1则需要收集
   * 字段比特位顺序见toAddEleInfoArray数组
  */
  to_add_ele?: number

  /**
   * 具体所需授予的权限, 0表示无需授予。用bit位标识，某位为1则需要收集
   * 第0位：TO_AUTH_KYC，表示需要授权获取基础支付KYC
  */
  to_auth_ele?: number

  /**
   * 具体所需签署的信息，0表示无需签署。用bit位标识，某位为1则需要收集
   * 第0位：TO_SIGN_GUANGGONG，表示需要签署广工
   * 第1位：TO_SIGN_CHOUZHOU，表示需要签署稠州
   * 第2位：TO_SIGN_HENGFENG，表示需要签署恒丰
  */
  to_sign_contract?: {
    to_sign_contract: string[]
  }
}

// 订单状态开始 - 未来有单据完善领域后可以迁移
// #region

/**
 * 互联挂单状态
*/
export enum HoldListState {
  /**
   * 待关联
  */
  PENDING_ASSOCIATION = 10,

  /**
   * 关单中
  */
  CLOSING = 20,

  /**
   * 已关单（终态）----默认为超时关单
  */
  CLOSED = 30,

  /**
   * 已同步（终态）
  */
  SYNCHRONIZED = 40,
}

/**
 * 失败状态原因码
*/
export enum FailState{
  TIMEOUT_CLOSED = 1, // 超时关单,跳转到超时关单的错误页
  NAME_MISMATCH_CLOSED = 2, // 姓名不匹配关单, 跳转到"实名信息(李乐林)与收款人姓名不一致"的错误页
  INVALID_CLOSED = 3, // 校验年龄\地区等不合法关单,跳转到"不合法关单"的错误页
  CANCELLED_BY_SENDER = 4, // 汇款人取消订单的错误页
  ORG_BALANCE_NOT_ENOUGH_CANCEL_ORDER = 5, // 机构余额不足-取消订单
}

// 定义收款账户要求的常量
export enum RequestAccountType {
  NO_REQUIREMENT = 0, // 无要求
  ONLY_WALLET = 1,    // 仅零钱
}

/**
 * 互联挂单信息
*/
export interface HoldListInfo{
  listid: string; // 订单号，必填
  hold_list_state: HoldListState; // 待关联状态，必填
  fail_state?: FailState; // 失败状态原因码，结合hold_list_state为20或30使用：
  remitter_name: string; // 汇款人姓名明文，必填
  recv_phone_tail?: string; // 收款手机尾号，选填
  tran_ccy: string; // 入账币种，必填
  tran_amt: number; // 入账金额，必填
  tran_ccy_point_num: number; // 入账币种小数位，必填
  auth_phone?: string; // 授权手机号（明文），选填
  act_info?: Record<string, any>; // 活动信息，选填，使用json对象表示；历史字段，当前没用到
  rqr_account_type?: RequestAccountType; // 收款账户要求，选填
  org_id?: string; // 主机构号
  sub_org_id?: string;  // 子机构号（间联机构场景才返回）
  remitter_country_code?: string; // 汇款地区code
}

// 港陆登记簿状态
export enum H2cShareInfoState {
  REGISTERED = 1,    // 已登记
  SYNC_FAILED = 2,   // 同步失败

  /**
   * 校验通过
   * 注意：3在后台是瞬时态，正常情况下都是由3瞬时扭转到4
  */
  VERIFIED = 3,
  SYNC_SUCCESS = 4,  // 同步成功
}

/**
 * 港陆登记簿状态
*/
export interface H2cShareInfo{
  state?: H2cShareInfoState; // 状态，选填
  recv_bank_name?: string; // 收款人银行名称，选填
  recv_bank_account_tail?: string; // 银行尾号后四位，选置
  recv_amt?: string; // 汇款金额，选填
  remit_time: string; // 汇款支付时间，必填
  receiver_mask_name?: string; // 收款人姓名（掩码），选填
  remitter_name?: string; // 汇款人姓名，选填
  receiver_mask_cre_id?: string; // 收款人证件号，选填
  receiver_cre_type?: string; // 收款人证件类型，选填
}

// 证件类型
export enum CreType {
  ID_CARD = 1, // 身份证
  HK_MACAO_PASS_CARD = 5, // 港澳居民往来内地通行证（回乡证）
}

/**
 * 小鹅订单信息
 * TODO 后台接口文档定了后要check一遍
*/
export interface ListInfo {
  listid: string; // 小鹅订单号
  org_listid: string; // 汇款机构订单ID，必填
  remitter_name: string; // 汇出人姓名，必填
  receiver_name?: string; // 收款人姓名掩码，选填
  receiver_mask_cre_id?: string; // 收款人证件号掩码，选填
  org_name: string; // 汇款机构名，必填
  remit_country: string; // 汇款国家，必填
  bank_account: string; // 银行卡号，必填
  bank_name?: string; // 银行名，选填
  tran_ccy: string; // 入账币种，必填
  tran_amt: number; // 入账金额，必填
  tran_ccy_point_num: number; // 入账币种小数位，必填
  list_state: string; // 订单状态，必填
  fail_state?: string; // 失败状态，选填
  fail_reason?: string; // 失败原因，选置
  create_time?: string; // 汇款受理时间，选填
  fail_code?: number; // 订单收款失败错误码，选填
  branch_type?: string; // 渠道编号，选填
  /**
   * 是否需要签署或提交信息，bit位
   * 第0位 - 待签署协议
   * 第1位 - 待补充个人信息
   * 第2位 - 待补充关系对信息
   */
  need_sign_contract?: number;
  cre_type: CreType; // 证件类型，必填
  act_info?: Record<string, any>; // 活动信息，选填
  rqr_account_type?: RequestAccountType; // 收款账户要求，选填
  org_id?: string; // 一级机构号
  sub_org_id?: string; // 二级机构号
}

/**
 * 授权手机号原包信息
*/
export type AuthPhonePackInfo = {
  encrypted_data?: string
  iv?: string

  /**
   * 由业务加密的授权手机号
   * 背景：
   * 小程序授权iv和encrypted_data模式有有效期。但是在前端流程里手机号要反复传给后台
   *
   * 未避免过有效期，后台换到手机号明文后会生成一个加密手机号返回给前端
   * 前端在后续流程里再回传给后台
  */
  auth_phone_enc?: string

  /**
   * 由业务加密的手机号明文区号
   * 背景同auth_phone_enc
  */
  auth_phone_area_code?: string
};

// 协议签署信息
export interface ContractInfo{
  to_sign_contract: Array<String>
}

// TODO 这些公共的类型声明移到data-source/base里吧

// 地区信息
export interface AddressInfo {
  address_code: string;
  address_region: string;
  address_detail: string;
}

// 职业信息
export interface JobInfo {
  job_code: string;
}

export enum SEX {
  MALE = '1', // 男性
  FEMALE = '2', // 女性
}


// 要素信息
export interface ElementInfo {
  address_info?: AddressInfo;
  sex?: SEX; // 收款人性别，1-男；2-女 (回乡证用户可能四要素中无性别,需要采集)
  cre_begin_date?: string; // 证件生效日期：YYYY-MM-DD (回乡证用户可能四要素中可能无有效期,需要采集)
  cre_end_date?: string; // 证件失效日期：YYYY-MM-DD (回乡证用户可能四要素中可能无有效期,需要采集)
  birth?: string; // 出生年月日：YYYY-MM-DD
  job_info?: JobInfo; // 职业信息，稠州渠道收取的九要素
}

// #endregion
// 订单状态结束 - 未来有单据完善领域后可以迁移
