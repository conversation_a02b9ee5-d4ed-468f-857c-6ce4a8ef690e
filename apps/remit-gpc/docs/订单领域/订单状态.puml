@startuml
' [*] -> 等待收款
' [*] -> 补充材料
' [*] -> 审核
' [*] -> 银行处理
' [*] -> 收款成功

' skinparam linetype ortho


state 等待收款 {
  state 10 : 等待收款
  state 10000 : 初始态
  state 10050 : 待更换收款账户
}
state 补充材料 {
  state 20 : 审核中
  state 20000 : 待补充9要素或者协议
  state 20013 : 需更换卡开户（稠州开户失败）
  state 20020 : 待签署承诺函
  state NOT_SIGN_CONTRACT : 未签署渠道协议(need_sign_contract > 0)
  note left of NOT_SIGN_CONTRACT
  有没有可能need_sign_contract === B11
  end note
  
  state PROCESS : 处理中(need_sign_contract <= 0)
  
  
  20000 -down-> NOT_SIGN_CONTRACT
  20000 -down-> PROCESS
  20020 -down-> 材料状态
  

}

state 材料状态 {
  state NEED_ALL : 承诺函+材料(material_type === B11 不一定)
  state NEED_SIGN : 仅承诺函(material_type === B10)
  state NEED_FILES : 仅材料(material_type === B01)
}
state 审核 {
  state 30 : 审核成功
}
state 银行处理 {
  state 40 : 银行处理中
  state 40060 : 银行处理中-待汇款
  state 40070 : 银行处理中-汇款处理中
  state 40075 : 汇款机构处理中
  state 40080 : 银行处理中-更换银行卡
}
state 收款成功 {
  state 50 : 收款成功
  state 60 : 收款失败
}
state 未签署协议{
state isNeed9Ele : 待补充九要素信息(need_sign_contract === B10 || need_sign_contract === B11)
state isNeedSign : 未签署协议(need_sign_contract === B01)
}

json json10 {
  "type": "<color:#3089F0>waiting",
  "title": "发起收款"
}
json json10050 {
  "type": "<color:#FFA338>warn",
  "title": "请更换收款方式"
}
json json20 {
  "type": "<color:#3089F0>waiting",
  "title": "审核中"
}


json json20013 {
  "type": "<color:#FFA338>warn",
  "title": "请更换收款方式"
}
json jsonNEED_ALL {
  "type": "<color:#FFA338>warn",
  "title": "请提交材料"
}
json jsonNEED_SIGN {
  "type": "<color:#FFA338>warn",
  "title": "请确认亲属关系"
}
json jsonNEED_FILES {
  "type": "<color:#FFA338>warn",
  "title": "请提交证明材料"
}
json jsonisNeed9Ele {
  "type": "<color:#FFA338>warn",
  "title": "请完善个人信息"
}
json jsonisNeedSign {
  "type": "<color:#FFA338>warn",
  "title": "请签署协议"
}
json json30{
  "type": "<color:#3089F0>waiting",
  "title": "审核成功"
}
json json40{
  "type": "<color:#3089F0>waiting",
  "title": "银行处理中"
}
json json40075{
  "type": "<color:#3089F0>waiting",
  "title": "汇款机构处理中"
}
json json40080{
   "type": "<color:#FFA338>warn",
  "title": "请更换收款方式"
}
json json50{
   "type": "<color:#0CBD6A>success",
  "title": "收款成功"
}
json json60{
  "type": "<color:#F54545>fail",
  "title": "收款失败"
}
json jsonPROCESS{
  "type": "<color:#3089F0>waiting",
  "title": "处理中"
}


10 -down-> json10
10000 -down-> json10
10050 -down-> json10050
20 -down-> json20
20013 -down-> json20013
NEED_ALL -down-> jsonNEED_ALL
NEED_FILES -down-> jsonNEED_FILES
NEED_SIGN -down-> jsonNEED_SIGN
' NOT_SIGN_CONTRACT -down-> jsonNOT_SIGN_CONTRACT
30 -down-> json30
40 -down-> json40
40075 -down-> json40075
40080 -down-> json40080
40070 -down-> json40
40060 -down-> json40
50 -down-> json50
60 -down-> json60
PROCESS -down-> jsonPROCESS
NOT_SIGN_CONTRACT -down-> isNeed9Ele
NOT_SIGN_CONTRACT -down-> isNeedSign
isNeed9Ele -down-> jsonisNeed9Ele
isNeedSign -down-> jsonisNeedSign
@enduml
