@startuml

skinparam packageStyle rectangle

package userDomain-用户领域{

  class accountService{
    +{static}开户()
  }
  class cardService{
    +{static}修改多音字()
    +{static}绑卡()
    +{static}手机号授权()
  }
}

package ContractDomain-协议领域{
  class ContractService{
    -{static}cacheContractsEntity
    +{static}getSignedContracts(boolean force)
    +{static}clearContractsCache()
  }
  entity ContractEntity {
    signedContracts: string[]
    isSignedContract: boolean
  }
  ContractService::getSignedContracts --> ContractEntity

  note left of ContractService::cacheContractsEntity
    缓存的协议实体
  end note
  note left of ContractService::getSignedContracts
    获取已签约的协议实体，
    如果有缓存，可以直接从缓存获取
  end note
  note left of ContractService::clearContractsCache
    清空缓存实体
  end note
  
  note left of ContractEntity::signedContracts
    已签署协议列表
  end note
  note left of ContractEntity::isSignedContract
    getter：是否有签署了的协议
  end note
  

}

package ConfigDomain-配置领域{
  class ConfigService{
    -{static}caches
    +{static}getEntryConfig()
  }
  entity ConfigEntity {
    assistantList: AssistantItem[]
    fastReceiptList: AssistantItem[]
    westernUnionList: AssistantItem[]
    weCurrencyList: AssistantItem[]
    remitList: AssistantItem[]
  }
  interface AssistantItem {
    able: '0' | '1'
    desc: string
    title: string
    url: string
    reportKey: string
  }
  ConfigService::getSignedContracts --> ConfigEntity
  ConfigEntity::assistantList --> AssistantItem
  ConfigEntity::fastReceiptList --> AssistantItem
  ConfigEntity::westernUnionList --> AssistantItem
  ConfigEntity::weCurrencyList --> AssistantItem
  ConfigEntity::remitList --> AssistantItem

  note left of ConfigService::cache
    已缓存的配置列表
  end note
  note left of ConfigService::getEntryConfig
    获取入口的配置信息
  end note
  

  note left of ConfigEntity::assistantList
    所有收款助手列表
  end note
  note left of ConfigEntity::fastReceiptList
    小鹅快收收款助手列表
  end note
  note left of ConfigEntity::westernUnionList
    西联收款助手列表
  end note
  note left of ConfigEntity::weCurrencyList
    汇率工具助手列表，<color :red>后续需要移除</color>
  end note 
  note left of ConfigEntity::remitList
    汇款助手列表，<color :green>本次新增</color>
  end note
  note left of AssistantItem::reportKey
    <color :red>这个key用来作为判断不同类型的助手，含义比较模糊</color>
  end note 
}
@enduml