@startuml
left to right direction

package 原生小程序{
  [原生小程序主包]
  [原生小程序子包]
}
package uniapp{
  [uniapp主包]
  [uniapp子包]
}
package 图例{
  [调用方]
  [被调方]
}

原生小程序主包 -> 原生小程序子包
原生小程序主包 -> uniapp子包
原生小程序主包 <.[#red].> uniapp主包
原生小程序子包 -[#orange]-> 原生小程序主包
原生小程序子包 <.[#red].> uniapp主包
原生小程序子包 -> uniapp子包
原生小程序子包 -> 原生小程序子包

uniapp主包 -> uniapp子包
uniapp子包 -[#blue]-> uniapp主包
uniapp子包 -> uniapp子包


调用方 <.[#red].> 被调方: 不可互相调用
调用方 -> 被调方: 分包异步化
调用方 -[#orange]-> 被调方: 相对路径引用
调用方 -[#blue]-> 被调方: 大仓包引用（源码）


@enduml