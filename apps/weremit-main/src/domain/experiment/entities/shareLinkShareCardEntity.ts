/**
 * shareLink分享卡片实体
*/

import { CustomException } from '@/adapters/report';

/**
 * 卡片类型
*/
export enum CardType{

  /**
   * 默认卡片
  */
  DEFAULT_CARD = 'defaultCard',

  /**
   * 绿色大卡片
  */
  GREEN_BIG_CARD = 'greenBigCard',

  /**
   * 橘色大卡片
   */
  ORANGE_BIG_CARD = 'orangeBigCard',
}

/**
 * 实验key
*/
enum ExptKey {
  /**
   * shareLink分享卡片实验
   * 分一个实验组、一个对照组
  */
  SHARELINK_SHARE_CARD = 'expt_shareCard',
}

export class ShareLinkShareCardExperiment {
  /**
   * 用户命中的卡片类型
  */
  private cardType: CardType;

  constructor() {
    this.cardType = this.getInitCardType();
  }

  getCardInfo(): CardType {
    return this.cardType;
  }

  /**
   * 获取用户命中的卡片类型
  */
  private getInitCardType(): CardType {
    /**
     * 如果用户基础库不支持getExptInfoSync API，则返回默认卡片类型
    */
    if (!wx.getExptInfoSync) {
      new CustomException(null, 'shareLinkCardType_unSupportApi', 'shareLink分享卡片不支持小程序实验api');
      return CardType.DEFAULT_CARD;
    }

    // 调用wxAPI拿到命中的实验
    const exptObj = wx.getExptInfoSync(Object.values(ExptKey));

    // 根据命中的实验返回不同的卡片类型
    switch (exptObj[ExptKey.SHARELINK_SHARE_CARD]) {
      case '0':
        return CardType.GREEN_BIG_CARD;
      case '1':
        return CardType.ORANGE_BIG_CARD;
      default:
        new CustomException(exptObj[ExptKey.SHARELINK_SHARE_CARD], 'shareLinkCardType_unknownExptType', '未知的shareLink分享卡片实验类型');
        return CardType.DEFAULT_CARD;
    }
  }
}
