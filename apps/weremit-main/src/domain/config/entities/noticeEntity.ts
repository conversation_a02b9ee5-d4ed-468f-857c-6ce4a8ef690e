import { FastReceiptItem } from '@/data-source/config/notice-fast-receipt';
import { getTimeLeft } from '@/adapters/time';
export interface NoticeParam {
  page: string,
  branchType: string
}
export class NoticeEntity {
  /**
   * 生效的公告配置
   */
  public validConfig: FastReceiptItem | undefined;
  /**
   * 原始数据
   */
  private originData: FastReceiptItem[] = [];
  private params: NoticeParam;

  constructor(data: FastReceiptItem[] = [], params: NoticeParam) {
    this.originData = data;
    this.params = params;
    this.validConfig = this.filterValidConfig();
  }
  // 禁用功能是否开启
  get isDisableValid() {
    // 公告生效，禁用才会生效，所以先筛选出生效的公告
    if (!this.validConfig) {
      // 未开启禁用
      return false;
    }
    const validDisable = this.checkDisableValid();
    return validDisable;
  }

  // 检查禁用功能是否生效
  private checkDisableValid() {
    // 是否启用禁用功能
    if (!this.validConfig || +this.validConfig.isDisable === 0) {
      // 未启用禁用功能，直接返回false
      return false;
    }

    // 验证生效日期
    if (!this.isDateValid(this.validConfig.startDisableTime, this.validConfig.endDisableTime)) {
      return false;
    }
    // 验证禁用页面
    const pages = this.validConfig.disablePage.split(',');
    const isPageValidate = pages.includes(this.params.page);
    if (!isPageValidate) {
      return false;
    }
    // 验证禁用渠道
    const branchTypes = this.validConfig.disableBranchType.split(',');
    if (this.params.branchType !== 'ignore' && !branchTypes.includes('all') && !branchTypes.includes(this.params.branchType)) {
      return false;
    }
    return true;
  }
  /**
   * 判断是否在时间内
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 是否在时间内
   */
  private isDateValid(startTime: string | number, endTime: string | number) {
    return getTimeLeft(new Date(startTime)) <= 0 && getTimeLeft(new Date(endTime)) >= 0;
  }
  // 过滤出有效公告
  private filterValidConfig() {
    const data = this.originData;
    const { params } = this;
    const noticeItems = data.filter((item: any): boolean => {
      // 筛选生效日期
      if (!this.isDateValid(item.startNoticeTime, item.endNoticeTime)) {
        return false;
      }
      // 筛选生效页面
      const pages = item.noticePage.split(',');
      const isPageValidate = pages.includes(params.page);
      if (!isPageValidate) {
        return false;
      }
      const branchTypes = item.branchType.split(',');
      // 筛选生效渠道
      // (1)ignore—忽略渠道字段，用于一些不用渠道判断的页面，例如：首页
      // (2)生效渠道包含all
      // (3)配置项渠道包含当前渠道
      if (params.branchType !== 'ignore' && !branchTypes.includes('all') && !branchTypes.includes(params.branchType)) {
        return false;
      }
      return true;
    });
    if (noticeItems.length > 1) {
      return this.getItemByWeight(noticeItems);
    }
    return noticeItems[0] as FastReceiptItem | undefined;
  }

  // 根据权重优先级筛选
  private getItemByWeight(items: FastReceiptItem[]) {
    let targetItem = items[0];
    items.forEach((item: any) => {
      // 权重值越大，优先级越高
      if (+item.weight > +targetItem.weight) {
        targetItem = item;
      }
    });
    return targetItem;
  }
}
