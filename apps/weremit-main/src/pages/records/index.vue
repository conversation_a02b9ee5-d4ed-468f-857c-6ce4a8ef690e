<template>
  <view class="records pt-40rpx observer-container">
    <remit-cell-item
      v-for="(item, index) in myData.cell"
      :key="index"
      :cell-item="item"
    />
  </view>
</template>

<script setup lang="ts">
import { ConfigService } from '@/domain/config/configService';
import { useTabbar } from '@/composition/useTabbar';
const myData = ConfigService.getRecordCellConfig();
useTabbar();
</script>

<style scoped lang="scss">
.records {
  padding: 24rpx 32rpx;
  background-color: #F7F7F7;
  margin: 0;
  box-sizing: border-box;
  height: 100%;
}
</style>
