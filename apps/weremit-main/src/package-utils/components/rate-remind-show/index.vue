<template>
  <RateRemindShow
    theme-color="#6FDEA1"
    :src-currency="props.srcCurrency"
    :dest-currency="props.destCurrency"
    :src-country="props.srcCountry"
  />
</template>

<script setup lang="ts">
import type { Currency } from '@/adapters/functions';
import RateRemindShow from '@tencent/ppd-uni-component/packages/exchange-rate/remind-show/index.vue';
import { RATE_SERVICE_KEY } from '@tencent/ppd-uni-component/packages/exchange-rate/token';
import { OfficialAccountService, RateServer } from '@tencent/ppd-uni-domain';
import { useMpAdapterEmit } from '@tencent/ppd-uni-common/src/hooks/useMpAdapter';

import Container from 'typedi';
import { PropType } from 'vue';
import { OFFICIAL_ACCOUNT_KEY } from '@tencent/ppd-uni-component/packages/official-account-popup/token';
provide(RATE_SERVICE_KEY, Container.get(RateServer));
provide(OFFICIAL_ACCOUNT_KEY, Container.get(OfficialAccountService));
const _emit = defineEmits<(e: 'action') => void>();
// 兼容原生小程序
useMpAdapterEmit(_emit);

const props = defineProps({
  srcCurrency: {
    type: String as PropType<Currency>,
    required: true,
    default: 'USD',
  },
  destCurrency: {
    type: String as PropType<Currency>,
    required: true,
    default: 'CNY',
  },
  srcCountry: {
    type: String,
    required: true,
  },
});


</script>
<style>
</style>
