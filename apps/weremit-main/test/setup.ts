import { vi } from 'vitest';

vi.mock('@/adapters/report', () => ({
  CustomException: vi.fn(),
}));

const storage: Record<string, any> = {};
const uniObj = {
  showToast: vi.fn((options) => {
    options.complete?.();
  }),
  showLoading: vi.fn(),
  hideLoading: vi.fn(),
  showModal: vi.fn(),
  loadFontFace: vi.fn(),
  login: (options: any) => {
    setTimeout(() => {
      options.success({
        code: 'xxxx',
      });
    }, 100);
  },
  showShareMenu: vi.fn(),
  reportAnalytics: vi.fn(),
  navigateTo: vi.fn(),
  redirectTo: vi.fn(),
  reLaunch: vi.fn(),
  onPageNotFound: vi.fn(),
  getStorageSync: vi.fn().mockImplementation(cacheKey => storage[cacheKey]),
  setStorageSync: vi.fn().mockImplementation((cacheKey, data) => {
    storage[cacheKey] = data;
  }),
  removeStorageSync: vi.fn().mockImplementation((cacheKey) => {
    delete storage[cacheKey];
  }),
  getStorage: vi.fn().mockImplementation(async cacheKey => storage[cacheKey]),
  setStorage: vi.fn().mockImplementation(async (cacheKey, data) => {
    storage[cacheKey] = data;
  }),
  removeStorage: vi.fn().mockImplementation(async (cacheKey) => {
    delete storage[cacheKey];
  }),
  getAccountInfoSync: vi.fn(() => ({
    miniProgram: {
      appId: 'wx5b7cda9d14819945',
      envVersion: 'develop',
      version: '',
    },
  })),
  getSystemInfoSync: vi.fn(() => ({
    SDKVersion: '2.15.0',
    batteryLevel: 100,
    benchmarkLevel: 1,
    brand: 'vi-devtools',
    deviceOrientation: 'portrait',
    devicePixelRatio: 3,
    enableDebug: false,
    fontSizeSetting: 16,
    language: 'zh_CN',
    model: 'iPhone X',
    pixelRatio: 3,
    platform: 'devtools',
    safeArea: {
      bottom: 778,
      height: 734,
      left: 0,
      right: 375,
      top: 44,
      width: 375,
    },
    screenHeight: 812,
    screenWidth: 375,
    statusBarHeight: 44,
    system: 'iOS 10.0.1',
    version: '7.0.4',
    windowHeight: 730,
    windowWidth: 375,
  })),
  onAppRoute: vi.fn(() => {

  }),
  reportPerformance: vi.fn(() => {

  }),
  navigateToMiniProgram: vi.fn((options) => {
    options.success?.();
    options.complete?.();
  }),
  setBackgroundColor: vi.fn(() => {

  }),
};
vi.stubGlobal('uni', uniObj);
vi.stubGlobal('wx', uniObj);
