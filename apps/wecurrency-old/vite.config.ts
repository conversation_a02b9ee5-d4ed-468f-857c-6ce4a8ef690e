import conf from '@tencent/ppd-uniapp-devconfig/vite.config';
import mpStaticLoader from '@tencent/ppd-uniapp-devconfig/plugins/mp-static-loader';
import { defineConfig } from 'vite';
import packageJson from './package.json';
// @ts-ignore
import viteBiMpPlugin from '@tencent/fit-bi-sdk/vite-mp-loader';

const dir = packageJson.name;
const isProduction = process.env.NODE_ENV === 'production';
// https://vitejs.dev/config/
const config = Object.assign({}, conf, {
  base: isProduction ? `//weremit-static.tenpay.com/${dir}/` : '/wecurrency/',
});
config.plugins.push(viteBiMpPlugin());
if (isProduction && process.env.UNI_PLATFORM === 'mp-weixin') {
  config.plugins.push(mpStaticLoader({
    // cos路径前缀
    base: `https://weremit-static.tenpay.com/${dir}-mp/`,
    // 静态文件生成的目标目录
    dest: process.env.UNI_OUTPUT_DIR,
    // 静态文件生成到目标目录的哪个目录
    outputDir: '/cdn',
    // 不用上传cdn
    exclude: ['**'],
  }));
}
export default defineConfig(config);

