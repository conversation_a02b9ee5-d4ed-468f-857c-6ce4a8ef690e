import { loginChannel } from './../../data-source/base/type';
import { getMsgNo } from '@/utils/string';
import { useUserStore } from '@/store/modules/user/index';
import { BaseException, HttpExceptions, RequestBottomExceptions, createUniAppRequest, OtherConfig, RequestOptions } from '@tencent/ppd-common/src/request/index';
import { commonModal } from '../uni-api-common';
import { NEED_TO_LOGIN_ERROR_CODE, getResReturn, CommonReturnCode } from './code';
import { dealLoginFail, reLogin } from './reLogin';

export {
  BaseException,
  HttpExceptions,
  RequestBottomExceptions,
  getResReturn,
  CommonReturnCode,
  NEED_TO_LOGIN_ERROR_CODE,
};

/**
 * 基础返回内容
 */
export interface CommonResponse {
  /**
   * 响应状态码
   */
  retcode: string;
  /**
   * 响应信息
   */
  retmsg: string;
}

/**
 * 接口基础错误返回格式
 */
export class BaseErrorResponse implements CommonResponse {
  constructor(
    /**
     * 响应状态码
     */
    public retcode: string,
    /**
     * 响应信息
     */
    public retmsg: string,
    /**
     * 数据原串
     */
    public rawData: CommonResponse | unknown = {
      retcode,
      retmsg,
    },
  ) {}
}

/**
 * 请求选项
 */
export interface BaseRequestOptions<OtherConfig> extends RequestOptions<OtherConfig> {
  /**
   * 是否需要登陆, 默认为true
   */
  isNeedLogin?: boolean;

  /**
   * 重试登陆次数
   */
  reLoginLimit?: number;
};

/**
 * 业务异常类
 */
export class BusinessExceptions extends BaseException  {
  name = 'BusinessExceptions';
  /**
   * 业务错误状态码
   */
  retcode: string;

  /**
   * 业务错误信息
   */
  retmsg: string;

  constructor(retcode: string, retmsg: string, rawData?: unknown, message = retmsg) {
    super(message, rawData);
    console.log('业务异常');
    this.retcode = retcode;
    this.retmsg = retmsg;
  }
}

/**
 * 请求的默认地址前缀
 */
let baseURL =  'https://weremit.tenpay.com/cgi-bin';
// #ifdef H5
baseURL = '/cgi-bin';
// #endif
export const { toRequest, baseRequest, requestProxy } = createUniAppRequest({
  baseUrl: baseURL,
  config: {
    // header: {
    // 设置默认header
    // 'content-type': 'application/json',
    // },
    // timeout: 2000, // 设置定时
  },
});

/**
 * 设置请求拦截器
 */
requestProxy.overrideInterceptor((interceptor) => {
  // 请求拦截器
  interceptor.beforeRequest.use(async (options: BaseRequestOptions<OtherConfig>) => {
    // 使用user的store
    const { loginData } = useUserStore();
    const header: OtherConfig['header'] = options.config?.header ?? {};
    // 非登陆接口写入登陆凭证
    if (options.isNeedLogin) {
      if (!loginData.qlskey) {
        await reLogin();
      }
      header.qlskey = loginData.qlskey;
    }
    if (typeof options.data === 'object') {
      // eslint-disable-next-line no-param-reassign
      options.data = {
        msg_no: getMsgNo(),
        login_channel: loginChannel,
        ...options.data,
      };
    }
    Object.assign(options, {
      config: {
        ...options.config,
        header,
      },
    });
    console.log('request:::config', options);
    return options;
  });

  // 响应拦截器
  interceptor.afterResponse.use(
    (httpResponse) => {
      const response = httpResponse.data as CommonResponse;
      // 对业务异常进行特殊处理
      const { code, message } = getResReturn(response);
      if (code === CommonReturnCode.SUCCESS) {
        return httpResponse;
      }
      return Promise.reject(new BusinessExceptions(
        code,
        message,
        // 错误数据原串
        new BaseErrorResponse(
          response.retcode,
          response.retmsg,
          // 错误数据原串
          response,
        ),
      ));
    },
    (err) => {
      if (err instanceof BusinessExceptions) {
        const options = interceptor.getDefaultOptions() as BaseRequestOptions<OtherConfig>;
        const { code, message } = getResReturn(err);
        // 处理未登录或登录失效
        if (
        // 判断业务状态码是否为未登陆需要登陆的状态码
          NEED_TO_LOGIN_ERROR_CODE.includes(code)
          // 判断接口是否需要重新登陆
          && options.isNeedLogin
        ) {
          // 判断重新登陆次数是否超过限制
          if (Number(options?.reLoginLimit) > 0) {
            // 调用重新登陆逻辑
            return reLogin()
              .then(() => {
                if (options.reLoginLimit) {
                  Object.assign(
                    options,
                    {
                      reLoginLimit: options.reLoginLimit - 1,
                    },
                  );
                }
                return request(options);
              });
          }
          // 出现重试一次登陆接口成功后，重试接口请求还是失败情况下弹出弹窗让用户手动登陆
          commonModal({
            content: message,
            showCancel: false,
            complete() {
              dealLoginFail();
            },
          });
        }
      }
      return Promise.reject(err);
    },
  );
});

/**
 * 基础请求封装
 * @param options 请求参数
 * @returns
 */
export const request = async <Response>(
  options: BaseRequestOptions<OtherConfig>,
): Promise<Response & CommonResponse> => {
  // 设置默认值
  Object.assign(options, {
    reLoginLimit: typeof options.reLoginLimit === 'number' ? options.reLoginLimit : 1,
    isNeedLogin: typeof options.isNeedLogin === 'boolean' ? options.isNeedLogin : true,
  });
  return toRequest<Response & CommonResponse>(options);
};
