/*
 * @Author: jeakeyliang <EMAIL>
 * @Date: 2023-05-13 16:14:50
 * @LastEditors: jeakeyliang <EMAIL>
 * @LastEditTime: 2023-05-13 16:36:51
 * @FilePath: /wecurrency_main/ppd-uniapp/apps/wecurrency/src/constant/app.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const systemInfo = uni.getSystemInfoSync();
let aegisId = 'WeMEdqJEhkvPAkDXkU';
// #ifdef MP-WEIXIN
aegisId = 'WeMEdqJEMnDpLkKGjO';
// #endif

// 环境判断，h5目前只能区分本地环境
let env = process.env.NODE_ENV === 'development' ? 'local' : 'production';
// 如果是小程序，可以获取真实的环境信息
if (typeof uni.getAccountInfoSync === 'function') {
  env = uni.getAccountInfoSync()?.miniProgram?.envVersion ?? 'production';
  // 统一环境标识
  env = env === 'release' ? 'production' : env;
}
export default Object.assign({}, systemInfo, {
  codeVersion: process.env.VERSION,
  aegisId,
  platform: systemInfo.platform,
  env,
});


