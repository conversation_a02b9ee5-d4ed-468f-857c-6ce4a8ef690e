{"retcode": "0", "retmsg": "OK", "use_std_pay": "2", "elements": [{"param_category": "trade_info", "param_description": "6ZmE6KiA", "param_max_len": "18", "param_max_value": "0", "param_min_len": "0", "param_min_value": "0", "param_name": "attach", "param_regex": "XltcdyBdKiQ=", "param_requirement": "2", "param_type": "30", "regex_error_alert": "6K+36L6T5YWl5Y2K6KeS6Iux5paH5oiW5pWw5a2X5a2X56ym"}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa56LSm5oi35Y+356CB", "param_max_len": "12", "param_max_value": "0", "param_min_len": "6", "param_min_value": "0", "param_name": "student_recv_account", "param_regex": "XlswLTldKiQ=", "param_requirement": "1", "param_type": "37", "regex_error_alert": "6K+36L6T5YWl5pWw5a2X"}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa56LSm5oi35ZCN56ew", "param_max_len": "120", "param_max_value": "0", "param_min_len": "1", "param_min_value": "0", "param_name": "student_recv_name", "param_regex": "XlthLXpBLVogJy1dKiQ=", "param_requirement": "1", "param_type": "30", "regex_error_alert": "6K+36L6T5YWl5Y2K6KeS6Iux5paH5a2X56ym77yM5Y+v55So56ym5Y+35YyF5ousJy0="}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa55Zyw5Z2A", "param_max_len": "140", "param_max_value": "0", "param_min_len": "1", "param_min_value": "0", "param_name": "student_recv_account_address", "param_regex": "XlthLXowLTlBLVogJyxcLy1dKiQ=", "param_requirement": "1", "param_type": "30", "regex_error_alert": "6K+36L6T5YWl5Y2K6KeS6Iux5paH5oiW5pWw5a2X5a2X56ym77yM5Y+v55So56ym5Y+35YyF5ousJ++8jC8tIA=="}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa55omA5Zyo5Z+O5biCKFN1cmJ1cmIp", "param_max_len": "35", "param_max_value": "0", "param_min_len": "1", "param_min_value": "0", "param_name": "student_recv_city", "param_regex": "XlthLXpBLVogJy1dKiQ=", "param_requirement": "1", "param_type": "30", "regex_error_alert": "6K+36L6T5YWl5Y2K6KeS6Iux5paH5a2X56ym77yM5Y+v55So56ym5Y+35YyF5ousJy0="}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa55omA5Zyo5bee5Luj56CBKFN0YXRlKQ==", "param_max_len": "3", "param_max_value": "0", "param_min_len": "2", "param_min_value": "0", "param_name": "student_recv_province", "param_regex": "XltBLVpdKiQ=", "param_requirement": "1", "param_type": "37", "regex_error_alert": "6K+36L6T5YWl5aSn5YaZ6Iux5paH5a2X5q+N"}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa55Zu95a62", "param_max_len": "3", "param_max_value": "0", "param_min_len": "3", "param_min_value": "0", "param_name": "student_recv_country", "param_regex": "XltBLVpdKiQ=", "param_requirement": "1", "param_type": "35", "regex_error_alert": "6K+36L6T5YWl5aSn5YaZ6Iux5paH5a2X5q+N"}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa56YKu57yWKFBvc3Rjb2RlKQ==", "param_max_len": "4", "param_max_value": "0", "param_min_len": "4", "param_min_value": "0", "param_name": "student_recv_postcode", "param_regex": "XlswLTldKiQ=", "param_requirement": "1", "param_type": "37", "regex_error_alert": "6K+36L6T5YWl5pWw5a2X"}, {"param_category": "payee_base_info", "param_description": "5pS25qy+5pa56KGX6YGT5Zyw5Z2A", "param_max_len": "90", "param_max_value": "0", "param_min_len": "1", "param_min_value": "0", "param_name": "student_recv_street_address", "param_regex": "XlthLXowLTlBLVogJywvLV0k", "param_requirement": "1", "param_type": "30", "regex_error_alert": "6K+36L6T5YWl5Y2K6KeS6Iux5paH5a2X56ym77yM5Y+v55So56ym5Y+35YyF5ousJy8t"}, {"param_category": "beneficiary_bank_info", "param_description": "QlNCLUF1c3RyYWxpYW4gYmFuayBzdGF0ZSBicmFuY2ggY29kZQ==", "param_max_len": "6", "param_max_value": "0", "param_min_len": "6", "param_min_value": "0", "param_name": "routing_code", "param_regex": "XlswLTldKiQ=", "param_requirement": "1", "param_type": "37", "regex_error_alert": "6K+36L6T5YWl5pWw5a2X"}, {"param_category": "beneficiary_bank_info", "param_description": "5pS25qy+6KGM5ZCN56ew", "param_max_len": "140", "param_max_value": "0", "param_min_len": "1", "param_min_value": "0", "param_name": "bank_name", "param_regex": "", "param_requirement": "1", "param_type": "30", "regex_error_alert": ""}, {"param_category": "beneficiary_bank_info", "param_description": "5pS25qy+6KGM5Zyw5Z2A", "param_max_len": "140", "param_max_value": "0", "param_min_len": "1", "param_min_value": "0", "param_name": "bank_address", "param_regex": "", "param_requirement": "1", "param_type": "30", "regex_error_alert": ""}, {"param_category": "beneficiary_bank_info", "param_description": "5pS25qy+6KGMU3dpZnQgQ29kZQ==", "param_max_len": "11", "param_max_value": "0", "param_min_len": "8", "param_min_value": "0", "param_name": "swift_code", "param_regex": "XltBLVpdezZ9W0EtWjAtOV17Mn0oW0EtWjAtOV17M30pPyQ=", "param_requirement": "1", "param_type": "35", "regex_error_alert": "6K+36L6T5YWlOOaIljEx5L2N5pyJ5pWIU1dJRlTku6PnoIHvvIzku4XmlK/mjIHlpKflhpnlrZfmr43lkozmlbDlrZfnu4TlkIg="}, {"param_category": "beneficiary_bank_info", "param_description": "5pS25qy+6KGM5omA5Zyo5Zu95a62L+WcsOWMug==", "param_max_len": "3", "param_max_value": "0", "param_min_len": "3", "param_min_value": "0", "param_name": "student_recv_country", "param_regex": "XltBLVpdKiQ=", "param_requirement": "1", "param_type": "35", "regex_error_alert": "6K+36L6T5YWl5aSn5YaZ6Iux5paH5a2X5q+N"}]}