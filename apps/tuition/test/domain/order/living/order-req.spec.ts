/**
 * @jest-environment jsdom
 */
import { DetailQryResp } from '@/api/order/tuition/tuition-order';
import { useOrderUpdateReq } from '@/domain/order/living/order-req';

const detailData = {
  retcode: '0',
  retmsg: 'OK',
  account: '**********************',
  account_code: '********************',
  account_name: '<PERSON>VHS Mariaspring',
  attach: 'ada',
  bank_name: 'Sparkasse Gottingen',
  big_pay_finish_flag: '0',
  big_pay_flag: '1',
  big_pay_not_in_use_left_time: '0',
  big_pay_not_in_use_reason: '',
  can_edit_more_again: '1',
  channel_id: 'we_remit',
  crn_code: '',
  crn_type: '0',
  currency: 'USD',
  currency_point_num: '2',
  default_pay_type: '1',
  edit_flag: '1',
  is_user_list: '1',
  kyc_flag: '0',
  last_audit_fail_reason: '',
  last_audit_fail_time: '',
  list_state: '20010',
  list_user_name: '',
  listid: '5991800008742202107163280012305',
  pay_amt: '117857',
  pay_type_tips: '受周末/节假日影响，仅部分银行支持大额支付，具体以页面展示为准。',
  rate: '0',
  relation: '8',
  routing_code: '**********************',
  routing_type: 'IBA',
  school_name_en: 'Torrens University Australia Ltd',
  school_system: '14',
  sign: 'AW8BAGqKbD8spKIslRX4nFutHagiCX0In1y42GuiBfWuzhqP_liVge08lebEIT3uImE5G0gfju_pkeNVNzgQC86vKsn0Dw1Ua-oAcbXoSTIiGk4nl_ZSSmBy0IKPhj0dH-SZ9zqNnLcUMP0aHScnGl5lzvTkq98PeEsuDex-QDCLpewwJvzE9Q',
  student_cre_id: '510403********0331',
  student_email: '1****@qq.com',
  student_first_name: 'shi',
  student_id: '*********',
  student_last_name: 'c*',
  student_name: '*试',
  student_passport_no: '123****23',
  student_phone: '123****2312',
  swift_code: 'NOLADE21GOE',
  total_fee: '201102',
  original_total_fee: '3101110',
  tuition_amt: '2342300',
  tuition_deadline: '2021-08-15',
  type: '1',
  file_list: [
    {
      file_id: 'AW8BAGqKbD-4t28foE1m98pWC6FARXo1Syuug9I1eYavK21OS_uKuOu5f2h1cY7Ac5ZXxtbJmQ5axUIFXmCEKOmB1AVqW_Z_MmKCUd0LfqwcTaWVBclffYLTtH0R9ka1_P1cJXtoKNk',
      file_type: 'OFE',
    },
    {
      file_id: 'AW8BAGqKbD-4t28foE1m98pWC6FARXo1Syuug9I1eYavK21OS_uKuEQ9acXSTm4ho6VSXeH65JTeqH0QPEph2NssBBKNjwv78kBy7BqNFvDYcApT1Tfq4Rot21KborwpPeIzdKoajL8',
      file_type: 'INV',
    },
  ],
};

describe('更新订单', () => {
  it('更新订单', () => {
    // @ts-ignore
    const { params, oldForm, getDiffForm } = useOrderUpdateReq(detailData as unknown as DetailQryResp);
    expect(JSON.stringify(oldForm)).toBe(JSON.stringify(detailData));
    // @ts-ignore
    expect(params.value.listid).toBe(oldForm.listid);
    const diffObj = {
      student_recv_account: 'test',
    };
    // 变更数值
    Object.keys(diffObj).forEach((key) => {
      params.value[key] = diffObj[key];
    });
    expect(getDiffForm()).toEqual(diffObj);
  });
});
