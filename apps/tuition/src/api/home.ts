import { request } from '@/adapters/request';

export interface StudentItem {
  /**
   * 学生编码
   */
  student_code: string;
  /**
   * 学生姓名
   */
  student_name: string;
  /**
   * 学校编码
   */
  school_code: string;
  /**
   * 学校英文名
   */
  school_name_en: string;
  /**
   * 账号编码
   */
  account_code: string;
  /**
   * 学校所在国家代码
   */
  school_country_code: string;
  /**
   * 学校中文名
   */
  school_name_cn: string;
}

/**
 * 首页接口返回数据
 */
export interface HomeInitResponse {
  /**
   * 待支付订单数量
   * 说明：
   * 0-不展示待支付模块
   * 1-展示待支付模块，点击跳转到确认支付页
   * 大于1-展示待支付模块，点击跳转到缴费记录页
   */
  wait_pay_count: number;
  /**
   * 待支付订单号
   * 说明：
   * wait_pay_count=1时返回
   */
  wait_pay_listid?: string;
  /**
   * 灰度标志
   * 说明：
   * 是否灰度发布阶段
   * 1：是
   * 其他：否
   */
  gray_switch: number;
  /**
   * 用户类型
   * 说明：
   * 当gray_switch=1时才返回
   * 1：内部测试用户
   * 2：外部测试用户（种子用户）
   * 3：普通用户
   */
  user_type?: number;
  /**
   * 常用缴费信息列表
   */
  student_list?: StudentItem[];
}

/**
 * 首页初始化接口
 * @returns 返回待支付订单&常用缴费信息列表
 */
export const getHomeInitMessage = () => request<HomeInitResponse>({
  url: '/tuition/homepage_init.fcgi',
});
