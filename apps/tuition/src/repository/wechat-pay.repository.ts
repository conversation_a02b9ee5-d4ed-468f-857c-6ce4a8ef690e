import { payCallback } from '@/api/pay';
import { IWechatPayRepository, PayCallbackReq } from '@tencent/tuition-domain/pay/repository/wechat-pay.repository.interface';

export class WechatPayRepository implements IWechatPayRepository {
  /**
   * 获取支付订单详情
   */
  payCallback(params: PayCallbackReq): Promise<void> {
    return payCallback(params).then(() => {
      console.log('payCallback resolve');
    });
  }
}
