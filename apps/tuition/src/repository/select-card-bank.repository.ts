import { getPreCardSession } from '@/api/select-card';
import { ISelectBankCardRepository, PreCardParam, SelectCardData } from '@tencent/tuition-domain/pay/repository/select-bank-card.repository.interface';


export class SelectCardBankRepository implements ISelectBankCardRepository {
  async preQuerySelectCard(params: PreCardParam): Promise<SelectCardData> {
    const result = await getPreCardSession(params);
    return {
      sessionid: result.sessionid,
    };
  }
}
