import { RealNameRepository } from '../../repository/real-name.repository';
import { dependencies } from '@tencent/tuition-real-name';
import { useModule } from '@tencent/tuition-common';
import { isInWhiteList } from '@/adapters/white-list';
import { LOGIN_CHANNEL } from '@/constant/app';
import { navigateToWebView } from '@/route/web-view';

export default useModule({
  dependencies,
  setup() {
    // 实名仓储实现
    const realNameRepository = new RealNameRepository();
    return {
      realNameRepository,
      isInWhiteList() {
        return isInWhiteList();
      },
      logInChannel: LOGIN_CHANNEL,
      navigateToWebView(params, navigateType) {
        navigateToWebView(params, navigateType);
      },
      topImg: 'https://weremit-static.tenpay.com/upload-static/tuition/UD5fDn0XaawsmcMdPSngDj-frame687.png',
      safetyImg: 'https://weremit-static.tenpay.com/upload-static/tuition/BkvEBBX8P8fmjxy6a7BvDh-tuition-safety-icon.png',
      privacyProtocolConfig: {
        privacyProtocolText: '《腾讯留学缴费隐私政策》',
        privacyProtocolUrl: 'https://posts.tenpay.com/posts/73ede71e63b785ac002442b57335b9a6.html',
        privacyProtocolDetailText: '《腾讯留学缴费隐私政策》第5.2条',
        privacyProtocolDetailUrl: 'https://posts.tenpay.com/posts/c749a6be63b786290023587452a252e5.html',
      },
      serviceProtocolConfig: {
        serviceProtocolText: '《腾讯留学缴费服务协议》',
        serviceProtocolUrl: 'https://posts.tenpay.com/posts/6c14146e639c150f002c93a11fb21be4.html',
      },
    };
  },
});
