/** @type { import('@storybook/vue3').Preview } */
import { INITIAL_VIEWPORTS } from '@storybook/addon-viewport'
import { setup } from '@storybook/vue3';
import 'virtual:uniapp-global-css';
import vuePluginForUniapp from 'virtual:vue-plugin-for-uniapp';

setup((app) => {
  app.use(vuePluginForUniapp);
});
const preview = {
  parameters: {
    viewport: {
      viewports: INITIAL_VIEWPORTS,
      defaultViewport: 'iphone12'
    },
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
};

export default preview;
