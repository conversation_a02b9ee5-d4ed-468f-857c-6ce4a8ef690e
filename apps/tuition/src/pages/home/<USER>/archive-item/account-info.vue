<!--
 * @LastEditTime: 2023-04-24 14:18:02
 * @FilePath: /ppd-uniapp/apps/tuition/src/pages/home/<USER>/archive-item/account-info.vue
 * @Description: 档案卡中部汇款账户信息，包括就读院校等
-->

<template>
  <view
    v-if="userAccount.schoolNameEn"
    class="flex flex-row items-start"
  >
    <view class="text5-reg color-text-auxiliary">
      就读院校
    </view>
    <view class="text5-sem color-text-title ml-40rpx w-406rpx h-112rpx">
      <view class="school-en">
        {{ userAccount.schoolNameEn }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { UserAccount } from './type';
type Props = {
  // 学校英文名
  userAccount: UserAccount;
};

withDefaults(defineProps<Props>(), {});

</script>

<style scoped lang="scss">
.school-en{
  display: -webkit-box; /* 显示为块级元素 */
  -webkit-box-orient: vertical; /* 垂直排列 */
  -webkit-line-clamp: 2; /* 最多显示两行 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}
</style>
