<template>
  <archiveItemContainer :is-last-child="true">
    <view
      class="w-100% h-100% flex flex-col items-center justify-center mt-20rpx "
      @click="onClick"
    >
      <view class="circle mb-32rpx mr-12rpx" />
      <view class="text5-sem color-text-auxiliary mr-12rpx">
        给其他学生缴费
      </view>
    </view>
  </archiveItemContainer>
</template>

<script setup lang="ts">
import { handleNewUserEntrancesClick } from '../../composition';
import { EntranceType } from '../entrances/type';
import archiveItemContainer from './archive-item-container.vue';
// 点击新增账号
const onClick = () => {
  handleNewUserEntrancesClick({
    type: EntranceType.TUITION_FEE,
    isNewUser: true,
  });
};
</script>

<style scoped lang="scss">
.circle {
  @apply bg-primary-normal;
  width: 95rpx;
  height: 95rpx;
  border-radius: 50%;
  position: relative;
}

.circle::before,
.circle::after {
  content: "";
  position: absolute;
  border-radius: 1rpx;
  top: 50%;
  left: 50%;
  width: 63%;
  height: 8rpx;
  background-color: white;
  transform: translate(-50%, -50%) rotate(0deg);
}

.circle::after {
  transform: translate(-50%, -50%) rotate(-90deg);
}
</style>
