/** 这里编写storybook文档*/

import But<PERSON> from './index.vue';
type Template = <Args = unknown, Props extends Record<string, {
  argTypes: Record<string, unknown>;
}> = Record<string, {
  argTypes: Record<string, unknown>;
}>>(args: Args, props: Props) => Record<string, unknown>;

export default {
  title: '业务组件/Button',
  component: Button,
  parameters: {
    design: {
      type: 'figma',
      url: '',
    },
  },
};


const Template: Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Button },
  template: '<Button v-bind="$props"/>',
});

export const 默认 = Template.bind({});

// 这里写props参数的mock数据
(默认 as unknown as { args: Record<string, unknown> }).args = {};

