/** 组件的入口
标准格式：
  组件实例导出名字: Component
  组件类型导出名字：Type
  组件Composition导出名字：Composition

  例子:
  import Component from './index.vue';
  import * as Composition from './composition';
  import type * as Type from './types';

  export {
    Component,
    Composition,
    Type
  };

*/


import Component from './index.vue';
import * as Composition from './composition';
import type * as Type from './types';

export {
  Component,
  Composition,
  Type,
};
