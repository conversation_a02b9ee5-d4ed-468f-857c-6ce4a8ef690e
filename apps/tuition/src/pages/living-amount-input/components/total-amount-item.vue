<template>
  <div class="mb-32rpx flex justify-between">
    <div class="flex items-center">
      <div class="color-[rgba(0,0,0,.4)] text5-reg mr-4rpx">
        {{ props.label }}
      </div>
      <Tooltip
        v-if="props.tip"
        :content="props.tip"
        placement="bottom-start"
      >
        <div class="p-4rpx">
          <p-icon
            v-if="props.tip"
            name="info"
            class="color-icon-schematic w-32rpx h-32rpx"
            @click="handleTooltipClick"
          />
        </div>

        <template #content>
          <div
            v-for="item in props.tip.split('\\n')"
            :key="item"
          >
            {{ item }}
          </div>
        </template>
      </Tooltip>
    </div>
    <AmountValue
      :tags="tags"
      :fake-value="fakeValue"
      :value="value"
      :unit="unit"
    />
  </div>
</template>

<script setup lang="ts">
// import Tooltip from '@/components/feedback/tooltip.vue';
import Tooltip from '@/components/feedback/zb-tooltip.vue';
import AmountValue from './amount-value.vue';

const props = withDefaults(defineProps<{
  label: string,
  tags?: {tag: string, color: string, borderColor: string}[],
  fakeValue?: string,
  value: string,
  tip?: string,
  unit?: string,
}>(), {
  label: '',
  tags: () => [],
  fakeValue: '',
  value: '',
  tip: '',
  unit: '',
});

const emit = defineEmits(['click']);

const showTooltip = ref(false);
function handleTooltipClick() {
  showTooltip.value = !showTooltip.value;
  emit('click');
}

</script>
