<template>
  <view class="h-100% observer-container">
    <!-- 实名协议start -->
    <RealNameProtocal
      v-show="!isSigned"
      @agree="signProtocal"
    />
    <!-- 实名协议end -->
    <f-page
      v-show="isSigned"
      :show-customer-service="true"
      :is-customer-service-jump-official-account="true"
      @click="handlePageClick"
    >
      <!-- 隐私协议弹窗 -->
      <PrivacyDialog />
      <div
        v-stat.brow.leave="'living_amt.brow'"
        class="pt-40rpx pr-32rpx pl-32rpx"
      >
        <div class="title3-sem mb-40rpx">
          填写生活费汇款金额
        </div>
        <div class="mb-80rpx">
          <AmountInputGroup
            v-model:amtInputBox="amtInputBox"
            :title-box="titleBox"
            :placeholder-box="placeholderBox"
            :focus-placeholder-box="focusPlaceholderBox"
            :err-box="errBox"
            :hold-keyboard="false"
            :digit-box="digitBox"
            @blur="onBlur"
            @focus="onFocus"
            @change="onChange"
          >
            <template #prefix-BUY>
              <div
                class="flex items-center"
                @click="handleCurrencyClick"
              >
                <CountryCurrencyIcon
                  :currency="buyCurrency"
                  class="mr-16rpx flex items-center"
                />
                <text class="number5 mr-16rpx">
                  {{ buyCurrency }}
                </text>
                <p-icon
                  v-if="!studentCode"
                  name="arrow-down"
                />
              </div>
            </template>
            <template #prefix-SELL>
              <div class="flex items-center">
                <CountryCurrencyIcon
                  :currency="sellCurrency"
                  class="mr-16rpx"
                />
                <text class="number5">
                  {{ sellCurrency }}
                </text>
              </div>
            </template>
          </AmountInputGroup>
        </div>
        <TotalAmount
          v-if="isAmtInited"
          :is-better-rate="isBetterRate"
          :rate-disp="rateDisp"
          :is-better-fee="isBetterFee"
          :origin-fee-disp="isBetterFee?`${originFeeDisp} ${sellCurrency}`:''"
          :fee-disp="`${feeDisp} ${sellCurrency}`"
          :pay-amount-disp="isQuerying?'':`${payAmountDisp}`"
          :sell-amount="isQuerying?'':amtInputBox[TransType.SELL]||''"
          :school_country_code="schoolCountryCode"
          @click="handleTotalAmountClick"
        />
      </div>

      <template #footer>
        <div class="pt-32rpx pb-32rpx flex justify-center">
          <p-button
            type="primary"
            size="large"
            class="text4-sem"
            :custom-style="{
              width: '432rpx',
              height: '88rpx',
            }"
            :color="(nextBtnDisable) ? '#BBF0D0' : ''"
            @click="handleNextClick"
          >
            下一步
          </p-button>
        </div>
      </template>
      <biz-select-popup
        :show="isShowSelectCurrency"
        :current-currency="currentCurrency"
        :currency-list="currencyList"
        @change-currency="onCurrencyChange"
        @close="onClosePop"
      />
    </f-page>
  </view>
</template>

<script setup lang="ts">
import PrivacyDialog from '@/components/privacy-dialog/index.vue';
import CountryCurrencyIcon from '@/components/common/country-currency-icon.vue';
import TotalAmount from './components/total-amount.vue';

import { Composition } from '@tencent/ppd-uni-component/packages/amount-input-group-v1';
import AmountInputGroup from '@tencent/ppd-uni-component/packages/amount-input-group-v1/index.vue';
import { TransType } from '@tencent/ppd-uni-component/packages/amount-input-group/types';
import { useLivingAmount } from '@/domain/order/living/use-amount';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { commonModal, hideLoading, showLoading } from '@/adapters/uni-api-common';
import { debounceFn } from '@/adapters/functions';
import { TransDirection } from '@/api/order/living/living-amount';
import { useOrderReqProcess } from '@/domain/order/living/order-req-process';
import { LivingAmountInputOptions } from '@/route/living/amount-input';
import { getOptions } from '@/route/base';
import RealNameProtocal from '@/components/real-name-protocal/index.vue';
import { useProtocal } from '@/domain/common/protocal';
import { useLivingAccount } from '@/domain/account/living-account/use-living-account';
import BizSelectPopup from './components/biz-select-popup/index.vue';
import { getCurrencyData } from '@/domain/common/calculate/currency';
import { NavigateType } from '@/adapters/route';
import { navigateToSchoolSearch } from '@/route/school-search';
import { biClick } from '@tencent/fit-bi-sdk';
import { useTuitionAccount } from '@/domain/account/tuition-account/use-tuition-account';
import { yuanToYuan } from '@/adapters/currency';
import { useArchiveData } from '@/domain/account/archive/use-archive-data';
import { useOrderReq } from '@/domain/order/living/order-req';
import { navigateToHome } from '@/route/home';
/**
 * 临时下线生活费增加一个重定向首页的逻辑
 */
navigateToHome(NavigateType.reLaunch);

const { getAccountByStudentCode } = useLivingAccount();

const { checkIsLivingVerified } = useArchiveData();

// 档案编号
const studentCode = ref('');
// 账户编号
const accountCode = ref('');
// 用户协议
const { isSigned, signProtocal } = useProtocal();

const { isTuitionNewUser } = useTuitionAccount();

/**
 * 处理首页点击事件
 * 处理客服按钮点击埋点
 */
function handlePageClick(e) {
  if (e?.type === 'customerService') {
    biClick('living_amt.service_click', {
      schoolCountryCode,
      isNewUser: isTuitionNewUser.value ? 1 : 2,
    });
  }
}

const { payAmountDisp,
  rateDisp,
  isBetterRate,
  feeDisp,
  originFeeDisp,
  isBetterFee,
  quota,
  quotaDisp,
  updateAmount,
  isAmtInited,
  minAmount,
  isQuerying,
  buyPointNum,
  sellPointNum,
} = useLivingAmount(studentCode);


/**
 * 编辑换汇方向
 */
const direction = ref(TransDirection.BUY);
/**
 * 卖出币种
 */
const sellCurrency = ref('CNY');
/**
 * 买入币种
 */
const buyCurrency = ref('');
/**
 * 是否禁用【下一步】按钮
 */
const nextBtnDisable = computed(() => {
  /**
   * 接口调用中或异常时，禁用操作
   */
  if (isQuerying.value) return true;

  /**
   * 超出最大值，禁用操作
   */
  if (checkMaxAmount(+amtInputBox.value[TransType.SELL])) {
    return true;
  }

  /**
   * 小于最小值，禁用操作
   */
  if (checkMinAmount(+amtInputBox.value[TransType.SELL])) {
    return true;
  }

  /**
   * 没有填写金额，禁用操作
   */
  if (!+amtInputBox.value[TransType.SELL] || !+amtInputBox.value[TransType.BUY]) {
    return true;
  }
  return false;
});

/**
 * 默认买入金额大小
 */
const DefaultBuyAmount = '5000';
/**
 * 缓存 placeholder
 */
let cachePlaceholder = {
  [TransType.BUY]: '0',
  [TransType.SELL]: '0',
};

/**
 * 币种选择器币种列表
 */
const currencyList = ref([] as any);
/**
 * 币种选择器当前选择币种
 */
const currentCurrency = ref('');
/**
 * 记录学生币种
 */
let studentCurrency = '';

/**
 * 学校地区码
 */
let schoolCountryCode = '';

const {
  amtInputBox,
  placeholderBox,
  focusPlaceholderBox,
  errBox,
  titleBox,
  setAmtInputBox,
  setPlaceholderBox,
  // setFocusPlaceholderBox,
  digitBox,
  setErrBox,
  clearErrBox,
  onBlur,
  onFocus,
  onChange,
} = Composition.useAmtInputGroup({
  defaultPlaceholder: cachePlaceholder,
  defaultDigit: {
    [TransType.BUY]: 0,
    [TransType.SELL]: +sellPointNum.value,
  },
  handleFocus: (type) => {
    if (type === TransType.BUY) {
      biClick('living_amt.forcurrency_click', {
        schoolCountryCode,
      });
    } else {
      biClick('living_amt.cny_click', {
        schoolCountryCode,
      });
    }
    const isDirectionChange = type !== getInputBoxDirection(direction.value);
    // 如何输入方向没有变更，则不需要重置输入框
    if (!isDirectionChange) return;

    // 输入方向发生变更，重置输入框
    direction.value = getTransDirection(type);
    initInputBox();
  },
  handleBuyChange: debounceFn((value: string) => {
    (async () => {
      await setAmount({
        sellCurrency: sellCurrency.value,
        buyCurrency: buyCurrency.value,
        amount: value,
        direction: direction.value,
      });
    })();
  }, 200),
  handleSellChange: debounceFn((value: string) => {
    (async () => {
      await setAmount({
        sellCurrency: sellCurrency.value,
        buyCurrency: buyCurrency.value,
        amount: value,
        direction: direction.value,
      });
    })();
  }, 200),
});

/**
 * 监听精度变化，修改对应字段
 */
watch([buyPointNum, sellPointNum], ([buy, sell]) => {
  digitBox.value[TransType.BUY] = +buy;
  digitBox.value[TransType.SELL] = +sell;
});

/**
 * 当 direction 发生变更时，修改输入框标题文本
 */
watch(direction, (v) => {
  titleBox.value[TransType.SELL] = `${v === TransDirection.SELL ? '' : '预计'}我汇出的金额`;
  titleBox.value[TransType.BUY] = `${v === TransDirection.BUY ? '' : '预计'}学生收到的金额`;
}, { immediate: true });

/**
 * 异常监听设置异常信息，根据卖出金额、最大金额、最小金额
 */
watch([() => amtInputBox.value[TransType.SELL], quotaDisp], ([v]) => {
  if (v === '') {
    clearErrBox();
    return;
  }
  // todo，将最大金额和最小金额的判断逻辑汇总到一个函数里集中处理
  if (+v > +quota.value) {
    const errValue = studentCode.value
      ? `当前汇出金额超出可汇余额 ¥${quotaDisp.value}`
      : '最高可享受每年150,000 CNY的汇款限额';
    setErrBox({ type: TransType.BUY, value: errValue });
  } else if (+v < +minAmount.value) {
    setErrBox({ type: TransType.BUY, value: `当前汇出金额没有达到最小金额 ¥${minAmount.value}` });
  } else {
    clearErrBox();
  }
});

/**
 * 加载时，设置 placeholder
 */
watch(isQuerying, (v) => {
  if (v) {
    const reverseInputDirection = getReverseInputBoxDirection(direction.value);
    setPlaceholderBox({ type: reverseInputDirection, value: '' });
  }
});

/**
 * 获取页面参数
 * 设置当前学生买入币种
 * 设置初始化 placeholder
 */
onLoad((options: DefineOnloadOptions<LivingAmountInputOptions>) => {
  (async () => {
    // 获取页面参数
    const typedOptions = getOptions(options);
    studentCode.value = typedOptions.studentCode || '';
    accountCode.value = typedOptions.accountCode || '';
    buyCurrency.value = typedOptions.buyCurrency || 'USD';
    // 每次初始化都清空表单
    useOrderReq().initOrderPrams();
    // 第一次进来需要弹窗了
    watch(isSigned, () => {
      if (isSigned.value) {
        checkStudentCode(studentCode.value);
      }
    }, {
      immediate: true,
    });

    setPlaceholderBox({
      type: TransType.BUY,
      value: yuanToYuan({
        amount: DefaultBuyAmount,
        pointNum: +(options.buyCurrencyPointNum ?? 2),
        thousandsSep: true,
      }),
    });
    // 有值才会进行获取account
    if (studentCode.value) {
      // 设置买入币种
      const res = await getAccountByStudentCode({
        studentCode: studentCode.value,
        accountCode: accountCode.value,
      });
      studentCurrency = res?.student_recv_currency || '';
      schoolCountryCode = res?.chief_school_country_code || '';
    } else {
      studentCurrency = '';
      schoolCountryCode = '';
    }

    initPlaceholder();

    // 币种列表过滤掉 CNY
    currencyList.value = (await getCurrencyData()).filter(item => item.en !== 'CNY');
  })();
});

/**
 * 重新进入时更新汇率
 */
let isFirstShow = true;
onShow(() => {
  (async () => {
    if (isFirstShow) {
      isFirstShow = false;
      return;
    }

    showLoading();
    if (amtInputBox.value[TransType.BUY] === '') {
      await initPlaceholder();
    } else {
      const amount = amtInputBox.value[getInputBoxDirection(direction.value)];
      await setAmount({
        sellCurrency: sellCurrency.value,
        buyCurrency: buyCurrency.value,
        amount,
        direction: direction.value,
      });
    }
    hideLoading();
  })();
});

/**
 * 处理币种选择器
 */
const isShowSelectCurrency = ref(false);
function handleCurrencyClick() {
  // 有学生账号的话，则不展示币种选择器
  if (studentCode.value) return;
  isShowSelectCurrency.value = true;
  currentCurrency.value = buyCurrency.value;
}
async function onCurrencyChange(e) {
  isShowSelectCurrency.value = false;
  if (studentCurrency && studentCurrency !== e) {
    commonModal({
      content: '生活费汇款功能限量开放中，目前仅支持使用最近一次缴费币种汇款',
      showCancel: false,
      confirmColor: '#0CBD6A',
    });
    return;
  }
  buyCurrency.value = e;
  await setAmount({
    sellCurrency: sellCurrency.value,
    buyCurrency: buyCurrency.value,
    amount: amtInputBox.value[getInputBoxDirection(direction.value)],
    direction: direction.value,
  });
}
function onClosePop() {
  isShowSelectCurrency.value = false;
  currentCurrency.value = '';
}

/**
 * 初始化金额输入框 placeholder
 */
async function initPlaceholder() {
  const res = await updateAmount({
    sellCurrency: sellCurrency.value,
    buyCurrency: buyCurrency.value,
    amount: DefaultBuyAmount, // 初始默认值
    direction: TransDirection.BUY,
  });
  if (!res) return;

  const { buyAmount, sellAmount } = res;
  setPlaceholderBox({ type: TransType.SELL, value: sellAmount });
  setPlaceholderBox({
    type: TransType.BUY,
    value: yuanToYuan({
      amount: buyAmount,
      pointNum: +buyPointNum.value,
      thousandsSep: true,
    }),
  });
  cachePlaceholder = { ...placeholderBox.value };

  // [无卖出金额版本]临时代码
  amtInputBox.value[TransType.SELL] = res.sellAmount;
}

/**
 * 重置输入框，包括输入内容和占位内容
 */
function initInputBox() {
  setAmtInputBox({ type: TransType.SELL, value: '' });
  setAmtInputBox({ type: TransType.BUY, value: '' });
}

/**
 * 设置输入金额
 */
async function setAmount({
  sellCurrency, buyCurrency, amount, direction,
}: {
  sellCurrency: string,
  buyCurrency: string,
  amount: string,
  direction: TransDirection
}) {
  /** 当清空输入内容时，初始化 inputBox 和 placeholder */
  if (amount === '') {
    updateAmount({
      sellCurrency,
      buyCurrency,
      amount,
      direction,
    });
    initInputBox();
    initPlaceholder();
    return;
  }

  /** 输入时，清空另一个方向的内容 */
  const reverseInputDirection = getReverseInputBoxDirection(direction);
  setAmtInputBox({ type: reverseInputDirection, value: '' });

  const res = await updateAmount({
    sellCurrency,
    buyCurrency,
    amount,
    direction,
  });

  if (!res) return;
  const { buyAmount, sellAmount } = res;

  /** 输入接口响应后，设置另一个方向的内容 */
  if (direction === TransDirection.BUY) {
    // 判断当前页面上的金额与输入金额是否一致，避免数据时序混乱
    if (+buyAmount !== +amtInputBox.value[TransType.BUY]) {
      return;
    }
    setAmtInputBox({ type: TransType.SELL, value: sellAmount });
  } else {
    // 判断当前页面上的金额与输入金额是否一致，避免数据时序混乱
    if (+sellAmount !== +amtInputBox.value[TransType.SELL]) {
      return;
    }
    setAmtInputBox({ type: TransType.BUY, value: buyAmount });
  }

  // 还原占位内容
  placeholderBox.value = { ...cachePlaceholder };
}

const { resolveAmountInput } = useOrderReqProcess();
/**
 * @description 点击下一步
 */
const handleNextClick = async () => {
  biClick('living_amt.next_click', { schoolCountryCode, isNewUser: isTuitionNewUser.value ? 1 : 2 });

  if (!await checkStudentCode(studentCode.value) || nextBtnDisable.value) {
    return;
  }

  resolveAmountInput({
    studentCode: studentCode.value,
    accountCode: accountCode.value,
    amount: amtInputBox.value[getInputBoxDirection(direction.value)] || '',
    sell_currency: sellCurrency.value,
    buy_currency: buyCurrency.value,
    trans_direction: direction.value,
  });
};


/**
 * 处理 tooltip 点击事件
 */
function handleTotalAmountClick({ type }) {
  if (type === 'tooltip') {
    biClick('living_amt.fee_click', {
      schoolCountryCode,
      isNewUser: isTuitionNewUser.value ? 1 : 2,
    });
  }
}

/**
 * 检查当前页面是否带有学生 code 参数
 */
async function checkStudentCode(studentCode: string) {
  if (!await checkIsLivingVerified(studentCode)) {
    commonModal({
      content: '生活费汇款功能限量开放中，您只需成功完成一笔学费汇款，即可体验留学生活费线上汇款！',
      showCancel: true,
      confirmText: '去缴学费',
      confirmColor: '#0CBD6A',
      success: (result) => {
        // 用户点击确认
        if (result.confirm) {
          // 这里回到首页从体验上直接后退更合适
          // navigateBack();
          // 需求变更，这里改成直接跳转到学校搜索页
          navigateToSchoolSearch(NavigateType.redirectTo);
        }
      },
    });
    return false;
  }
  return true;
}


// 转换 TransDirection 与 TransType，将 TransDirection 转换成 TransType
function getInputBoxDirection(direction: TransDirection) {
  return direction === TransDirection.BUY ? TransType.BUY : TransType.SELL;
}

// 转换 TransDirection 与 TransType，将 TransDirection 转换成 TransType，并转换买卖方向
function getReverseInputBoxDirection(direction: TransDirection) {
  return direction === TransDirection.BUY ? TransType.SELL : TransType.BUY;
}

// 转换 TransDirection 与 TransType，将 TransType 转换成 TransDirection
function getTransDirection(direction: TransType) {
  return direction === TransType.BUY ? TransDirection.BUY : TransDirection.SELL;
}

/**
 * 最大可汇金额判断
 */
function checkMaxAmount(amount: number) {
  return amount > +quota.value;
}

/**
 * 最小汇款金额判断
 */
function checkMinAmount(amount: number) {
  return amount < +minAmount.value;
}
</script>
