<template>
  <img
    :src="icon"
    alt="country icon"
    :class="`w-${size} h-${size} border-rd-4px`"
    style="background-color:#D8D8D8"
  >
</template>

<script setup lang="ts">
import { useCountryCurrencyStore } from '@/store/modules/country-currency';

const props = withDefaults(defineProps<{
  country?: string,
  currency?: string,
  size?: string,
}>(), {
  country: '',
  currency: '',
  size: '56rpx',
});

const { getIconByCountry, getIconByCurrency } = useCountryCurrencyStore();
const icon = ref('');

watch(props, async (v) => {
  if (v.country) {
    icon.value = await getIconByCountry(v.country) || '';
    return;
  }
  if (v.currency) {
    icon.value = await getIconByCurrency(v.currency) || '';
    return;
  }
}, { immediate: true });

</script>
