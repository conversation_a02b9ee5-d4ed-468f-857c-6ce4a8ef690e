<template>
  <div class="flex items-center">
    <p-tag
      v-if="isBetterRate"
      text="优于银行"
      :border-color="color"
      :color="color"
      plain
      class="mr-8rpx"
    />
    <div class="number6 color-text-title">
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { IsBetterRate } from '@/api/order/living/living-amount';
import { useRate } from '@/domain/common/rate/use-rate';

const color = '#FFA338';
const { formatRateText, checkIsBetterRate } = useRate();

const { buyCurrency, sellCurrency, rate, isBetter } = toRefs(withDefaults(defineProps<{
  buyCurrency: string,
  sellCurrency: string,
  rate: string,
  isBetter: IsBetterRate
}>(), {
  buyCurrency: '',
  sellCurrency: '',
  rate: '',
  isBetter: IsBetterRate.NOT_BETTER,
}));
const text = computed(() => formatRateText({
  buyCurrency: buyCurrency.value,
  sellCurrency: sellCurrency.value,
  rate: rate.value,
}));
const isBetterRate = computed(() => checkIsBetterRate(isBetter.value));
</script>
