import reportInstance, { CustomException } from '@/adapters/report';
import { to } from '@/adapters';

export const useDraw = (componentInstance, emit) => {
  const canvasId = 'drawBoard';
  let ctx = uni.createCanvasContext(canvasId, componentInstance);
  let canvas: WechatMiniprogram.IAnyObject | null = null;
  let lastPoint = { x: 0, y: 0 };
  let isPainting = false;
  const canvasSize = {
    width: 0,
    height: 0,
  };
  initCanvas();

  function initCanvas() {
    // uni.createSelectorQuery.fields不支持查询node节点，但在小程序内可以支持，所以直接端言类型
    // 若在h5中使用，需再做h5兼容处理
    const query = uni.createSelectorQuery().in(componentInstance) as WechatMiniprogram.SelectorQuery;
    query.select('#drawBoard').fields({ node: true, size: true }, (res) => {
      if (!res.node) {
        new CustomException(null, 'canvas_node_null', '画板node获取为空');
        return;
      }
      canvas = res.node as unknown as WechatMiniprogram.IAnyObject;
      ctx = canvas.getContext('2d');
      canvasSize.width = res.width;
      canvasSize.height = res.height;
      const dpr = uni.getSystemInfoSync().pixelRatio;
      canvas.width = res.width * dpr;
      canvas.height = res.height * dpr;
      ctx.scale(dpr, dpr);
    })
      .exec();
  }

  // 初始化画板
  function touchStart(e) {
    isPainting = true;
    const { x, y } = e.touches[0];
    lastPoint = { x, y };
    ctx.save();
    ctx.beginPath();
  }
  function touchMove(e) {
    if (isPainting) {
      const { x, y } = e.touches[0];
      const newPoint = { x, y };
      drawLine(lastPoint.x, lastPoint.y, newPoint.x, newPoint.y);
      lastPoint = newPoint;
      emit('sign');
    }
  }
  function drawLine(x1, y1, x2, y2) {
    ctx.lineWidth = 4;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.strokeStyle = '#000000';
    ctx.moveTo(Number(x1), Number(y1));
    ctx.lineTo(Number(x2), Number(y2));
    ctx.stroke();
    ctx.closePath();
  }

  function touchEnd() {
    isPainting = false;
  }

  function onError(e) {
    new CustomException(e, 'canvas_err', 'canvas组件报错');
  }

  function onFlush() {
    ctx.clearRect(0, 0, canvasSize.width, canvasSize.height);
    ctx.restore();
    emit('clear');
  }

  function saveToFilePath(): Promise<string> {
    return new Promise((resolve, reject) => {
      uni.canvasToTempFilePath({
        // uni.canvasToTempFilePath不支持在type 2d下的canvas参数，但小程序api支持，这里先ignore uni的eslint问题
        // @ts-ignore
        canvas: canvas as WechatMiniprogram.IAnyObject,
        fileType: 'png',
        quality: 1, // 图片质量
        success(res) {
          resolve(res.tempFilePath);
        },
        fail(e) {
          reject(e);
        },
      }, componentInstance);
    });
  }

  async function saveToBase64(filePath = ''): Promise<{base64: string} | undefined> {
    try {
      const data = (canvas as WechatMiniprogram.IAnyObject).toDataURL('image/png', 1);
      const base64data = data.replace('data:image/png;base64,', '');
      return {
        base64: base64data,
      };
    } catch (error) {
      let fileTempPath = filePath;
      reportInstance.reportInfo({ key: 'canvas_toDataURL_fail', msg: error });
      if (!filePath) {
        const [err, res] = await to(saveToFilePath());
        if (err) {
          new CustomException(err, 'saveToFilePath_err', 'canvas保存到本地图片失败');
          return;
        }
        fileTempPath = res;
      }
      return new Promise((resolve, reject) => {
        uni.getFileSystemManager().readFile({
          filePath: fileTempPath,
          encoding: 'base64',
          success(data) {
            resolve({
              base64: data.data as string,
            });
          },
          fail(e) {
            reject(e);
          },
        });
      });
    }
  }

  async function saveToFileAndBase64() {
    const [err, filePath] = await to(saveToFilePath());
    if (err) {
      new CustomException(err, 'saveToFilePath_err', 'canvas保存到本地图片失败');
      return;
    }
    const [base64Err, base64data] = await to(saveToBase64(filePath));
    if (base64Err || !base64data) {
      new CustomException(err, 'saveToBase64_err', 'canvas转base64失败');
      return;
    }
    return {
      filePath,
      base64: base64data.base64,
    };
  }

  return {
    saveToFilePath,
    saveToFileAndBase64,
    saveToBase64,
    touchStart,
    touchMove,
    touchEnd,
    onError,
    onFlush,
  };
};
