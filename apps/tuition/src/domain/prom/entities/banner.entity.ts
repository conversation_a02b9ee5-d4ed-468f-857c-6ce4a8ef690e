import { BannerEntity as BaseBannerEntity } from '@tencent/ppd-uni-domain/src/prom/dp';

enum AbleStatus {
  orderClose = '10', // 订单关闭
  orderExamine = '30', // 订单审核
  bankProcessingProgress = '40', // 银行处理中
  remittanceTuitionFees = '50', // 学费汇出
  paymentFailedRefundProgress = '60-70', // 缴费失败，退款中
  paymentFailedRefunded = '80', // 缴费失败，已退款
}

enum BannerType {
  basicsBanner = '1', // 基础banner
  orderBanner = '2', // 订单banner（根据订单状态展示）
}
export interface BannerCompItem {
  ableStatus?: AbleStatus[]; // 生效订单状态
  bannerType: BannerType; // banner类型
  name: string;
  image: string;
  url: string;
}

export interface BannerExtInfo {
  orderState: string; // 生效订单状态
}

export class BannerEntity extends BaseBannerEntity<BannerCompItem> {
  /**
   * 是否将同一位置同一类型组件数据聚合
   * banner类型需要合并数据
   */
  static isMerge = true;
  /**
   * 组件类型
   */
  public compType = 'base_banner';

  /**
   * 清洗后是否生效
   * @param extInfo
   * @returns
   */
  protected initData() {
    const { orderState } = this.extInfo;
    const filterIndex: number[] = []; // 符合条件的索引
    this.originData.forEach((item, i: number) => {
      const { contentBaseData } = item;
      const isBasicsBanner = contentBaseData?.bannerType === BannerType.basicsBanner;
      // eslint-disable-next-line max-len
      const isOrderBanner = contentBaseData?.bannerType === BannerType.orderBanner && contentBaseData?.ableStatus?.includes(orderState);
      if (isBasicsBanner || isOrderBanner) {
        filterIndex.push(i);
      }
    });
    this.filteredData = filterIndex.map((index: number) => this.originData[index]);
  }
}
