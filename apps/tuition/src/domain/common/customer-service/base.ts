import { ContextInstance } from './interface';
/**
 * 客服基类，用来
 *
 * @export
 * @class Customer
 */
export class Customer {
  /**
   * 实例的data数据
   *
   * @protected
   * @type {Record < string, any >}
   * @memberof Customer
   */
  protected data: Record < string, any > = {};
  /**
   * 内部存储的setData方法
   *
   * @protected
   * @type {Setfunc}
   * @memberof Customer
   */
  protected context: ContextInstance;
  /**
   * 页面触发的事件钩子函数集合
   *
   * @protected
   * @memberof Customer
   */
  protected handler: Record<string, (data?: string) => void> = {};
  /**
   *Creates an instance of Customer.
   * @param {Setfunc} setData 一般是小程序的setData方法，通过这种方式来驱动页面
   * @memberof Customer
   */
  constructor(context: ContextInstance) {
    this.context = context;
    this.setData({
      type: 'default',
    });
  }
  /**
   * 设置页面数据
   *
   * @type {Setfunc}
   * @memberof Customer
   */
  setData(data: Record<string, any>): void {
    this.data = Object.assign({}, this.data, data);
    this.context.setData({ data: this.data });
  }
  /**
   * 点击客服按钮的时候的触发方法
   *
   * @param {WechatMiniprogram.BaseEvent} e
   * @memberof Customer
   */
  trigger(e: WechatMiniprogram.BaseEvent): void {
    console.log(e);
  }
  /**
   * 直接展开客服信息，不经过点击事件
   */
  display(): void {
    console.log('Customer Service Display');
  }
  /**
   * 判断实例钩子函数中是否有对应执行方法并执行
   *
   * @param {string} e
   * @param {string} data
   * @memberof Customer
   */
  onEvent(e: string, data: string) {
    if (this.handler[e]) {
      this.handler[e](data);
    }
  }
}
