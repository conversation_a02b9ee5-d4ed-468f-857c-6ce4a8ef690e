/**
 * useStorage函数使用的缓存key
 */
export enum UseStorageKeys {
  /**
   * 未支付订单持久化key
   */
  WAIT_PAY_LIST_ID = 'WAIT_PAY_LIST_ID',
  /**
   * 转账指引显示次数缓存key
   */
  BIG_PAY_TRANSFER_GUIDE_SHOW_COUNT = 'BIG_PAY_TRANSFER_GUIDE_SHOW_COUNT',
}

/**
 * pinia持久化使用缓存key
 */
export enum PersistedStateKey {
  /**
   * 登陆信息的持久化key
   */
  LOGIN_DATA = 'LOGIN_DATA',
  /**
   * 未支付订单持久化key
   */
  WAIT_PAY_LIST_ID = 'WAIT_PAY_LIST_ID',
}


/**
 * 全部缓存key的类型
 */
export enum GlobalStorageKeys {
  NAME = 'NAME',
  TIME_DIFF = 'time_diff', // 服务端时间差
}

/**
 * 全部缓存key的类型
 */
export enum NativeStorageKeys {
  TUITION_CPS_ACT_202407 = 'TUITION_CPS_ACT_202407', // 留学2024.07 cps活动
}
