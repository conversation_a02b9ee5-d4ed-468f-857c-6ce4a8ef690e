import { useUserStore } from '@/store/modules/user';
import {
  Request,
  BaseException,
  mergeConfig,
} from '@tencent/ppd-common/src/request-core';
import {
  UniDownloadFileAdaptor,
  UniDownloadFileConfig,
  UniDownloadFileRequestOptions,
  UniDownloadFileResponse,
} from '@tencent/ppd-uni-common/src/request/adaptors';
import {
  DEFAULT_REQUEST_CONFIG,
} from '../common';

/**
 * 下载请求响应错误封装
 */
export class DownloadFileResponseExceptions extends BaseException<UniDownloadFileResponse<string>> {
  name = 'DownloadFileResponseExceptions';
}

/**
 * 下载文件请求实例
 */
const uniDownloadFileInstance = new Request(new UniDownloadFileAdaptor());

uniDownloadFileInstance.setDefaultConfig(DEFAULT_REQUEST_CONFIG);

/**
 * 下载请求拦截器
 */
uniDownloadFileInstance.interceptors.request.use((request) => {
  // 使用user的store
  const { getLoginData } = useUserStore();
  const loginData = getLoginData();
  const header: UniDownloadFileConfig['header'] = {};
  // 非登陆接口写入登陆凭证
  if (loginData?.qlskey) {
    header.qlskey = loginData.qlskey;
  }
  console.log('download:::config', request);
  console.log('download:::loginData', loginData);
  return mergeConfig(request, {
    config: {
      header,
    },
  });
});

/**
 * 下载响应拦截器
 */
uniDownloadFileInstance.interceptors.response.use((response) => {
  const wxDownloadFileResponse = response.rawResponse as WechatMiniprogram.DownloadFileSuccessCallbackResult & {
    header?: AnyObject;
  };
    // 某些机型内 content-type是数组。。。
  const contentType = String(wxDownloadFileResponse?.header?.['Content-Type']
      ?? wxDownloadFileResponse?.header?.['content-type']
      ?? '');
  if (
    wxDownloadFileResponse.errMsg === 'downloadFile:ok'
      && contentType
      // 仅支持 image 和 pdf文件下载
      && (contentType.indexOf('image') > -1 || contentType.indexOf('pdf') > -1)
  ) {
    return response;
  }
  return Promise.reject(new DownloadFileResponseExceptions('下载失败', response));
});

/**
 * 基础下载封装
 * @param options 请求参数
 * @returns
 */
// eslint-disable-next-line max-len
export const download = (options: UniDownloadFileRequestOptions) => uniDownloadFileInstance.request(options).then(response => response.data);

