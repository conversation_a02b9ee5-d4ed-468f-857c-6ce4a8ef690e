import { useUserStore } from '@/store/modules/user';
import { mergeConfig } from '@tencent/ppd-common/src/request-core';
import { UniRequestConfig } from '@tencent/ppd-uni-common/src/request/adaptors';
import { ReLoginTransformRequestOptions } from './reLogin';

/**
 * 业务登录注入处理
 * @param request
 * @returns
 */
export const busLoginRequest = (request: ReLoginTransformRequestOptions<UniRequestConfig>) => {
  // 使用user的store
  const { getLoginData } = useUserStore();
  const loginData = getLoginData();
  const header: UniRequestConfig['header'] = {};
  // 登陆接口写入登陆凭证
  if (request.isNeedLogin) {
    header.qlskey = loginData?.qlskey || '';
  }
  console.log('request:::config', request);
  console.log('request:::loginData', loginData,  mergeConfig(request, {
    config: {
      header,
    },
  }));
  return mergeConfig(request, {
    config: {
      header,
    },
  });
};
