// 微信原生拉起授权订阅消息
import { requestSubscribeMessage as rawRequestSubscribeMessage, checkIsAllTemplatesAccepted, checkIsAllTemplatesRejected } from '@tencent/ppd-common/src/wx-common-api';
import { biClick } from '@tencent/fit-bi-sdk';

/**
 * @description 上报订阅消息状态
 * @return {*}
 */
export enum BiSubscribeStatus {
  // 全接受
  ALL_ACCEPT = '1',
  // 全拒绝
  ALL_REJECT = '2',
  // 部分接受
  PART_ACCEPT = '3',
}

/**
 * @description 上报新老用户状态
 * @return {*}
 */
export enum BiIsNewUserTag {
  // 新用户
  NEW_USER = '1',
  // 老用户
  OLD_USER = '2',
}

/**
 * @description BI上报拓展字段，为保证通用型，均为非必填
 */
export type RequestSubscribeMessageExtraBi = {
  isNewUser?: BiIsNewUserTag
};

/**
 * 订阅消息（包含上报逻辑）
 * 上报用户授权状态参考文档：https://developers.weixin.qq.com/miniprogram/dev/api/open-api/subscribe-message/wx.requestSubscribeMessage.html
 *
 * 调用地方：大额转卡页下一步，活动落地页下一步
 * @param tempIds
 * @returns
 */
// eslint-disable-next-line max-len
export function requestSubscribeMessage<TemplateIds extends Array<string>>(tempIds: TemplateIds, extraBiInfo?: RequestSubscribeMessageExtraBi): Promise<WechatMiniprogram.RequestSubscribeMessageSuccessCallbackResult> {
  return rawRequestSubscribeMessage(tempIds).then((res) => {
    // 授权状态
    const biSubscribeStatus = getBiSubscribeStatus(tempIds, res);
    // 扩展字段
    const extraInfo = Object.assign({}, {
      subscribeStatus: getBiSubscribeStatus(tempIds, res),
    }, extraBiInfo);
    switch (biSubscribeStatus) {
      // 拒绝授权
      case BiSubscribeStatus.ALL_REJECT:
        biClick('messagesubscribe_popup.cancel_button', extraInfo);
        break;
      // 同意授权
      case BiSubscribeStatus.ALL_ACCEPT:
      case BiSubscribeStatus.PART_ACCEPT:
        biClick('messagesubscribe_popup.allow_button', extraInfo);
        break;
    }
    return res;
  })
    .catch(err =>
      // 这里上报有问题，用户拒绝也不会走到catch里
      // biClick('messagesubscribe_popup.cancel_button');
      Promise.reject(err));
}

/**
 * @description 获取bi上报状态
 * @return {*}
 */
const getBiSubscribeStatus = (
  // 模版id
  tempIds: Array<string>,
  // 成功回调返回
  res: WechatMiniprogram.RequestSubscribeMessageSuccessCallbackResult,
): BiSubscribeStatus => {
  let biSubscribeStatus;
  if (checkIsAllTemplatesAccepted(tempIds, res)) {
    // 全接受
    biSubscribeStatus = BiSubscribeStatus.ALL_ACCEPT;
  } else if (checkIsAllTemplatesRejected(tempIds, res)) {
    // 全拒绝
    biSubscribeStatus = BiSubscribeStatus.ALL_REJECT;
  } else {
    // 部分接受
    biSubscribeStatus = BiSubscribeStatus.PART_ACCEPT;
  }
  return biSubscribeStatus;
};
