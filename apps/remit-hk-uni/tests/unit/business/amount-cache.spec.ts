import agreeCache from '@/business/amount-cache';
import { TransType } from '@/types';

const { setCache, getCache, clearCache } = agreeCache;
const amountInputBox = {
  [TransType.BUY_IN]: '1',
  [TransType.SELL_OUT]: '2',
  transType: TransType.BUY_IN,
};

describe('business/amount-cache.ts', () => {
  it('setCache/getCache/clearCache - 设置金额缓存', () => {
    expect(getCache()).toBeNull();
    setCache(amountInputBox);
    expect(JSON.stringify(getCache())).toBe(JSON.stringify(amountInputBox));
    clearCache();
    expect(getCache()).toBeNull();
  });
});
