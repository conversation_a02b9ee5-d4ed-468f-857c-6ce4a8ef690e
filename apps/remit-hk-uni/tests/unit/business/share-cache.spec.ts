import shareCache from '@/business/share-cache';

const { setCache, getCache, clearCache } = shareCache;

const testCases = [
  { listid: '1', value: true },
  { listid: '2', value: true },
  { listid: '3', value: false },
];

describe('business/.ts', () => {
  it('setCache/getCache/clearCache - 修改缓存 ', () => {
    testCases.forEach((test) => {
      expect(getCache(test.listid)).toBeFalsy();
    });

    testCases.forEach((test) => {
      setCache(test.listid, test.value);
    });

    testCases.forEach((test) => {
      expect(getCache(test.listid)).toBe(test.value);
    });

    testCases.forEach((test) => {
      clearCache(test.listid);
    });

    testCases.forEach((test) => {
      expect(getCache(test.listid)).toBeFalsy();
    });
  });
});
