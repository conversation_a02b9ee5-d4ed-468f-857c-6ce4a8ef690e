import { getFollowAccountConf, getDefaultFollowAccountImg } from '@/business/follow-account-abt';
import { abtLangKeyConf } from '@/business/abtest';
import { Lang } from '../utils';
import { language } from '@tencent/ppd-base-libs';

const $$abtestConf = {
  followAccount: [
    {
      programmeKey: '121306',
      zh_cn: '//st.moneydata.hk/res/fmd_mojo_static/PgsaWWMIJ1ncbGpkjsQHk8xtnpkIIvhT.png',
      zh_hk: '//st.moneydata.hk/res/fmd_mojo_static/kl96XPseBiNdSiUlCos9k6Sf7LDSqbHu.png',
      english: '//st.moneydata.hk/res/fmd_mojo_static/jvfvL3beUrBYUGfKo5r6QX8Uc75l6mBx.png',
    },
    {
      programmeKey: '121307',
      zh_cn: '//st.moneydata.hk/res/fmd_mojo_static/nliJBEP5w9gVjeS8iBvQt2V8uVVVrDqm.png',
      zh_hk: '//st.moneydata.hk/res/fmd_mojo_static/GzrkMVGabZyv4JsaDP5OYFLzU5Wxu7rW.png',
      english: '//st.moneydata.hk/res/fmd_mojo_static/5kEmeYRiceWp19S4jQYRbqxeH6Ssddx3.png',
    },
  ],
};

const initConfig = (lang: 'zh-hk' | 'zh-cn' | 'en') => {
  language.setLanguage(lang);
  // 初始化环境
  Object.defineProperty(window, '$$abtestConf', {
    value: $$abtestConf,
    writable: true,
  });
};


describe('business/follow-account-abt.ts', () => {
  it('getFollowAccountConf - 获取关注公众号配置失败', () => {
    const config = getFollowAccountConf();
    expect(Array.isArray(config)).toBeTruthy();
    expect(config.length).toBe(0);
  });
  ['zh-hk', 'zh-cn', 'en'].forEach((lang) => {
    it(`getFollowAccountConf - 获取默认关注配置-正常获取 - ${lang}`, () => {
      language.setLanguage(lang);
      const config = getDefaultFollowAccountImg();
      const targetKey = '121307';
      const key = abtLangKeyConf[lang];
      const defaultConf = $$abtestConf.followAccount.find(item => item.programmeKey === targetKey)![key];
      expect(config).toBe(defaultConf);
    });
  });
  ['zh-hk', 'zh-cn', 'en'].forEach((lang) => {
    it(`getFollowAccountConf - 获取关注公众号配置-正常获取 - ${lang}`, () => {
      initConfig(lang as Lang);
      const config = getFollowAccountConf();
      const targetKey = '121306';
      expect(config).toContainEqual({
        programmeKey: targetKey,
        imgUrl: $$abtestConf.followAccount.find(item => item.programmeKey === targetKey)![abtLangKeyConf[lang]],
      });
    });
  });
});
