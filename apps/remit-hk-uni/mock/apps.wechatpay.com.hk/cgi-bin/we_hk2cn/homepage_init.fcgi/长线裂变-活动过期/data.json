{"act_cn_simple_showname": "", "act_cn_tradition_showname": "", "act_en_showname": "", "addr_state": "0", "agree_state": "1", "audit_free_status": "101", "cdd_state": "0", "cur_cdd_flag": "0", "disc_fee": "0", "ekyc_gray_use_ekyc": "1", "ekyc_state": "2", "gender": "1", "hkwallet_openid": "o5PXlsmg9hFgRhpFeuLfNrTpWOU4", "is_age_limit": "2", "is_old_hk2cn_user": "2", "is_we_hk2cn_user": "1", "kyc_state": "0", "level_id": "0", "min_amt": "5000", "name": "", "original_rate": "0", "rate": "0", "rate_sign": "AW8BACe1NS-BrH9r0APFSOFGfpIkE0XjZjkXtmXU0rRWtSnQXU87RQ2tI5w7MGCLfMByXH_n7dtkuYhF1yFFI0E0z5XKdw8g", "retcode": "0", "retmsg": "OK", "share_act_info": {"cur_act_id": "1679369821337-32000668010002", "cur_has_recv_num": "0", "cur_has_shared_num": "0", "cur_share_act_end_time": "2023-05-31 23:59:59", "cur_share_act_start_time": "2023-05-17 00:00:00", "enc_sharer_openid": "AW8BALSgBxZGtlQZsJRuEEXg9WXshQN6mwZQU_fSMmWzWt7F-fehBA", "is_rate_card_effective": "2", "last_share_act_start_time": "2023-05-17 00:00:00", "need_shared_num": "3", "share_act_display": "1", "share_act_extend_day": "1"}, "share_remit_succ_act": {"accepter_list": null, "act_id": "1682255632093-32000668010003", "enc_sharer_openid": "AW8BACe1NS-BrH9r0APFSOFGfpIkE0XjAbXzYXCEQur6zJnDYMyjRg", "has_shared_num": "0", "rate_card_display": "0", "rate_card_remain_day": "0", "share_act_end_time": "2023-05-31 23:59:59"}, "std_kyc_state": "4", "total_coupon_amt": "0", "total_fee": "3000", "wait_pay_count": "0"}