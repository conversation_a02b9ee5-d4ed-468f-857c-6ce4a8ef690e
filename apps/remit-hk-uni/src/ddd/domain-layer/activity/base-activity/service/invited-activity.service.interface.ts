import { IActivityService } from './activity.service.interface';
import { DpMessageCommonRes } from '@/business/dp-loader/types';


/**
 * 受邀活动
 */
export interface IInvitedActivityService<
  ActConfPayload=never,
  ActInfoPayload=never,
  UserTags=never
> extends IActivityService<ActConfPayload, ActInfoPayload, UserTags>{
  /**
   * 是否是受邀用户(在人群包内的用户)
   */
  isInvitedUser: boolean;
  /**
   * 设置是否是受邀用户
   * @param dpRes 投放接口返回
   */
  setIsInvitedUser: (dpRes: DpMessageCommonRes) => void;
  /**
   * 调用投放接口，判断是否在人群报内
   * @param dpPosition 投放位置id
   */
  getDpMessage: (dpPosition?: string, dpPage?: string) => Promise<DpMessageCommonRes | undefined>;
}
