import { DpMessageCommonRes } from '@/business/dp-loader/types';
import { IActivityService } from '../../base-activity/service/activity.service.interface';
import { TotalFeeCardActivity } from '../entity/total-fee-card.entity';
import { TotalFeeCardActConfPayload, TotalFeeCardActInfoPayload, TotalFeeCardActUserTag } from '../types';

export interface ITotalFeeCardService extends IActivityService<
TotalFeeCardActConfPayload,
TotalFeeCardActInfoPayload,
TotalFeeCardActUserTag
>{
  /**
   * 活动对象
   */
  activity: TotalFeeCardActivity | null;

  /**
   * 是否是受邀用户(在人群包内的用户)
   */
  isInvitedUser: boolean;

  /**
   * 获取投放计划
   */
  setIsInvitedUser: (dpRes: DpMessageCommonRes) => void;

  /**
   * 获取投放计划
   */
  getDpMessage: (dpPosition?: string) => Promise<DpMessageCommonRes | undefined>;
}
