import { ITotalFeeCardService } from './total-fee-card.service.interface';
import { TotalFeeCardActivity, TotalFeeCardActOption } from '../entity/total-fee-card.entity';
import { ActivityService } from '../../base-activity/service/activity.service';
import { TotalFeeCardActConfPayload, TotalFeeCardActInfoPayload, TotalFeeCardActUserTag } from '../types';
import { CustomException, util } from '@tencent/ppd-base-libs';
import { queryDpMessage } from '@/business/dp-loader';
import { DpMessageCommonRes } from '@/business/dp-loader/types';
import { DP_PAGE } from '@/business/dp-loader/config';

export class TotalFeeCardService extends ActivityService<
TotalFeeCardActConfPayload,
TotalFeeCardActInfoPayload,
TotalFeeCardActUserTag
> implements ITotalFeeCardService {
  /**
   * 活动对象
   */
  activity: TotalFeeCardActivity | null = null;

  /**
   * 是否是受邀用户(在人群包内的用户)
   */
  isInvitedUser = false;

  /**
   * @param option 初始化活动对象
   */
  async setActivity(option: TotalFeeCardActOption) {
    this.activity = new TotalFeeCardActivity(option);
  }

  /**
   * 设置是否是受邀用户
   * @param dpRes 投放接口返回
   */
  setIsInvitedUser(dpRes?: DpMessageCommonRes) {
    // 没有投放计划接口返回，说明没配置投放位置，则已经全量对客
    if (!dpRes) return this.isInvitedUser = true;
    // 命中投放人群，有资格
    if (dpRes.dpList.length > 0) {
      this.isInvitedUser = true;
    }
  }

  /**
   * 调用投放接口，判断是否在人群报内
   * @param dpPosition 投放位置id
   */
  async getDpMessage(dpPosition?: string) {
    if (!dpPosition) return;
    return queryDpMessage({
      dpPage: DP_PAGE.index || '',
      dpPositions: [dpPosition],
    });
  }

  /**
   * 初始化活动数据
   */
  async init() {
    const frontActConfig = await this.getActiveFrontActConfig();
    if (!frontActConfig) return;

    // 请求活动接口
    const [err, res] = await util.to(Promise.all([
      this.activityRepository.getActInfo<TotalFeeCardActInfoPayload>(frontActConfig.actId),
      this.activityRepository.getActAwardInfo(frontActConfig.actId),
      this.activityRepository.getUserTag(frontActConfig.userTagIdList || ''),
      this.getDpMessage(frontActConfig.payload?.dpPosition || ''),
    ]));

    if (err || !res) {
      new CustomException(err, 'failToInitActivity', `活动信息初始化失败-${this.actKey}`);
      return;
    }

    // 解构放在err判断后，因为如果触发err，res将为null null无法被解构
    const [actInfo, awardList, userTagList, dpRes] = res;

    // 设置人群报资格
    this.setIsInvitedUser(dpRes);

    this.setActivity({
      frontActConfig,
      actInfo,
      awardList,
      userTagList,
    });
  };
}
