import { Activity, ActivityOption } from '../../base-activity/entity/activity.entity';
import {
  TotalFeeCardActConfPayload,
  TotalFeeCardActInfoPayload,
  TotalFeeCardActUserTag,
} from '../types';


export type TotalFeeCardActOption = ActivityOption<TotalFeeCardActConfPayload, TotalFeeCardActInfoPayload>;

export class TotalFeeCardActivity extends Activity<
TotalFeeCardActConfPayload,
TotalFeeCardActInfoPayload,
TotalFeeCardActUserTag
> {}
