@import "mixins/base";
@import "mixins/form";
.@{__var_prefix}-form {
  position: relative;
  &-title{
    font-size: @__size_font_40;
    font-weight: 500;
  }
  &-desc{
    color: @__color_grey;
    font-size: @__size_font_28;
    margin-top: 4/@__var_rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &-box{
    text-align: left;
    position: relative;
    overflow-x: hidden;
    color: @__color_black;
    font-size: @__size_font_34;
    line-height: 48/@__var_rem;
    padding: 44/@__var_rem 32/@__var_rem 18/@__var_rem 32/@__var_rem;
    background: @__color_grey_6;
    &:after{
      content: '';
      border-bottom: 1px solid @__color_grey_4;
      transform: scaleY(0.5);
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
    }
  }
  &.@{__var_prefix}-form-border{
    &:after{
      content: '';
      border-bottom: 1px solid @__color_grey_4;
      transform: scaleY(0.5);
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
    }
  }
}
