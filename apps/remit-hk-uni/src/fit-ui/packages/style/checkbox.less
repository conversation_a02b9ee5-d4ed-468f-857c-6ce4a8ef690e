@import "style/widget/weui-agree/weui-agree.less";

// 不知道为什么不生效 所以强覆盖一次
.weui-agree__checkbox{
  border: 1px solid @weuiLineColorDark!important;
  border-radius: 3px!important;;
}
.fit-check-box{
  width: 24/@__var_rem;
  display: inline-block;
  height: 24/@__var_rem;
  position: relative;
  vertical-align: middle;
  margin-top: -8/@__var_rem;
  margin-right: 10/@__var_rem;
  .weui-cells_checkbox{
    width: 0;
    height: 0;
    opacity: 0;
    margin: 0;
    &:before{
      display: none;
    }
  }
  .fit-icon-tick{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-44%,-50%);
  }
  &:before{
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 200%;
    height: 200%;
    border:1px solid rgba(208,208,208,1);
    transform: scale(0.5);
    transform-origin: 0 0 ;
    border-radius: 8/@__var_rem;
  }
  &__hide{
    opacity: 0;
  }
}
