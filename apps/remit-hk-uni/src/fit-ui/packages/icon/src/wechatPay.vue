<!-- eslint-disable max-len -->
<template>
  <svg
    width="34px"
    height="30px"
    class="weui-icon-svg weui-icon-svg-height-1"
    :style="{'font-size':pxToRem(size) }"
    viewBox="0 0 34 30"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>Fill 1</title>
    <desc>Created with Sketch.</desc>
    <g
      id="页面1"
      stroke="none"
      stroke-width="1"
      fill="none"
      fill-rule="evenodd"
    >
      <g
        id="话费充值-8备份"
        transform="translate(-60.000000, -311.000000)"
        fill="#FFFFFF"
      >
        <g
          id="Group-3"
          transform="translate(30.000000, 292.000000)"
        >
          <g id="Fill-1">
            <path d="M42.2041667,37.7287083 C42.0525,37.8045417 41.8820833,37.849125 41.70125,37.849125 C41.2816667,37.849125 40.9166667,37.6182917 40.725,37.2770417 L40.6520833,37.1170417 L37.5970833,30.4137083 C37.5641667,30.3407917 37.54375,30.2582917 37.54375,30.1774583 C37.54375,29.869125 37.7941667,29.619125 38.1029167,29.619125 C38.22875,29.619125 38.3441667,29.660375 38.4375,29.7299583 L42.0420833,32.2962083 C42.305,32.4682917 42.6204167,32.569125 42.9591667,32.569125 C43.1604167,32.569125 43.3533333,32.5320417 43.5325,32.466625 L60.48375,24.9224583 C57.4454167,21.3407917 52.4408333,18.9999583 46.7779167,18.9999583 C37.51125,18.9999583 30,25.2599583 30,32.9824583 C30,37.1957917 32.2591667,40.9887083 35.7970833,43.5520417 C36.0808333,43.7545417 36.26625,44.0870417 36.26625,44.4632917 C36.26625,44.5874583 36.2404167,44.700375 36.2075,44.819125 C35.9254167,45.8732917 35.4729167,47.5607917 35.4520833,47.6399583 C35.4166667,47.771625 35.36125,47.910375 35.36125,48.0487083 C35.36125,48.3574583 35.6116667,48.6074583 35.92125,48.6074583 C36.0420833,48.6074583 36.14125,48.5624583 36.2441667,48.5037083 L39.9175,46.382875 C40.1933333,46.2237083 40.4858333,46.1249583 40.8083333,46.1249583 C40.98,46.1249583 41.1458333,46.1512083 41.30125,46.199125 C43.015,46.691625 44.8641667,46.965375 46.7779167,46.965375 C56.045,46.965375 63.5570833,40.705375 63.5570833,32.9824583 C63.5570833,30.644125 62.8641667,28.4412083 61.6458333,26.5037083 L42.3266667,37.6574583 L42.2041667,37.7287083 Z" />
          </g>
        </g>
      </g>
    </g>
  </svg>
</template>

<script lang="ts">
import mixins from '../mixins';

export default defineComponent({
  mixins: [mixins],
  props: {
    fill: {
      type: String,
      default: '#07C160',
    },
    size: {
      type: [String, Number],
      default: 34,
    },
  },
});
</script>
