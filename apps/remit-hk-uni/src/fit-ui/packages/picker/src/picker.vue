<template>
  <div
    class="fit-multi-select"
    :class="{'fit-form-item-el':isInForm}"
  >
    <div
      class="fit-form-line"
      :class="{'fit-form-item-el-line':isInForm}"
      @click="onClick"
    >
      <span v-if="isValueAvaliavle">{{ curLabel }}</span>
      <span
        v-else
        class="fit-color-light-grey"
      >{{ placeholder }}</span>
      <span
        v-if="isInForm"
        class="fit-form-item-suffix"
      >
        <fit-icon icon="triangle" />
      </span>
    </div>
    <FitHalfScreenDialog
      :title="title"
      :visible="visible"
      :append-to-body="true"
      :mask-close="maskClose"
      @on-close="onClose"
    >
      <template #bd>
        <div

          class="weui-picker__bd"
        >
          <ScrollList
            v-for="(number,i) in depth"
            :key="i"
            :temp="curResult[i]"
            :items="multiItems[i]"
            @on-change="(item,index)=>onChange(item,index,i)"
          />
        </div>
      </template>
      <template #ft>
        <div
          class="weui-picker__group"
        >
          <FitButton @click="confirm">
            {{ confirmBtnText }}
          </FitButton>
        </div>
      </template>
    </FitHalfScreenDialog>
  </div>
</template>
<script setup lang="ts">
import { FitHalfScreenDialog, FitButton } from '@/fit-ui';
import FitIcon from '@/fit-ui/packages/icon/index.vue';
import ScrollList from './scrollList.vue';
interface Item {
  label: string;
  value: string;
  children?: Item[];
}

interface Props {
  title?: string;
  isInForm?: boolean;
  maskClose?: boolean;
  confirmBtnText?: string;
  placeholder?: string;
  items?: Item[];
  depth?: number;
  value?: string[] | string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  isInForm: false,
  maskClose: true,
  confirmBtnText: '',
  placeholder: '',
  items: () => [],
  depth: 3,
  value: '',
});

const emits = defineEmits<{
  (e: 'onClose'): void;
  (e: 'change', value: string[], label: string[]): void;
}>();

const visible = ref(false);
const curLabel = ref('');
const curResult = ref<number[]>([]);

const multiItems = computed(() => {
  let { items } = props;
  const outputItems: Item[][] = [];
  for (let i = 0; i < props.depth; i++) {
    if (
      typeof curResult.value[i] !== 'undefined'
        && items[curResult.value[i]]
    ) {
      // 说明能找到值
      outputItems.push(items);
      items = items[curResult.value[i]].children || [];
    } else {
      // 找不到 空数组
      outputItems.push([]);
    }
  }
  return outputItems;
});

function onClick() {
  visible.value = true;
}

function getValueAndLabelByResult() {
  const value: string[] = [];
  const label: string[] = [];
  console.log(
    'getValueAndLabelByResult----->',
    JSON.stringify(curResult.value),
  );
  curResult.value.reduce((pre: Item[], next: number) => {
    console.log('reduce----->', pre, next);
    const obj = pre[next];
    if (obj) {
      value.push(obj.value);
      label.push(obj.label);
      return obj.children || [];
    }
    return [];
  }, props.items);
  return [value, label];
}

function confirm() {
  const [value, label] = getValueAndLabelByResult();
  console.log('confirm', value, label);

  emits('change', value, label);
  onClose();
}
function onChange(item: object, index: number, i: number) {
  curResult.value.splice(i, 1, index);
  for (let j = i + 1, l = curResult.value.length; j < l; j++) {
    curResult.value[j] = 0;
  }
}

// created
onMounted(() => {
  curResult.value = getCurResultByValue();
  setLable();
});

const isValueAvaliavle = computed(() => {
  console.log('isValueAvaliavle----->', JSON.stringify(props.value));
  const valueLength = props.value.length;
  if (valueLength === 0) {
    // 数值为空
    return false;
  }
  let tem = props.items;
  for (let i = 0, l = valueLength; i < l; i++) {
    if (!Array.isArray(tem)) {
      // 说明有数值 但是没有匹配了
      return false;
    }
    const resultIndex = tem.findIndex(item => item.value === props.value[i]);
    console.log('resultIndex----->', resultIndex, JSON.stringify(tem));

    if (resultIndex === -1) {
      return false;
    }
    tem = tem[resultIndex].children as Item[];
  }
  if (valueLength < props.depth) {
    // 如果数值小于深度 还要判断是否还有子项 没有子项为true 有为false
    return !tem;
  }
  return true;
});

function getCurResultByValue() {
  console.log(
    'getCurResultByValue----->start',
    JSON.stringify(isValueAvaliavle.value),
  );
  if (!isValueAvaliavle.value) {
    return new Array(props.depth).fill(0);
  }
  const result: number[] = [];
  let tem = props.items;
  for (let i = 0; i < props.depth; i++) {
    const resultIndex = tem.findIndex(item => item.value === props.value[i]);
    if (resultIndex === -1) {
      return result;
    }
    tem = tem[resultIndex].children || [];
    result.push(resultIndex);
  }
  console.log('getCurResultByValue----->', JSON.stringify(result));
  return result;
}

watch(() => visible.value, () => {
  if (visible.value) {
    curResult.value = getCurResultByValue();
  }
});

function setLable() {
  console.log('touch ser label', props.value, isValueAvaliavle.value);
  if (isValueAvaliavle.value) {
    // const  [value, label] = this.getValueAndLabelByResult();
    // this.curLabel = label.join(',');
    curLabel.value = getLabel(props.value, props.items);
    return;
  }
  curLabel.value = '';
}

watch(() => props.items, setLable);
watch(() => props.value, setLable);

function getLabel(value: string | string[], items: Item[]) {
  const label: string[] = [];
  let itemList = items;
  for (let i = 0, l = value.length; i < l; i++) {
    const curValue = value[i];
    const curItem = itemList.find(item => item.value === curValue);
    if (curItem) {
      label.push(curItem.label);
      itemList = curItem.children || [];
    } else {
      return '';
    }
  }
  return label.join(',');
}

function onClose() {
  emits('onClose');
  visible.value = false;
}

</script>

<style scoped lang='less'>
@import '../../style/picker.less';
:deep(.weui-half-screen-dialog) {
  padding: 0 30rpx;
}
</style>

