<template>
  <div class="weui-container">
    <div class="weui-mask_transparent" />
    <div
      class="weui-toast fit-t-c"
      :class="[{'weui-toast__dot_type':showDotLine}]"
    >
      <slot name="icon">
        <FitIcon
          :icon="icon"
          class="weui-icon_toast"
          size="91"
          fill="#fff"
        />
      </slot>
      <div
        class="weui-toast__content"
        :class="[{'weui-toast__content__dot':showDotLine}]"
      >
        <slot />
        <div
          v-if="showDotLine"
          class="weui-toast__dot-line"
        >
          <div class="weui-toast__dot" />
          <div class="weui-toast__dot" />
          <div class="weui-toast__dot" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import FitIcon from '@/fit-ui/packages/icon/index.vue';

defineProps({
  showDotLine: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: 'success-no-circle',
  },
  size: {
    type: [String, Number],
    default: 200,
  },
});
</script>

<style scoped lang='less'>
@import '../../style/toast.less';
</style>
