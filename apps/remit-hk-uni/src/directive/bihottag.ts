/* eslint-disable no-param-reassign */
import { DirectiveBinding, Directive } from 'vue';
import { biHottag } from '@/business/biHottag';

// 上报缓存，用于记录已上报事件
const biCache: { brow: string[] } = {
  brow: [],
};

// 上报序列化
const biStringify = (tag: string, extBiParams: Record<string, string>) => `${tag}-${JSON.stringify(extBiParams)}`;

export const directiveOption: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const {
      arg,
      modifiers,
      value,
    } = binding;

    const { tag, extBiParams = {} } = value;

    if (!tag) {
      throw Error('上报tag不能为空');
    };

    if (typeof tag !== 'string') {
      throw Error('上报tag必须为string类型');
    }

    if (extBiParams && typeof extBiParams !== 'object') {
      throw Error('上报拓展字段必须为object类型');
    }

    // 曝光埋点
    if (arg === 'brow') {
      // 缓存字符串生成，作为重复上报的唯一标识
      const cacheStr = biStringify(tag, extBiParams);

      // 如果只上报一次，且缓存中已有记录，直接返回
      if (modifiers?.once && biCache.brow.includes(cacheStr)) {
        return;
      }

      // 上报埋点
      biHottag(tag, extBiParams);

      // 记录缓存
      biCache.brow.push(cacheStr);
    }
  },
};

export default directiveOption;
