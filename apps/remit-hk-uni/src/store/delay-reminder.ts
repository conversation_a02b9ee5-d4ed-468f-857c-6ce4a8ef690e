/* eslint-disable camelcase */
import { VuexModule, Module, Mutation, getModule, Action } from 'vuex-module-decorators';
import store from './index';
import { language } from '@tencent/ppd-base-libs';
import { isTimeValidate } from '@/business/time';

export interface DelayConfigText {
  delayMinutes: Number;
  recordInProcessDesc: string;
}

@Module({ dynamic: true, store, name: 'delayReminder', namespaced: true })
class DelayReminder extends VuexModule {
  // 是否要提示
  public senderText: DelayConfigText = {
    delayMinutes: 0,
    recordInProcessDesc: '',
  };
  public receiverText: DelayConfigText = {
    delayMinutes: 0,
    recordInProcessDesc: '',
  };

  public get getSenderText() {
    return this.senderText;
  }

  public get getReceiverText() {
    return this.receiverText;
  }

  /**
   * 初始化延迟到账的配置文案
   */
  @Action
  public async initDelayReminderText() {
    const textList = window.$$delayTextConfig || [];
    // 找出有效期内的文案配置
    let priority = 0;
    let resultText: Record<string, string> = {};
    textList.forEach((item) => {
      if (isTimeValidate(item.startTime, item.endTime)) {
        if (Number(item.priority) > priority) {
          priority = Number(item.priority);
          resultText = item;
        }
      }
    });
    if (!resultText?.startTime) {
      return;
    }
    this.addSenderDelayText(resultText);
    this.addReceiverDelayText(resultText);
  }

  @Mutation
  public addSenderDelayText(param) {
    const lang = language.getLanguage().replace(/-/g, '_');
    this.senderText.delayMinutes = Number(param.delayTime);
    this.senderText.recordInProcessDesc = param[`sender_record_desc_${lang}`];
  }

  @Mutation
  public addReceiverDelayText(param) {
    this.receiverText.delayMinutes = Number(param.delayTime);
    this.receiverText.recordInProcessDesc = param.receiver_record_desc;
  }

  // 用于单测使用,清空副作用
  @Mutation
  public reset() {
    // 是否要提示
    this.senderText = {
      delayMinutes: 0,
      recordInProcessDesc: '',
    };
    this.receiverText = {
      delayMinutes: 0,
      recordInProcessDesc: '',
    };
  }
}


const delayReminderModule = getModule(DelayReminder);
export default delayReminderModule;
