import { <PERSON><PERSON><PERSON><PERSON> } from '@/types';
import { hisListStateQry, ListState, HisListStateQryRes } from '@/api';
import cache from '@/business/cache';
import { arrayMinus, arrayPlus } from '@/utils';
import { CustomException, util } from '@tencent/ppd-base-libs';
import { VuexModule, Module, getModule, Action, Mutation } from 'vuex-module-decorators';
import store from './index';


/**
 * 提醒展示态
 */
export enum NoticeDisplay {
  NONE = '0', // 无未完成订单
  UNPAY = '1', // 一笔未支付订单
  UNSHARE = '2', // 一笔未分享订单
  UNRECEIVE = '4', // 一笔未收款订单
  UNFINISH = '100', // 多笔未完成订单
  SHOWBADGE = '101' // footer小标展示
};

/**
 * 未完成订单状态
 */
export enum UnfinishOrderState {
  UNPAY = '1', // 未支付
  UNSHARE_OTHER = '2', // 未分享给别人
  UNSHARE_SELF = '3', // 未分享给自己
  UNRECEIVE = '4', // 未收款
};

function getCacheList(cacheKey: CacheKey) {
  let list = cache.getCache(cacheKey);
  if (!Array.isArray(list)) {
    list = [];
  }
  return list;
}

/**
 * 解析订单
 * @param str
 */
export function parseListState(str: string) {
  const [listid, caseSeq] = str.split('|') as [string, '1' | '2' | '3' | '4'];
  const listState: ListState = {
    listid,
    case_seq: caseSeq,
  };
  return listState;
}

/**
 * 序列化订单
 * @param listState
 */
export function stringifyListState(listState: ListState) {
  return `${listState.listid}|${listState.case_seq}`;
}

/**
 * 判断未支付订单
 * @param order
 */
function isUnpayOrder(order: ListState) {
  return order.case_seq === UnfinishOrderState.UNPAY;
}

/**
 * 判断未分享订单
 * @param order
 */
function isUnShareOrder(order: ListState) {
  return order.case_seq === UnfinishOrderState.UNSHARE_OTHER;
}

/**
 * 判断未收款订单
 * @param order
 */
function isUnReceiveOrder(order: ListState) {
  return order.case_seq === UnfinishOrderState.UNRECEIVE || order.case_seq === UnfinishOrderState.UNSHARE_SELF;
}

/**
 * 获取需要提醒的订单列表
 * @param hisListStateRes
 * @return {string[]}
 */
function getCurrentUnFinishlistidArr(hisListStateRes: HisListStateQryRes) {
  // 获取接口返回的未完成订单，并序列化订单
  const arr = hisListStateRes.his_list
    ? hisListStateRes.his_list.map(item => stringifyListState(item))
    : [];

  return arr;
}


@Module({ dynamic: true, store, name: 'unpayOrder', namespaced: true })
class UnFinishOrder extends VuexModule {
  /**
   * 未完成订单列表(未分享 & 未支付)
   */
  public curUnFinishOrderList: string[] = [];

  /**
   * 顶部 - 待提醒未完成订单列表
   */
  public unFinishOrderList: string[] = [];

  /**
   * 小红点 - 待提醒未完成订单列表
   */
  public badgeShowOrderList: string[] = [];

  /**
   * 提示展示状态
   */
  public noticeDisplay: NoticeDisplay = NoticeDisplay.NONE;

  /**
   * 未完成订单数量
   * 以后端返回数量为准
   */
  public get unFinishOrderListNum() {
    return this.curUnFinishOrderList.length;
  }

  /**
   * 是否在footer显示 badge
   */
  public get shouldShowBadge() {
    return this.noticeDisplay === NoticeDisplay.SHOWBADGE;
  }

  /**
   * 设置订单提醒缓存
  */
  @Action
  public setUnFinishOrderListCache() {
    // 顶部 - 更新已提醒缓存
    cache.setCache(
      CacheKey.CACHE_HAS_NOTICED_UNFINISH_ORDER_LIST,
      this.curUnFinishOrderList,
    );
  }

  /**
   * 点击去分享 或 去者支付
   * 追加小红点已提醒
   */
  @Action
  public clearBadgeUnFinish() {
    this.updateHasBadgeShowOrderList(this.unFinishOrderList);
  }

  /**
   * 去到汇款详情
   * 重置小红点已提醒 -> 当前未完成订单
   */
  @Action
  public clearBadgeAll() {
    this.replaceHasBadgeShowOrderList();
  }

  /**
   * 是否在白名单
   */
  @Action
  public async initUnFinishOrderList() {
    const [err, res] = await util.to(hisListStateQry({
      /**
       * 订单状态列表查询
       *
       * 四种状态，用4位二进制表示
       *
       * 例：
       * "<4><3><2><1>"
       *
       * <1>：未支付订单
       *
       * <2>：未分享订单（汇款给别人）
       *
       * <3>：未分享订单（汇款给自己）
       *
       * <4>：未完成收款（汇款给自己）
       */
      bit_case_flag: '1111',
    }));
    if (err) {
      new CustomException(err, 'hisListStateQryFail', '查询未支付订单数量失败');
      // 由于不影响主流程 静默报错即可 不用loading 也不用弹窗
      return;
    }

    // 获取需要提醒的订单列表
    const curUnFinishlistidArr = getCurrentUnFinishlistidArr(res);

    // 未完成订单
    this.setCurUnfinishOrderList(curUnFinishlistidArr);

    // 设置需提醒订单
    this.setUnFinishOrderList();

    // 设置小红点提醒订单，并缓存需提醒
    this.setBadgeShowOrderList();

    // 设置展示态
    this.setNoticeDisplay();
  }

  /**
   * 展示badge
   */
  @Mutation
  public showBadge() {
    this.noticeDisplay = NoticeDisplay.SHOWBADGE;
  }

  /**
   * 关闭提醒
   */
  @Mutation
  public hideNotice() {
    this.noticeDisplay = NoticeDisplay.NONE;
  }

  /**
   * 后端返回未完成订单
   *
   * @param list
   */
  @Mutation
  private setCurUnfinishOrderList(list: string[]) {
    this.curUnFinishOrderList = list;
  }

  /**
   * 顶部 - 设置未完成订单
   * @param {string[]} curUnFinishlistidArr
   */
  @Mutation
  private setUnFinishOrderList() {
    // 顶部需提醒 = 当前未完成 - 已提醒
    this.unFinishOrderList = arrayMinus(
      this.curUnFinishOrderList,
      getCacheList(CacheKey.CACHE_HAS_NOTICED_UNFINISH_ORDER_LIST),
    );
  }

  /**
   * 设置小红点未完成订单
   * @param {string[]} list
   */
  @Mutation
  private setBadgeShowOrderList() {
    // 小红点需提醒 = 当前未完成 - 已提醒缓存
    this.badgeShowOrderList = arrayMinus(
      this.curUnFinishOrderList,
      getCacheList(CacheKey.CACHE_HAS_BADGE_NOTICED_ORDER_LIST),
    );
  }

  /**
   * 设置展示态
   * @param list []
   */
  @Mutation
  private setNoticeDisplay() {
    const list = this.unFinishOrderList; // 未提醒过的未完成订单列表
    const curList = this.curUnFinishOrderList; // 后端返回的未完成订单列表

    // 没有未完成订单 (用缓存去重过的未完成订单列表做判断)
    if (!list.length) {
      // 小红点提醒
      if (this.badgeShowOrderList.length) {
        this.noticeDisplay = NoticeDisplay.SHOWBADGE;
        return;
      }
      this.noticeDisplay = NoticeDisplay.NONE;
      return;
    };

    // 多笔未完成（用后端返回的未完成订单列表做判断）
    if (curList.length > 1) {
      this.noticeDisplay = NoticeDisplay.UNFINISH;
      return;
    }

    // 获取订单对象
    const order = parseListState(curList[0]);

    // 未支付订单
    if (isUnpayOrder(order)) {
      this.noticeDisplay = NoticeDisplay.UNPAY;
      return;
    }

    // 未分享订单
    if (isUnShareOrder(order)) {
      this.noticeDisplay = NoticeDisplay.UNSHARE;
      return;
    }

    // 未收款订单
    if (isUnReceiveOrder(order)) {
      this.noticeDisplay = NoticeDisplay.UNRECEIVE;
      return;
    }
  }

  /**
   * 重置小红点已提醒订单列表
   *
   * @param list
   */
  @Action
  private replaceHasBadgeShowOrderList() {
    cache.setCache(
      CacheKey.CACHE_HAS_BADGE_NOTICED_ORDER_LIST,
      this.curUnFinishOrderList,
    );
    this.clearBadgeShowOrderList();
    this.setNoticeDisplay();
  }

  /**
   * 追加小红点已提醒订单列表
   *
   * @param list
   */
  @Action
  private updateHasBadgeShowOrderList(list: string[]) {
    cache.setCache(
      CacheKey.CACHE_HAS_BADGE_NOTICED_ORDER_LIST,
      arrayPlus(list, getCacheList(CacheKey.CACHE_HAS_BADGE_NOTICED_ORDER_LIST)),
    );
    this.updateBadgeShowOrderList(list);
    this.setNoticeDisplay();
  }

  /**
   * 更新小红点未提醒订单列表 （移除 list）
   */
  @Mutation
  private updateBadgeShowOrderList(list: string[]) {
    this.badgeShowOrderList = arrayMinus(this.badgeShowOrderList, list);
  }

  /**
   * 清空小红点未提醒订单列表
   */
  @Mutation
  private clearBadgeShowOrderList() {
    this.badgeShowOrderList = [];
  }
}


const UnFinishOrderModule = getModule(UnFinishOrder);
export default UnFinishOrderModule;
