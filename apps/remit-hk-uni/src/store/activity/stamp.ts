import { defineStore } from 'pinia';
import useAct from './index';
import { CustomException, util } from '@tencent/ppd-base-libs';
import { UseRequest, initRequest } from '@/hooks/business/useRequest';
import { qryActStamp, queryActInfo, ActInfoRes } from '@/api/activity';
import useUserTags from '@/store/user-tag';
import { isTimeValidate } from '@/business/time';
import { StoreKeys } from '../store-key';
import { DialogConfig } from '@/domain/common/dialog';

// 格式化印花信息
export type FormatStampActInfo = FrontActConfig<StampPayload>  &  {
  stamp: number,
  maxRecvTimes: number,
  isEffected: boolean,
} | null;

type StampActPayload = {
  award_id: string;// 奖品id
  is_effective: '1'|'0';// 1代表生效中, 0 代表未生效
  allow_recv_times: string;// 允许获得最大奖品数
};

// 活动是否有效：活动在有效期内 & (用户没有首次支付时间 || 用户首次支付时间在有效期内)
function checkActIsEffective(actInfo: Required<ActInfoRes<StampActPayload>>['formatActInfo'], firstPayTime: string) {
  console.log('【印花活动信息】', actInfo);
  console.log('【印花活动信息】', '首次支付时间', firstPayTime);
  console.log('【印花活动信息】', '活动开始时间', actInfo.act_start_time, '活动结束时间', actInfo.act_end_time);
  if (+actInfo?.is_effective !== 1) return false;
  return !firstPayTime || isTimeValidate(actInfo.act_start_time, actInfo.act_end_time, firstPayTime);
}

/**
 * 落地页配置
 */

interface LandingPage {
  /**
   * 落地页背景图
   */
  bgImg: string;
  /**
   * 活动投放id
   */
  promId: string;
}

// 印花活动额外配置
interface StampPayload{
  // 印花核销个数
  redemptionCount: number;
  // 用户标签
  userTag: string;
  /**
   * 落地页数据
   */
  landingPage: LandingPage;
  // 分享配置
  share: ShareConfig;
  // 弹窗配置
  dialog: {
    // 未参与前三笔活动
    newUserProm: DialogConfig
    // 活动无资格配置
    ineligible: DialogConfig
  }
}

export default defineStore(StoreKeys.STAMP_ACT, () => {
  const actMap: Record<string, UseRequest<FormatStampActInfo>> = {};
  const qryAct = async (actId: string): Promise<FormatStampActInfo> => {
    const { getFrontActInfo } = useAct();// 前端活动配置
    const { qryUserTag } = useUserTags();// 用户标签
    const actConfig = getFrontActInfo<StampPayload>(actId);
    // 通过活动id获取前端配置，如果获取不到直接返回空（底层已经针对获取不到做了异常上报，此处不用重复处理）
    if (!actConfig) return null;
    const [err, res] = await util.to(Promise.all([
      // 查询活动信息
      queryActInfo<StampActPayload>(actId),
      // 获取印花信息
      qryActStamp({
        act_id: actId,
      }),
      // 查询用户标签
      qryUserTag(actConfig.payload.userTag),
    ]));
    if (err || !res) {
      throw new CustomException(err, 'getStampActInfoFail', '获取印花活动信息失败');
    }
    // 解构放在err判断后，因为如果触发err，res将为null null无法被解构
    const [actInfo, stampInfo, firstPayTime] = res;
    if (!actInfo.formatActInfo) {
      throw new CustomException(err, 'getStampFormatActInfoFail', '获取印花格式化活动信息失败');
    }

    // 是否活动有效（!!前端临时写死，后续逻辑迁移到活动中台）
    const isEffected = checkActIsEffective(actInfo.formatActInfo, firstPayTime);
    console.log('【印花活动信息】', '是否活动有效', isEffected);
    // 印花派发数组
    const stampArr = stampInfo.act_stamp_arr || [] ;
    const formatActInfo = {
      // 印花个数
      stamp: stampArr.length,
      // 最大兑换次数（最大派发奖品数）
      maxRecvTimes: +actInfo.formatActInfo.allow_recv_times,
      // 前端活动配置
      ...actConfig,
      // 活动是否生效
      isEffected,
    };
    console.log('【印花合并配置】', formatActInfo);
    // 合并配置
    return formatActInfo;
  };
  // 创建响应式请求对象
  const createActRequest = (id: string) => {
    actMap[id] = actMap[id] || initRequest(null);
    const requestObj = actMap[id];
    return {
      data: requestObj,
      qryAct: () => {
        requestObj.setLoading();
        return qryAct(id).then((data) => {
          requestObj.setValue(data);
        })
          .catch((err) => {
            requestObj.setError(err);
          });
      },
    };
  };
  return {
    createActRequest,
  };
});
