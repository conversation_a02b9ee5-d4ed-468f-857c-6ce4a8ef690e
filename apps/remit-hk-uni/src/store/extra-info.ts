import { VuexModule, Module, Mutation, getModule, Action } from 'vuex-module-decorators';
import store from './index';
import { RemitInfoMgrReq } from '@/api';
import { i18n } from '@/i18n';
import homeModule from './home';
import kycModule from './kyc';
import { encrypt } from '@/business/crypto';
import { CardState } from '@/business/config';

export interface ExtraInfoForm {
  form: RemitInfoMgrReq
}

/**
 * 是否填写过地址信息
 */
enum AgreeState {
  UNFILLED = '0', // 未填写
  FILL = '1'      // 填写过
}

@Module({ dynamic: true, store, name: 'extraInfo', namespaced: true })
class ExtraInfo extends VuexModule implements ExtraInfoForm {
  /**
   * cmd 操作类型
   * 1-新增
   * 2-修改（指修改用户持有中国内地身份证情况，当该值=1或=2存在时，需要传card_state；灰度发布时可不传，如果传card_state，需保证其值=1或=2否则提示参数校验报错）
   */
  public form = {
    cmd: '1',   // 1 新增 2 修改
    remit_country: 'HK', // 这里明确了稠州接口里，常驻国家代码定义为汇款来自国家或地区，所以港陆汇款这边默认为HKG
    remit_addr: '',
    card_state: CardState.UNFILLED,   // 0 未填写 1 持有 2 未持有
  };

  /**
   * 是否补充过汇款人信息 既补充了汇款信息，也补充了身份证
   */
  public get isSetExtraInfo() {
    return homeModule.homeInitRes?.agree_state === AgreeState.FILL
      && homeModule.homeInitRes?.card_state !== CardState.UNFILLED;
  }

  public get canBtnNext() {
    // 有其中之一未填写不可通过
    if (!this.form.remit_country) return false;
    /**
     * 非高级认证用户不可通过  未填写过地址并且表单地址未填写不可通过
     * 表单信息为未填写不可通过
     */
    if (
      !this.isSeniorOrEkycSuccess
      && (this.isCheckValidAgreeStateFill && !this.form.remit_addr)
    ) return false;
    if (!this.form.card_state || this.form.card_state === CardState.UNFILLED) return false;
    return true;
  }

  /**
   * ekyc或者高级cdd认证是否通过
   */
  public get isSeniorOrEkycSuccess() {
    return kycModule.isSeniorKycSuccess || kycModule.isEkycSuccess;
  }

  /**
   * 获取加密的补充信息
   */
  @Action
  public getEncryptData() {
    const data: RemitInfoMgrReq = Object.assign({}, this.form);
    if (this.form.remit_addr) {
      data.remit_addr = encrypt(this.form.remit_addr);
    }
    if (this.form.card_state && !this.isCheckValidAgreeStateFill) {
      data.cmd = '2';
    }
    return data;
  }

  public get getFormRule() {
    return {
      remit_country: [{
        required: true,
        message: i18n('请选择汇款人居住国家和地区'),
      }],
      remit_addr: [{
        required: !this.isSeniorOrEkycSuccess && this.isCheckValidAgreeStateFill,
        message: i18n('请填写你的居住地址'),
        type: 'string',
      }, {
        required: !this.isSeniorOrEkycSuccess && this.isCheckValidAgreeStateFill,
        message: i18n('最多输入80个字符'),
        type: 'string',
        validator: (rule, value) => value.length <= 80,
      }],
      card_state: [{
        required: true,
      }],
    };
  }

  /**
   * 校验用户地址信息是否需要填写
   * @return true true 则需要填写
   */
  public get isCheckValidAgreeStateFill() {
    return homeModule.homeInitRes?.agree_state === AgreeState.UNFILLED;
  }

  @Mutation
  public updateForm({ key, value }) {
    this.form[key] = value;
  }
}


const ExtraInfoModule = getModule(ExtraInfo);
export default ExtraInfoModule;
