/** 这里编写storybook文档*/

import Component from './Component.vue';

export default {
  title: '业务组件/BizEventBridge',
  component: Component,
  tags: ['autodocs'],
  render: args => ({
    components: {
      Component,
    },
    setup() {
      return {
        args,
      };
    },
    template: '<Component v-bind="args" />',
  }),
};

// More on writing stories with args: https://storybook.js.org/docs/vue/writing-stories/args
export const Primary = {
  args: {
  },
};


