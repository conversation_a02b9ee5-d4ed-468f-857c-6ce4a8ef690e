<template>
  <RateValueBox
    v-show="bankRate || rate"
    v-bind="$attrs"
    :value-box="valueBox"
  />
</template>

<script setup lang="ts">
import RateValueBox from './component.vue';
import { useBankRate } from '@/domain/common/useBankRate';
import { useRate } from '@/domain/common/useRate';
import { showLoading } from '@/adapters/uni-api-common';
import { hideLoading } from '@/fit-components/loading/main';

const {
  bankRate, // 银行汇率
  initBankRate,
  isBankRateLoading,
} = useBankRate();

const {
  rate, // 实时汇率
  initRate,
  isRateLoading,
} = useRate();

// 监听汇率变化展示loading
watch([isBankRateLoading, isRateLoading], () => {
  if (isBankRateLoading.value || isRateLoading.value) {
    showLoading();
    return;
  }
  hideLoading();
});
onMounted(() => {
  // 银行汇率懒加载
  if (!bankRate.value && !isBankRateLoading.value) {
    initBankRate();
  }
  // 汇率懒加载
  if (!rate.value && !isRateLoading.value) {
    initRate();
  }
});

/**
 * 汇率值（实时汇率、银行汇率）
 */
const valueBox = computed(() => ({
  rate: rate.value || '',
  bankRate: bankRate.value || '',
}));
</script>
