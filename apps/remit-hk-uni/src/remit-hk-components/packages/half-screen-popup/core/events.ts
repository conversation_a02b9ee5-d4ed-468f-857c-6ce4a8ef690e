export type EmitsSimple<V = undefined, A = undefined> = {
  /**
   * emit被触发时，接收方接收的内容
   */
  value?: V;
  /**
   * dom/组件触发emit传入的内容
   */
  args?: A;
};

// eslint-disable-next-line max-len
export type EmitEvents<T extends Record<string, EmitsSimple<unknown, unknown>> = Record<string, EmitsSimple<undefined, undefined>>> = T;


export type CommonEmits<Type = unknown, Value = unknown> = {
  event: Type;
  value?: Value;
};

export type GetEvents<Emits extends Record<string, unknown>> = {
  [Key in keyof Emits]: (value?: Emits[Key] extends EmitsSimple<unknown, unknown> ? Emits[Key]['value'] : Emits[Key]) => Promise<void> | void;
};

export type GetType<T> = T extends (arg: infer P) => void ? P : undefined;

export type EventConfig<Value = unknown> = {
  /**
   * 在事件前执行
   */
  before?: (event?: Value) => Promise<void> | void;
  /**
   * 在事件后执行
   */
  after?: (event?: Value) => Promise<void> | void;
};

export type AllEventConfig<T extends Record<string, unknown>> = {
  [Key in keyof T]?: EventConfig<GetType<T[Key]>>;
};

// eslint-disable-next-line max-len
export const createDefineEvents = <T extends GetEvents<Record<string, EmitsSimple<undefined, undefined>>>>(config?: AllEventConfig<T>) => (events: Partial<T>) => async <Key extends keyof T>(commonEvent: CommonEmits<Key, GetType<T[Key]>>) => {
  await config?.[commonEvent.event]?.before?.(commonEvent.value);

  await events[commonEvent.event as string]?.(commonEvent.value);

  await config?.[commonEvent.event]?.after?.(commonEvent.value);
};

// eslint-disable-next-line max-len
export const defineEvents = <T extends Record<string, unknown>>(config?: AllEventConfig<GetEvents<T>>) => createDefineEvents<GetEvents<T>>(config);

export type SimpleEmit<A extends Array<unknown>, V = unknown> = (...args: A) => V;

/**
 * 自定义类型保护和类型谓词
 * @returns
 */
function isHooks<T extends Record<string, Record<string, unknown>>, Key extends keyof T>(valueHooks: unknown): valueHooks is SimpleEmit<[T[Key]['args'] | undefined, ValueHooksConfigs], T[Key]['value']> {
  return typeof valueHooks === 'function';
}

/**
 * hooks事件相关内容
 */
export type ValueHooksConfigs<ValueHooksOtherArgs extends Array<unknown> = Array<unknown>> = {
  /**
   * 取消事件触发
   */
  cancel: () => void;
  /**
   * 启动事件触发
   */
  open: () => void;
  /**
   * 其他参数
   */
  otherArgs: ValueHooksOtherArgs;
};

// eslint-disable-next-line max-len
export const defineEmitsForSimple = <T extends Record<string, Record<string, unknown>>>(allEvent: <Key extends keyof T>(args: CommonEmits<Key, GetType<T[Key]>>) => void) => {
  const context = getCurrentInstance();
  return <Key extends keyof T>(event: Key, valueHooks?: T[Key] extends EmitsSimple<unknown, unknown> ? SimpleEmit<[T[Key]['args'] | undefined, ValueHooksConfigs], T[Key]['value']> : T[Key], isEmit = true) => {
    const emit = (value: unknown) => {
      allEvent({
        event,
        value: value as (GetType<T[Key]> | undefined),
      });
      context?.emit(event as string, value);
    };
    if (isHooks<T, Key>(valueHooks)) {
      const context = { emit: true };
      /**
         * 取消事件
         */
      const cancel = () => {
        context.emit = false;
      };
      const open = () => {
        context.emit = true;
      };
      return <OtherArgs extends ValueHooksConfigs['otherArgs']>(value: T[Key] extends EmitsSimple<unknown, unknown> ? T[Key]['args'] : undefined, ...otherArgs: OtherArgs) => {
        const result = valueHooks(value, {
          open,
          cancel,
          otherArgs,
        });
        if (!context.emit || !isEmit) {
          return;
        }
        emit(result);
      };
    }
    emit(valueHooks);
    // 避免类型为undefined 不符合类型
    return () => {};
  };
};
