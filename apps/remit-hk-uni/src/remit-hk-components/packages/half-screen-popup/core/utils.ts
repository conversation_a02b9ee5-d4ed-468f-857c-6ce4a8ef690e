import { MAX_HEIGHT_SIGN } from './constants';
import type { HalfScreenPopupPage, UA } from './../half-screen-popup.types';

/**
 * 是否配置中的弹窗高度为最大的标志 'max'
 * @param height 弹窗高度
 * @returns
 */
export const isPopupHeightIsMaxSign = (height: HalfScreenPopupPage['popupHeight']) => height === MAX_HEIGHT_SIGN;

/**
 * 获取浏览器环境
 * @returns
 */
export const getSystemOs = (): UA => {
  const u = navigator.userAgent;
  if (u.includes('Android') || u.includes('android') || u.includes('Linux')) {
    return 'android';
  }
  if (u.includes('iPhone') || u.includes('iOS')) {
    return 'ios';
  }
  return 'ios';
};


export type GetType<T> = T extends (...args: infer P) => void ? P : undefined;


/**
 * 深度设置响应对象
 * @param object 目标响应对象
 * @param key 对应的key
 * @param value 对应的值
 */
// eslint-disable-next-line max-len
export const setObjctByKey = <Object extends any, Key extends keyof Object>(object: Object, key: Key, value: Object[Key]) => {
  object[key] = value;
};

/**
 * 获取实际时间，时间为s
 */
export const getStringTime = (time: number): string => `${time}s`;
