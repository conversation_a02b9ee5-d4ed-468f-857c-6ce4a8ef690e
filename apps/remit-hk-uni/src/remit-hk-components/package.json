{"name": "remit-hk-components", "version": "0.0.2", "description": "", "main": "index.js", "scripts": {"build": "cd ../../ && npm run cmpBuild", "publish": "fbit r", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "keywords": [], "author": "", "license": "ISC", "businessInfo": "12-跨境业务", "compType": "vue-component-library", "devDependencies": {"@storybook/addon-essentials": "^7.0.18", "@storybook/addon-interactions": "^7.0.18", "@storybook/addon-links": "^7.0.18", "@storybook/blocks": "^7.0.18", "@storybook/builder-vite": "^7.0.20", "@storybook/testing-library": "^0.0.14-next.2", "@storybook/vue3": "^7.0.18", "@storybook/vue3-vite": "^7.0.18", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^7.0.18", "vite-plugin-eslint": "^1.8.1"}}