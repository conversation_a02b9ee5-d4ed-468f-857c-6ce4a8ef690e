import { i18n } from '@/fit-components/common/lang';
import store from './store';
import { getDefaultConfig, LoadingType } from '@/fit-components/loading/business';
interface LoadingOptions {
  show?: boolean;
  type?: LoadingType;
  title?: string;
}
const loadingList: number[] = [];


export function showLoading(options: LoadingOptions = { show: true }) {
  loadingList.push(1);
  const finalConfig = Object.assign(getDefaultConfig(), options, { show: true });
  console.log('finalConfig', finalConfig);
  store.loading = finalConfig;
}

export function showSecurityLoading(title = i18n('信息安全保障中')) {
  showLoading({
    type: LoadingType.Security,
    title,
  });
}

// 避免loading被别人覆盖
export function hideLoading() {
  loadingList.pop();
  // if (loadingList.length === 0) {
  // 把这个逻辑干掉了，这中方式不太友好-doblezhang 2020/4/23
  store.loading.show = false;
  // }
}
