import { Ref, ref, watch } from 'vue';
import { CustomException } from '@tencent/ppd-base-libs';
import request from '@/adapters/request/base';
import { LoadState } from '@/types';
import { getPromEditorConfigPath } from '@/utils/prom/url-store';

interface PromContainerOption {
  promId: Ref<string>;
}

/**
 * 活动页容器初始化hooks
 * @param promId 活动id
 */
export const usePromContainer = ({
  promId,
}: PromContainerOption) => {
  const isError = ref(false); // 是否加活动加载异常
  const isPromContainerLoading = ref(true); // 容器组件是否加载中
  const isConfigLoading = ref(true); // 是否配置加载中
  const appData = ref(null); // 中台app数据

  // 异常处理
  const onError = (err) => {
    isError.value = true;
    new CustomException(err, 'promOnError', '营销活动异常');
  };

  // 活动状态 依赖配置加载 & 容器组件加载
  const state = computed(() => {
    if (isError.value) return LoadState.FAIL;
    if (isPromContainerLoading.value || isConfigLoading.value) return LoadState.LOADING;
    return LoadState.SUCCESS;
  });

  // 容器异步加载
  const promContainer = defineAsyncComponent(() => import('@/components/activity/common/prom.vue').then((module) => {
    isPromContainerLoading.value = false;// 加载成功
    return module;
  }).catch((err) => {
    onError(err);
    throw err;
  }));

  // 监听活动id变化，请求活动页配置
  watch(promId, () => {
    if (!promId.value) return;
    isConfigLoading.value = true;
    // 中台生成配置url
    const configUrl = getPromEditorConfigPath(promId.value);
    // 请求中台配置，兼容编辑器接口数据和生成json数据格式
    request.loadVtools(configUrl).then(data => data?.data || data)
      .then((data) => {
        isConfigLoading.value = false;
        appData.value = data;
      })
      .catch((err) => {
        isConfigLoading.value = false;
        onError(err);
      });
  }, {
    immediate: true,
  });

  return {
    state,
    appData,
    promContainer,
  };
};
