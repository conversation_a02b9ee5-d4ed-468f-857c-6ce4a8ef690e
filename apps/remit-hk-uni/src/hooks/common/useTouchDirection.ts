import { onMounted, onUnmounted, ref, ComponentPublicInstance } from 'vue';

interface UseTouchDirectionOption {
  onSlideDown?: () => void;
  onSlideUp?: () => void;
}

/**
 * 返回一个ref，监听ref元素上的上滑、下滑操作并执行cb
 * @param {onSlideDown, onSlideUp}
 */
export const useTouchDirection = ({ onSlideDown, onSlideUp }: UseTouchDirectionOption) => {
  // 当ref为根元素时类型为Vue实例，当ref为子元素时类型为Element
  const el = ref<Element | ComponentPublicInstance | null>(null);

  let yStart: number | undefined;
  // touchstart回调
  const startCb = (e: TouchEventInit) => {
    yStart = e.touches?.[0]?.clientY;
  };
  // touchend回调
  const endCb = (e: TouchEventInit) => {
    const yEnd = e.changedTouches?.[0]?.clientY;

    if (!yEnd || !yStart) return;

    if (yStart > yEnd + 5) {
      // 上滑
      onSlideUp?.();
    } else if (yStart < yEnd - 5) {
      // 下滑
      onSlideDown?.();
    }
  };

  // 注册事件
  onMounted(() => {
    if (!el.value) return;
    if (el.value instanceof Element) {
      el.value.addEventListener('touchstart', startCb);
      el.value.addEventListener('touchend', endCb);
    } else {
      el.value.$el.addEventListener('touchstart', startCb);
      el.value.$el.addEventListener('touchend', endCb);
    }
  });

  // 销事件
  onUnmounted(() => {
    if (!el.value) return;
    if (el.value instanceof Element) {
      el.value.removeEventListener('touchstart', startCb);
      el.value.removeEventListener('touchend', endCb);
    } else {
      el.value.$el.removeEventListener('touchstart', startCb);
      el.value.$el.removeEventListener('touchend', endCb);
    }
  });

  return el;
};
