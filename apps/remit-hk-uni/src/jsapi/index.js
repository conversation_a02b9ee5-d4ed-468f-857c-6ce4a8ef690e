import wx from 'weixin-js-sdk';
import { CustomException, elk, util } from '@tencent/ppd-base-libs';
import aegis from '@/utils/aegis';
import share from '@/adapters/share/share';
import { MessageScene } from '@tencent/remit-hk-types';

const { to } = util;

let initPromise = null;

function reset() {
  initPromise = null;
}

let jssdkSuccess = false;
/**
 * 判断jssdk加载是否成功
 */
function isJssdkSuccess() {
  return jssdkSuccess === true;
}
/**
 * 设置jssdk成功
 */
function setJssdkSuccess() {
  jssdkSuccess = true;
}

/**
 * jsapi初始化
 * @param  {[type]} wxConfig [description]
 * @return {[type]}          [description]
 */
function init(wxConfig) {
  // 单例promise,确保只调用一次
  // 如果未初始化过，那就初始化一次，如果已经初始化过，那就等待promise的reject或者resolve状态
  if (!initPromise) {
    initPromise = new Promise((resolve, reject) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      let fail = false;
      let isTimeout = false;
      let timeout = null;
      let complete = false;
      const retryLimit = 1;
      let retryTimes = 0;

      const wxConfigInfo = {
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: wxConfig.appId, // 必填，公众号的唯一标识
        timestamp: wxConfig.timestamp, // 必填，生成签名的时间戳，注意这里的参数是timestamp ，小写的s，有些jsapi调用是大写的S
        nonceStr: wxConfig.nonceStr, // 必填，生成签名的随机串
        signature: wxConfig.signature, // 必填，签名
        jsApiList: wxConfig.jsApiList, // 必填，需要使用的JS接口列表
        openTagList: wxConfig.openTagList || [], // 可选，需要使用的开放标签列表，例如['wx-open-launch-app']
      };

      console.log('jsapi init: ', wxConfigInfo);
      const initStartTime = new Date().getTime();

      // jssdk初始化成功回调
      const onReady = function () {
        if (isTimeout) {
          elk({
            key: 'jsapi_ready_timeout',
            timeDiff: new Date().getTime() - initStartTime,
            str1: retryTimes,
          });
          return;
        }

        clearTimeout(timeout);
        complete = true;

        // jssdk失败后，会依次调用wx.error, wx.ready，retryTime > 0
        if (retryTimes > 0) {
          // 上报超时与重试次数
          elk({
            key: 'jsapi_ready_after_retry',
            timeDiff: new Date().getTime() - initStartTime,
            str1: retryTimes,
          });
          aegis.reportEvent('jsapi_ready_after_retry');

          // 如果有重试，按失败处理，因为wx.ready只会触发依次，无法感知到是否重试成功
          reject('jsapi init ready after retry');
        } else {
          // jssdk初始化成功
          setJssdkSuccess();

          // 上报ready耗时
          elk({
            key: 'jsapi_init_ready_timecost',
            timeDiff: new Date().getTime() - initStartTime,
          });
          aegis.reportTime('jsapi_init_ready_timecost', new Date().getTime() - initStartTime);
          resolve(true);
        }
      };

      // jssdk初始化失败回调
      const onError = function (res) {
        // 重试
        if (retryTimes < retryLimit) {
          wx.config(wxConfigInfo);
          // 告警
          new CustomException({ res, wxConfigInfo, retryTimes }, 'jsapi_init_retry');
          // eslint-disable-next-line no-plusplus
          retryTimes++;
          return;
        }

        // if (isTimeout) {
        //   return;
        // }

        clearTimeout(timeout);
        fail = true;
        complete = true;

        // 上报error耗时
        new CustomException({ res, wxConfigInfo, retryTimes }, 'jsapi_init_error');
        elk({
          key: 'jsapi_init_error_timecost',
          msg: res,
          timeDiff: new Date().getTime() - initStartTime,
          str1: retryTimes,
        });
        aegis.reportT({
          name: 'jsapi_init_error_timecost',
          duration: new Date().getTime() - initStartTime,
          ext1: JSON.stringify(res),
        });

        reject(res.errMsg);
      };

      // 初始化jssdk
      wx.config(wxConfigInfo);
      wx.ready(onReady);
      wx.error(onError);

      // 超时处理，在浏览器访问会触发
      timeout = setTimeout(() => {
        if (complete) {
          return;
        }
        isTimeout = true;
        // 超时告警
        new CustomException({ wxConfigInfo, retryTimes }, 'jsapi_init_timeout');
        reject('jsapi init timeout');
      }, 5000);
    });
  }
  return initPromise;
}

/**
 * 获取初始化promise对象
 * @return {tpye} [description]
 */
function getInitPromiseInstance() {
  if (!initPromise) {
    // 后续调用可以不带wxConfig，但是必须有一次init(wxConfig)在最前面
    throw new Error('please invoke init with wxConfig first!');
  }
  return initPromise;
}

/**
 * 拉起支付
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function getH5PrepayRequest(params) {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve, reject) => {
    wx.invoke('getH5PrepayRequest', params, (res) => {
      console.log('getH5PrepayRequest---->', res);
      if (/ok/.test(res.err_msg)) {
        resolve(res);
      } else {
        new CustomException({ res, params }, 'failToInvokePay', '拉起收银台失败');
        reject(res);
      }
    });
  });
}

/**
 * 拉起支付成功页面
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function getH5TransactionRequest(params) {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve, reject) => {
    wx.invoke('getH5TransactionRequest', params, (res) => {
      if (/ok/.test(res.err_msg)) {
        resolve(res);
      } else {
        reject(res);
      }
    });
  });
}

/**
 * 拉起app
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function launchApplication(params) {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve) => {
    wx.invoke('launchApplication', params, (res) => {
      resolve(res);
    });
  });
}

/**
 * 获取设备支持的生物识别方式
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function getSupportSoter() {
  // 这里错了就抛出吧，这样PC上调试可以通过，否则就走不下去了
  // 里面的promise reject,这里await就会报错
  await getInitPromiseInstance();
  // 接口调用返回文档 ：http://x.code.oa.com/ppd-web/doc/blob/master/finger_face_pay.md
  return new Promise((resolve) => {
    wx.invoke('getSupportSoter', {}, (res) => {
      resolve(res);
    });
  });
}

/**
 * 请求SOTER生物认证
 * @param  {[type]} params [description]
 * @return {[type]}        [description]
 */
async function requireSoterBiometricAuthentication(params) {
  // 这里错了就抛出吧，这样PC上调试可以通过，否则就走不下去了
  // 里面的promise reject,这里await就会报错
  await getInitPromiseInstance();
  const data = {
    auth_mode: params.auth_mode,
    challenge: params.challenge,
  };
  // 接口调用返回文档 ：http://x.code.oa.com/ppd-web/doc/blob/master/finger_face_pay.md
  return new Promise((resolve) => {
    wx.invoke('requireSoterBiometricAuthentication', data, (res) => {
      resolve(res);
    });
  });
}

/**
 * 隐藏所有非基础按钮
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function hideAllNonBaseMenuItem() {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve) => {
    wx.invoke('hideAllNonBaseMenuItem', {}, (res) => {
      resolve(res);
    });
  });
}

/**
 * 拉起扫描银行卡
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function scanLicence(param) {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve) => {
    wx.invoke('scanLicence', param, (res) => {
      resolve(res);
    });
  });
}

/**
 * 隐藏底部导航栏
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function hideToolbar() {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve) => {
    wx.invoke('hideToolbar', {}, (res) => {
      resolve(res);
    });
  });
}

/**
 * 隐藏网页右上角按钮
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function hideOptionMenu() {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  wx.hideOptionMenu();
  // 发送一个空的分享消息给小程序用于清空分享配置（无法隐藏分享按钮）
  wx.miniProgram.postMessage({ data: {
    scene: MessageScene.SHARE,
  } });
}

/**
 * 显示网页右上角按钮
 */
async function showOptionMenu() {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve) => {
    wx.invoke('showOptionMenu', {}, (res) => {
      resolve(res);
    });
  });
}

/**
 * 关闭当前webview
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function closeWindow(param = {}) {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  // await to(getInitPromiseInstance());

  return new Promise((resolve, reject) => {
    wx.invoke('closeWindow', param, (res) => {
      console.log('closeWindow---->', res);
      if (/ok/.test(res.err_msg)) {
        resolve(res);
      } else {
        reject(res);
      }
    });
  }).catch((err) => {
    new CustomException(err, 'closeWindow_fail', '关闭底层页面失败');
    // 兜底使用内置jsapi对象调用
    if (typeof window.WeixinJSBridge === 'object' && typeof window.WeixinJSBridge.invoke === 'function') {
      window.WeixinJSBridge.invoke('closeWindow');
    } else {
      new CustomException(null, 'closeWindow_downgrade_fail', '兜底关闭底层页面失败');
    }
  });
}

/**
 * 增加自定义menu
 * @param  {Function} callback [description]
 * @return {[type]}            [description]
 */
async function addCustomMenuItems(params) {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  return new Promise((resolve) => {
    wx.invoke('addCustomMenuItems', params, (res) => {
      resolve(res);
    });
  });
}

/**
 * 通过open-ssh生成客户端公私钥
 * fetch就是取，如果没有
 * @param   {[type]}  type          [save | fetch]
 * @param   {[type]}  walletRegion  [walletRegion description]
 * @return  {[type]}                [return description]
 */
async function handleWCPayWalletBuffer(options) {
  // 这里不处理jsapi.init的错误，无论jsapi.init成功与否，都去尝试调用下面的jsapi
  await to(getInitPromiseInstance());
  const { type, privateKey, publicKey, walletRegion, wxConfigInfo } = options;
  const { appId, signType, paySign, nonceStr } = wxConfigInfo;

  console.log(`jsapi index handleWCPayWalletBuffer: type, privateKey, publicKey, walletRegion , ${JSON.stringify(options)}`);
  // let walletRegion = params.walletRegion || '8';
  const data = {
    appId,
    signType,
    timeStamp: wxConfigInfo.timestamp, // 注意这里的参数是timeStamp ， 大写的S 。 wx.config中的是小写的s
    package: wxConfigInfo.package,
    paySign,
    nonceStr,
    action: type || 'fetch',
    walletRegion,
  };

  if ('save' === type) {
    data.buffer = `${privateKey}^${publicKey}`;
  }

  return new Promise((resolve, reject) => {
    wx.invoke('handleWCPayWalletBuffer', data, (res) => {
      console.log(`handleWCPayWalletBuffer  param: ${JSON.stringify(data)}, res: ${JSON.stringify(res)}`);

      if (/ok/.test(res.err_msg)) {
        let certInfo = null;
        // fetch的时候，才会返回buffer
        if ('fetch' === type) {
          const info = res.buffer.split('^');
          certInfo = {
            privateKey: info[0],
            publicKey: info[1],
          };
        }
        resolve(certInfo);
      } else {
        reject(res);
      }
    });
  });
}

export default {
  reset,
  init,
  getInitPromiseInstance,
  getSupportSoter,
  requireSoterBiometricAuthentication,
  getH5TransactionRequest,
  hideAllNonBaseMenuItem,
  hideToolbar,
  hideOptionMenu,
  closeWindow,
  launchApplication,
  addCustomMenuItems,
  scanLicence,
  handleWCPayWalletBuffer,
  getH5PrepayRequest,
  showOptionMenu,
  isJssdkSuccess,
  wx,
};
