<template>
  <Schedulable
    :scheduler-id="schedulerId"
    :schedulable-id="schedulableId"
    :schedulable-component="dpData[dpPosition]?.componentConf?.compName"
    :check-show="checkShow"
    :on-show="onShowWithReport"
    :on-close="onClose"
    :show-delay="showDelay"
    :close-delay="closeDelay"
    v-bind="{...dpData[dpPosition]?.props, ...$attrs}"
  />
</template>

<script setup lang="ts">
import { SchedulableId } from '@/business/component-scheduler';
import Schedulable from './Schedulable.vue';
import { useRoute } from 'vue-router';
import { useDp } from '@/components/common/dynamic-component/useDp';
import { DP_PAGE } from '@/business/dp-loader/config';
import { submitCheckValue } from '@/business/dp-loader';
import { ReportAction } from '@/business/dp-loader/types';
import { generateTraceId } from '@/business/biHottag';
import { InjectKeys } from '@/types';

type Props = {
  /** 可调度对象id */
  schedulableId: SchedulableId;
  /** 投放位置 */
  dpPosition: string;
  /** 调度器id */
  schedulerId: string;
  /** 前置曝光判断 */
  peerCheckShow?: () => Promise<boolean>;
  /** 调度回调 */
  onShow?: () => void;
  /** 关闭回调 */
  onClose?: () => void;
  /** 曝光延迟，当内部组件需要执行打开动画时可用上 */
  showDelay?: number;
  /** 关闭延迟，当内部组件需要执行关闭动画时可用上 */
  closeDelay?: number;
  /** 是否是动态加载组件 */
  isDynamicComponent?: boolean;
  /**
   * 防止双重滚动
   */
  disableBodyScroll?: boolean;
  /**
   * 隐藏背景
   */
  hideBackground?: boolean;
};


const route = useRoute();

const props = withDefaults(defineProps<Props>(), {
  onClose: () => {},
  onShow: () => {},
  showDelay: 0,
  closeDelay: 0,
  disableBodyScroll: true,
  hideBackground: false,
  peerCheckShow: async () => true,
  isDynamicComponent: false,
});

// traceId 用于abtest
const traceId = generateTraceId();

// 请求投放接口
const { positionMap: dpData, loadingPromise: dpPromise } = useDp({
  dpPage: DP_PAGE[route.name || ''],
  dpPositions: [props.dpPosition],
  traceId,
});

// 代理曝光回调
const onShowWithReport = () => {
  // 投放上报，用于曝光限频
  submitCheckValue([
    {
      dpPosition: props.dpPosition,
      dpId: dpData[props.dpPosition]?.dpId,
      action: ReportAction.CLICK, // ! - 占用"点击"作为曝光限频
    },
  ]);
  // 执行曝光回调
  props.onShow();
};

const checkShow = async () => {
  // 外部定义的前置曝光判断
  const peerCheckShowRes = await props.peerCheckShow();
  if (!peerCheckShowRes) return false;
  // 等待dp接口返回
  await dpPromise;
  // 判断是否有投放计划结果
  const componentConf = dpData[props.dpPosition]?.componentConf;
  // 无结果，不曝光
  if (!componentConf) return false;
  return true;
};

// 依赖注入abt的traceId
provide(InjectKeys.ABTEST, {
  traceId,
});
</script>
