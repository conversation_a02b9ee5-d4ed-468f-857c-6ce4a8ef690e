<template>
  <div class="chart-container">
    <VChart
      ref="chart"
      class="chart"
      :option="option"
      @highlight="onHighLight"
    />
    <hr class="dashed-line">
    <transition name="fade">
      <Loading
        v-if="showLoading"
        class="chart-loading"
      />
    </transition>
  </div>
</template>

<script lang="ts">
import { use } from 'echarts/core';
import { LineChart } from 'echarts/charts';
import VChart from 'vue-echarts';
import 'echarts/lib/component/markLine';
import {
  MarkPointComponent,
  TooltipComponent,
  MarkLineComponent,
  GridComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import type { PropType } from 'vue';
import NP from 'number-precision';
import { useTouchEvent } from './useChartEvent';
import { Loading }  from '@/components/common';


// 屏幕宽度与标准宽度的比值（根据此值做echarts适配，echarts部分单位不支持rem）
const widthRatio = (window.innerWidth || 375) / 375;
(window as any).$shareClickEvent = new CustomEvent('shareRateClick');
(window as any).$onShareRateClick = () => {
  window.dispatchEvent((window as any).$shareClickEvent);
};

use([
  CanvasRenderer,
  LineChart,
  MarkPointComponent,
  GridComponent,
  MarkLineComponent,
  TooltipComponent,
  MarkLineComponent,
]);

type XAxisData = string|{
  value: string,
  textStyle: {
    fontSize: number,
    padding: number[],
    color: string,
    align: 'left' | 'center' | 'right';
  }
};
/**
 * 生成x轴数据
 */
const getXAxisData = (xValList: Date[], formatter: (val) => string): XAxisData[] => xValList.map((xData, index) => {
  const baseTextStyle = {
    fontSize: Math.round(9 * widthRatio),
    padding: [Math.round(16 * widthRatio), 0, 0, 0],
    color: 'rgba(0, 0, 0, 0.4)',
  };
  const value = formatter(xData);
  // 左侧日期
  if (index === 0) {
    return { value, textStyle: { ...baseTextStyle, align: 'left' } };
  }
  // 左侧日期
  if (index === Math.floor(xValList.length / 2)) {
    return { value, textStyle: { ...baseTextStyle, align: 'center' } };
  }
  // 右侧日期
  if (index === xValList.length - 1) {
    return { value, textStyle: { ...baseTextStyle, align: 'right' } };
  }
  // 其余日期不展示
  return '';
});

/**
 * 生成坐标点数据
 */
const getSeriesData = (data: number[]) => data.map(rate => ({ value: rate.toFixed(4) }));

export default defineComponent({
  name: 'RateChart',
  components: {
    VChart,
    Loading,
  },
  props: {
    // y轴数据组，可包含多条线数据
    yAxisDataGroup: {
      type: Array as PropType<{key: string, data: number[]}[]>,
      required: true,
      default: () => ([]),
    },
    // x轴数据
    xAxisData: {
      type: Array as PropType<Date[]>,
      required: true,
      default: () => ([]),
    },
    // 曲线配置
    seriesConfig: {
      type: Array as PropType<{
        key: string,
        config: Record<string, any>,
      }[]>,
      required: true,
      default: () => ([]),
    },
    // 高亮虚线颜色
    axisPointerLineColor: {
      type: String,
      required: true,
      default: () => ('#0CBD6A'),
    },
    // x轴数据格式化
    xAxisDataFormatter: {
      type: Function as PropType<(val: Date) => string>,
      required: false,
      default: () => (val: string) => val,
    },
    // 是否展示loading
    showLoading: {
      type: Boolean,
      required: false,
      default: false,
    },
    // 配置格式化函数，用于外部传入自定义配置处理逻辑
    configFormatter: {
      type: Function as PropType<(config: import('echarts/types/dist/shared').EChartsOption) => import('echarts/types/dist/shared').EChartsOption>,
      default: config => config,
    },
  },
  emits: ['onSeriesHighLight', 'touchstart', 'touchmove', 'touchend', 'showTip', 'hideTip', 'chartClick'],
  setup(props, { emit }) {
    // echart实例
    const { chart } = useTouchEvent(emit);

    // 设置点击高亮的数据点
    const onHighLight = (params) => {
      const index = params?.batch?.[0]?.dataIndex;
      if (index === undefined) return;
      emit('onSeriesHighLight', index);
    };

    // echart配置
    const option = computed(() => {
      const allDataNestedArr = props.yAxisDataGroup.map(series => series.data);
      const yMin = +Math.min(...([] as number[]).concat(...allDataNestedArr)).toFixed(4); // y轴刻度最小值
      const yMax = +Math.max(...([] as number[]).concat(...allDataNestedArr)).toFixed(4); // y轴刻度最大值
      // const diff = NP.minus(yMax, yMin);
      // const avg = +NP.divide(NP.plus(yMax, yMin), 2).toFixed(4);
      // const yMinExtra = NP.minus(yMin, NP.divide(diff, 8));
      // const yMaxExtra = NP.plus(yMax, NP.divide(diff, 8));

      return props.configFormatter({
        grid: {
          top: '11%',
          left: 'left',
          right: '0%',
          bottom: '14%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          hideDelay: 0,
          confine: false,
          position(pt, params, dom, rect, size) {
            return [pt[0] - size.contentSize[0] / 2, '-5%'];
          },

          transitionDuration: 0,
          padding: [Math.round(6 * widthRatio), Math.round(12 * widthRatio)],
          backgroundColor: 'rgba(0, 0, 0, 0.72)',
          borderColor: 'white',
          textStyle: {
            color: 'white',
            fontSize: Math.round(10 * widthRatio),
          },
          extraCssText: 'box-shadow: none;',
          borderWidth: 0,
          formatter(params) {
            return props.xAxisDataFormatter(props.xAxisData[params[0].dataIndex]);
          },
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLine: {
            show: false,
          },
          axisTick: { show: false },
          data: getXAxisData(props.xAxisData, props.xAxisDataFormatter),
          axisLabel: {
            interval: 0,
          },
          axisPointer: {
            show: true,
            type: 'line',
            snap: true,
            lineStyle: {
              color: props.axisPointerLineColor,
              type: [2, 2],
            },
            label: { show: false },
            z: -1,
            animation: false,
          },
        },
        ],
        yAxis: {
          type: 'value',
          scale: true,
          min: yMin, // y轴刻度最小值
          max: yMax, // y轴刻度最大值
          // interval: 0.0001, // 根据最大最小
          interval: NP.divide(NP.minus(yMax, yMin), 2), // 根据最大最小
          splitLine: {
            show: false,
          },
          axisLabel: {
            // formatter(value) {
            //   const formatedVal = value.toFixed(4);
            //   if (+formatedVal === yMax || +formatedVal === yMin || +formatedVal === avg) {
            //     console.warn(formatedVal);
            //     return formatedVal;
            //   }
            //   return '';
            // },
            formatter(value) {
              return value.toFixed(4);
            },
            color: 'rgba(0, 0, 0, 0.4)',
            fontSize: Math.round(9 * widthRatio),
            hideOverlap: false,
          },
        },
        // 没时间处理这种类型问题
        // @ts-ignore
        series: [
          ...props.yAxisDataGroup.map(series => ({
            type: 'line',
            symbol: 'circle',
            symbolSize: Math.round(7 * widthRatio),
            showSymbol: false,
            ...props.seriesConfig.find(conf => conf.key === series.key)?.config,
            data: getSeriesData(series.data),
          })),
        ],
      });
    });

    return {
      option,
      onHighLight,
      chart,
    };
  },
});
</script>
<style lang="less" scoped>
@import "~@/less/main.less";
.chart,
.chart-container {
  height: 100%;// 避免canvas获取不到高度
  // height: 478 / @rem;
  width: 474 / @rem;
}
.chart-container {
  position: relative;
}
.dashed-line {
  border: 1/@rem dashed rgba(0, 0, 0, 0.08);
  border-bottom: none;
  width: 398/@rem;
  top: 383/@rem;
  left: 72/@rem;
  position: absolute;
}
.chart-loading {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 66.67/@rem;
}
:deep(.rate-share-tooltip) {
  background: rgba(219, 182, 154, 0.72) !important;
  // background-color: unset !important;
  color: white !important;
  font-size: 20/@rem !important;
  line-height: 24/@rem !important;
  font-weight: 600 !important;
  width: 304/@rem !important;
  padding: 8/@rem 16/@rem !important;
  backdrop-filter: blur(5px) !important;
}
:deep(.rate-share-icon) {
  height: 24/@rem;
  width: 24/@rem;
}
</style>
