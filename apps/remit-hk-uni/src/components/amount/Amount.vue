<template>
  <BaseAmount
    :type="amtType"
    :prefix="curUnit.prefix"
    :suffix="curUnit.suffix"
    :amt="amt"
    :format-int="formatInt"
    :currency="currency"
  />
</template>

<script setup lang="ts">
import BaseAmount from './Base.vue';
import { CURRENCY } from '@/types';

interface Props {
  amt?: string;
  currency?: CURRENCY;
  formatter?: (str: string) => string;
  type?: 'prefix' | 'suffix';
  amtType?: string;
  formatInt?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  amt: undefined,
  currency: undefined,
  formatter: undefined,
  type: 'prefix',
  amtType: 'normal',
  formatInt: false,
});

const unitMap = {
  prefix: {
    [CURRENCY.HKD]: 'HK$',
    [CURRENCY.CNY]: '¥',
  },
  suffix: {
    [CURRENCY.CNY]: 'CNY',
    [CURRENCY.HKD]: 'HKD',
  },
};

const curUnit = computed(() => {
  const unitConfig = unitMap[props.type];
  const text = unitConfig[props.currency];
  const formatted = typeof props.formatter === 'function' ? props.formatter(text) : text;
  return {
    prefix: props.type === 'prefix' ? formatted : '',
    suffix: props.type === 'suffix' ? formatted : '',
  };
});
</script>

<style lang="less">

</style>
