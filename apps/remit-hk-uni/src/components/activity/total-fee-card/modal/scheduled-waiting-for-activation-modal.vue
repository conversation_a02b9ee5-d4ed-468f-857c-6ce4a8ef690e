<template>
  <Schedulable
    :scheduler-id="schedulerId"
    :schedulable-id="schedulableId"
    :check-show="checkShow"
    :on-show="onShow"
    :on-close="onClose"
    :schedulable-component="defineAsyncComponent(
      () => import('@/components/activity/total-fee-card/modal/waiting-for-activation-modal.vue')
    )"
    :show-delay="200"
    :close-delay="300"
    :disable-body-scroll="true"
  />
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue';
import { SchedulableIdPaySuccessFinish, SchedulerId } from '@/business/component-scheduler';
import Schedulable from '@/components/common/Schedulable.vue';
import orderModule from '@/store/order';
// 判断订单中是否有手续费减免卡
const isCardOrder = computed(() => orderModule.isCardOrder);

// 调度对象配置
const schedulerId = SchedulerId.PAYSUCCESS_FINISH;
const schedulableId = SchedulableIdPaySuccessFinish.TOTAL_FEE_CARD_MODAL;
// 调度判断回调函数
const checkShow = async () => isCardOrder.value;

const onShow = () => {
};
// 销毁时恢复首页分享
const onClose = () => {
};
</script>
