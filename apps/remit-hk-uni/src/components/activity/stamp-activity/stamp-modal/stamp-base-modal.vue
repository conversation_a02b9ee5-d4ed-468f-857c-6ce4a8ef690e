<!-- 活动弹窗组件，包含弹窗阴影、主体插槽以及按钮组 -->

<template>
  <FitHalfScreenDialog
    :title="$t('')"
    :desc="$t('')"
    :visible="modalVisible"
    :mask-close="true"
    :append-to-body="true"
    @on-close="closeModal"
  >
    <template #bd>
      <slot
        name="bd"
        class="modal-bd"
      />
    </template>

    <template #ft>
      <slot
        name="ft"
        class="modal-ft"
      />
    </template>
  </FitHalfScreenDialog>
</template>

<script setup lang="ts">
import { FitHalfScreenDialog } from '@/fit-ui';

const emit = defineEmits<(e: 'closeModal') => void>();

export interface ActivityModalProps {
  /**
   * 弹层可视标志位
   */
  modalVisible: boolean;

};

defineProps<ActivityModalProps>();

const closeModal = () => {
  emit('closeModal');
};
</script>

<style scoped lang="less">
  @import "~@/less/main.less";
  .modal-bd{
    width: 100%;
    height: 100%;
  }

  // 半弹层样式
  :deep(.weui-half-screen-dialog) {

    .weui-icon-btn_close {
      transform: translate(-36/@rem,-12/@rem);
      background-image: url('//st.moneydata.hk/res/fmd_mojo_static/2nbV4Ou10UPiRSbtmkaaiF8YzI937jiq.png');
    }

    .weui-half-screen-dialog-mixin();
    background: url('//st.moneydata.hk/res/fmd_mojo_static/LXCAOfkAPTxAE7cIZ50ELO5oI8V2WFd6.png'),
    linear-gradient(34.75deg, #F8FFD3 5.19%, #D8FFF8 80.59%);
    background-size: 100% auto;
    background-repeat: no-repeat;
    border-top-right-radius: 38.4/@rem !important;
    border-top-left-radius: 38.4/@rem !important;
    padding: 0!important;
  }

  :deep(.weui-half-screen-dialog__bd){
    overflow-y: visible!important;;
  }

  :deep(.weui-half-screen-dialog__ft){
    padding-top: 0!important;
  }
</style>
