<template>
  <transition
    name="fade"
    @after-enter="toggleInteract"
    @before-leave="toggleInteract"
  >
    <div
      v-show="dialogVisible"
      ref="modal"
      class="intercept"
      @click="closeDialog"
    >
      <div
        class="intercept-container"
        @click.stop
      >
        <div class="intercept-content">
          <div class="intercept-content-title">
            你尚未獲取匯率優惠！
          </div>
          <!-- 用户输入港币时 -->
          <div
            v-if="isSellOut"
            class="intercept-content-tip"
          >
            獲取優惠後, 可少支付 <span>HK${{ promAmt }}</span>
          </div>
          <!-- 用户输入人民币时 -->
          <div
            v-else
            class="intercept-content-tip"
          >
            獲取優惠後, 對方多收 <span>{{ promAmt }}CNY</span>
          </div>
          <!-- 用户输入港币时 -->
          <div
            v-if="isSellOut"
            class="intercept-content-detail"
          >
            <div class="intercept-content-detail-title">
              預計付款金額
            </div>
            <div class="intercept-content-detail-number">
              HK${{ referAmt }}
            </div>
            <div class="intercept-content-detail-number-referAmt">
              HK${{ amount }}
            </div>
          </div>
          <!-- 用户输入人民币时 -->
          <div
            v-else
            class="intercept-content-detailCNY"
          >
            <div class="intercept-content-detailCNY-title">
              对方收到的人民币
            </div>
            <div class="intercept-content-detailCNY-number">
              <div>
                {{ amount }}<div class="intercept-content-detailCNY-number-prom">
                  + {{ promAmt }}
                </div>
              </div>
              <div class="intercept-content-detailCNY-number-tag">
                <img
                  class="intercept-content-detailCNY-number-tag-icon"
                  src="//st.moneydata.hk/res/fmd_mojo_static/yKQ4KXeVDpp8dRyglI4Hgve3Szv4I0IT.png"
                >
                <span>
                  CNY
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="intercept-container-buttons">
          <div
            class="intercept-container-buttons-button intercept-container-buttons-thanks"
            @click="closeDialog"
          >
            暫不需要
          </div>
          <div
            class="intercept-container-buttons-button intercept-container-buttons-use"
            @click.stop="goActivityPage"
          >
            獲取優惠
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>
<script lang="ts">
import { CustomException } from '@tencent/ppd-base-libs';
import { RemitWay, TransType } from '@/types'; // 转换类型
import rateActivityThreeModule, { RateCardThreeFrom } from '@/store/rate-activity-three';
import activityCenterModule from '@/store/activity-center';
import rateModule from '@/store/rate';
import { biHottag } from '@/business/biHottag';
import { useAppendToBody } from '@/hooks/common/useAppendToBody';
import { useBizCurrencyInputGroup } from '@/views/home-root/home/<USER>/currency/useBizCurrencyInputGroup';
import { referAmount, promAmount } from '@/business/amount';
import NP from 'number-precision';
import router, { ROUTE } from '@/router';


export default defineComponent({
  name: 'RateInterceptV2',
  components: {},
  props: {
    dialogVisible: { type: Boolean, default: false },
  },
  setup(props) {
    const {
      focusAmount,
      lastAmountInfoBox,
    } = useBizCurrencyInputGroup();

    // 当前交易类型的金额
    const amount = computed(() => focusAmount.value);

    // 是否汇出港币（用户输入港币时）
    const isSellOut = computed(() => lastAmountInfoBox.value?.transType === TransType.SELL_OUT);

    // 此处有两种情况
    // 当用户未享受汇率提升效果时，referRate非空且为优惠后汇率，将其返回
    // 当用户正在享受汇率提升效果时，referRate为空，rate就是优惠后的汇率
    const referRate = computed(() => rateModule.referRate || rateModule.rate);

    // 优惠前汇率
    const beforeRate = computed(() => NP.minus(referRate.value, 0.005).toFixed(4));

    // 优惠后当前交易类型的金额
    const referAmt = computed(() => referAmount(
      focusAmount.value,
      `${NP.times(beforeRate.value, 10 ** 8)}`,
      `${NP.times(referRate.value, 10 ** 8)}`,
      lastAmountInfoBox.value?.transType || TransType.SELL_OUT,
    ).toFixed(2));

    // 当前交易类型的金额优惠金额
    const promAmt = computed(() => promAmount(
      focusAmount.value,
      `${NP.times(beforeRate.value, 10 ** 8)}`,
      `${NP.times(referRate.value, 10 ** 8)}`,
      lastAmountInfoBox.value?.transType || TransType.SELL_OUT,
    ).toFixed(2));

    /**
     * 标签类型 - 事件上报用
     * 1 = 用户已获得二期汇率提升卡，尚未获取出粮日优惠
     * 2 = 用户已获得二期汇率提升卡，已经获取出粮日优惠
     * 3 =（老用户）用户不处于汇率提升卡生效期内
     * 4 =（新用户）用户不处于汇率提升卡生效期内
     * 5 = 用户未获得二期汇率提升卡，已经获取出粮日优惠
     */
    const tagType = computed(() => {
    // 没有享受汇率提升卡
      if (!activityCenterModule.hasRateCard) {
        if (rateModule.hasRateDiscount) {
        // 没有享受汇率提升卡，但正在享受汇率优惠
          return '4';
        }
        return '3';
      }
      // 如果正在享受汇率提升卡
      if (!rateActivityThreeModule.hasCurRecv) {
      // 如果正在享受汇率提升卡，但没领取三期汇率权益（只领取了二期权益）
        return '1';
      }
      if (rateActivityThreeModule.isOnlyHasThreeRecv) {
        return '5';
      }
      return '2';
    });

    /**
     * 弹窗类型 - 事件上报用
     * 1 = 固定输入港币框金额
     * 2 = 固定输入人民币框金额
     */
    const dialogType = computed(() => (isSellOut.value ? '1' : '2'));

    // 判断是否可交互<动画过程中不可交互>
    const interactable = ref<boolean>(false);


    watch(() => props.dialogVisible, () => {
      if (props.dialogVisible) {
      // 如果曝光时，已获得汇率提升卡
        if (activityCenterModule.hasRateCard) {
        // 异常事件 - 已获得汇率提升卡，却触发了拦截弹窗
          new CustomException(tagType.value, 'rateInterCeptError_hasRateCard', '已获得汇率提升卡，但触发拦截弹窗');
        }
        // 如果曝光时，不在三期有效期
        if (!rateActivityThreeModule.inRateActivityThree) {
        // 异常事件 - 不在三期有效期，却触发了拦截弹窗
          new CustomException(null, 'rateInterCeptError_notInRateActivityThree', '不在三期有效期，但触发拦截弹窗');
        }
        // 上报事件 - 跨境汇款.汇率权益卡活动三期.首页.点击下一步时的拦截弹窗展示
        biHottag('newhk2cn.h5_rate_promote3.home.activity_block_popup_window_brow', {
          tagType: tagType.value,
          dialogType: dialogType.value,
          ...rateActivityThreeModule.hottagValue,
        });
      }
    });

    // 挂载到body
    return {
      referAmt,
      promAmt,
      amount,
      isSellOut,
      modal: useAppendToBody(),
      // 关闭拦截弹窗
      async closeDialog() {
        // 当动画结束后才可以操作
        if (!interactable.value) {
          return;
        }
        // 上报事件 - 跨境汇款.汇率权益卡活动三期.首页.点击下一步时的拦截弹窗.点击取消
        biHottag('newhk2cn.h5_rate_promote3.home.activity_block_popup_window_close_click', {
          tagType: tagType.value,
          dialogType: dialogType.value,
          ...rateActivityThreeModule.hottagValue,
        });
        rateActivityThreeModule.setRateInterceptVisible(false);  // 关闭弹窗

        // 现在没有补充信息页 直接到选择汇款方式页
        router.push({
          name: ROUTE.CHOOSE_REMIT_WAY,
          params: {
            remitWay: RemitWay.SHARE,
          },
        });
      },
      // 跳转到三期活动详情页
      goActivityPage() {
        rateActivityThreeModule.setRateInterceptVisible(false);  // 关闭弹窗

        // 上报事件 - 跨境汇款.汇率权益卡活动三期.首页.点击下一步时的拦截弹窗.点击立即使用
        biHottag('newhk2cn.h5_rate_promote3.home.activity_block_popup_window_obtain_click', {
          tagType: tagType.value,
          dialogType: dialogType.value,
          ...rateActivityThreeModule.hottagValue,
        });

        // 进入三期活动详情页
        rateActivityThreeModule.goActivityPage(RateCardThreeFrom.RATE_CARD_THREE_INTERCEPT);
      },
      /**
       * 是否可操作的 flag
       *
       * 仅在 <开始动画结束后> 以及 <结束动画开始前> 可以操作点击事件
       */
      toggleInteract() {
        interactable.value = !interactable.value;
      },
    };
  },
});
</script>
<style scoped lang="less">
@import "~@/less/main.less";
// 遮罩层动画
.fade-enter-active, .fade-leave-active {
  .intercept-container{
    transition: transform 0.15s ease-in-out;
  }
}
.fade-enter-from, .fade-leave-to {
  .intercept-container{
    transform: scale(0);
  }
}
.intercept {
  background: rgba(0,0,0, 0.8);
  position: fixed;
  width: 100vw;
  height: 100vh;
  color: #000;
  top: 0;
  left: 0;
  z-index: @zModal;
  transition: transform 0.3s ease-in-out;
  display: flex;
  align-items: center;
  flex-direction: column;
  &-content{
    flex: 1;
    padding: 56 / @rem 48 / @rem 0;
    margin-bottom: 56 / @rem;
    &-title{
      font-size: 36 / @rem;
      line-height: 48 / @rem;
      font-weight: 500;
    }
    &-tip{
      font-size: 36 / @rem;
      line-height: 48 / @rem;
      font-weight: 500;
      span {
        color: #FF931F;
        font-family: WeChatSansSS-Medium;
        font-weight: 500;
      }
    }
    &-detail{
      margin-top: 24 / @rem;
      padding: 32 / @rem 0 0 40 / @rem;
      box-sizing: border-box;
      height: 220 / @rem;
      background-color: rgba(0,0,0, 0.04);
      &-title{
        font-size: 28 / @rem;
        line-height: 40 / @rem;
        color: rgba(0,0,0, 0.56);
      }
      &-number{
        margin-top: 16 / @rem;
        font-family: WeChatSansSS-Medium;
        font-size: 56 / @rem;
        line-height: 72 / @rem;
        &-referAmt{
          margin-top: 4 / @rem;
          padding-left: 3 / @rem;
          font-size: 28 / @rem;
          line-height: 32 / @rem;
          color: rgba(0,0,0, 0.24);
          text-decoration: line-through;
        }
      }
    }
    &-detailCNY{
      margin-top: 24 / @rem;
      padding: 32 / @rem 0 0 32 / @rem;
      box-sizing: border-box;
      height: 184 / @rem;
      background-color: rgba(0,0,0, 0.04);
      &-title{
        font-size: 28 / @rem;
        line-height: 40 / @rem;
        color: rgba(0,0,0, 0.56);
      }
      &-number{
        margin-top: 16 / @rem;
        font-family: WeChatSansSS-Medium;
        font-size: 56 / @rem;
        line-height: 72 / @rem;
        display: flex;
        justify-content: space-between;
        &-prom{
          margin-left: 8 / @rem;
          color: #FF931F;
          font-size: 28 / @rem;
          letter-spacing: -0.88 / @rem;
          line-height: 40 / @rem;
          height: 40 / @rem;
          display: inline-block;
          transform: translateY(-2 / @rem);
        }
        &-tag{
          padding-right: 32 / @rem;
          display: flex;
          align-items: center;
          &-icon{
            width: 50 / @rem;
            height: 50 / @rem;
            border-radius: 25 / @rem;
          }
          span {
            margin-left: 12 / @rem;
            font-size: 32 / @rem;
            font-family: "WeChatSansSS-Regular";
            line-height: 48 / @rem;
          }
        }
      }
    }
  }
  &-container{
    margin-top: 272 / @rem;
    width: 640 / @rem;
    border-radius: 24 / @rem;
    background-color: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    &-buttons{
      width: 100%;
      height: 100 / @rem;
      border-top: 1px solid rgba(0, 0, 0, 0.08);
      display: flex;
      &-button{
        width: 50%;
        height: 100%;
        font-size: 32 / @rem;
        line-height: 46 / @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      &-thanks{
        color: rgba(0,0,0, 0.56);
        border-right: 1px solid rgba(0, 0, 0, 0.08);
      }
      &-use{
        font-size: 34 / @rem;
        line-height: 48 / @rem;
        color: #07C160;
        font-weight: 500;
      }
    }
  }
}
</style>
