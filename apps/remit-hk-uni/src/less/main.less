@import "./flex.less";
@import "./layout.less";
@import "./zindex.less";
@import "./shape.less";

@rem: 32rem;
@green:#07c160;
@grey: rgba(0, 0, 0, 0.56);
@red:#f3363b;
@orange:#F58C23;
@blue:#3890CD;

body{
  // ios禁用缩放
  -webkit-text-size-adjust: 100%; /* 2 */
}

.g-text-56() {
  font-size: 56 / @rem;
  line-height: 72 / @rem;
}
.g-text-48() {
  font-size: 48 / @rem;
  line-height: 64 / @rem;
}
.g-text-44() {
  font-size: 44 / @rem;
  line-height: 56 / @rem;
}
.g-text-40() {
  font-size: 40 / @rem;
  line-height: 48 / @rem;
}
.g-text-36() {
  font-size: 36 / @rem;
  line-height: 50 / @rem;
}
.g-text-34() {
  font-size: 34 / @rem;
  line-height: 48 / @rem;
}
.g-text-32() {
  font-size: 32 / @rem;
  line-height: 44 / @rem;
}
.g-text-28() {
  font-size: 28 / @rem;
  line-height: 38 / @rem;
}
.g-text-24() {
  font-size: 24 / @rem;
  line-height: 32 / @rem;
}
.g-text-20() {
  font-size: 20 / @rem;
  line-height: 24 / @rem;
}
.g-opacity-88() {
  color: rgba(0, 0, 0, 0.88);
}

.g-opacity-56() {
  color: rgba(0, 0, 0, 0.56);
}

.g-opacity-24() {
  color: rgba(0, 0, 0, 0.24);
}

.g-layout-between() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.g-layout-left() {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.g-layout-center() {
  display: flex;
  justify-content: center;
  align-items: center;
}

.g-color-green {
  color: @green;
}
.g-color-grey {
  color: @grey;
}
.g-color-red {
  color: @red;
}
.g-color-orange{
  color:@orange;
}
/*  */
.g-divide-bottom() {
  position: relative;
  &::after {
    content: "";
    left: 0;
    bottom:0;
    position: absolute;
    width: 100%;
    height: 1px;
    background: rgba(0, 0, 0, 0.08);
    transform: scaleY(0.5);
  }
}

.g-press(){
  position: relative;
  &::before{
    content: '';
    position: absolute;
    background:rgba(0, 0, 0, 0.05);
    opacity: 0;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    pointer-events: none;
    transition: opacity 100ms ;
  }
  &:active{
    &::before{
      content: '';
      opacity: 1;
    }
  }
}

.g-divide-top() {
  position: relative;
  &::after {
    content: "";
    left: 0;
    top:0;
    position: absolute;
    width: 100%;
    height: 1px;
    background: rgba(0, 0, 0, 0.08);
    transform: scaleY(0.5);
  }
}

.g-triangle-right{
  font-size: 28/@rem;
}
.g-triangle-down{
  font-size: 28/@rem;
  transform-origin: 50% 50%;
  transform: rotate(90deg) translateX(-16%);
}

.g-bg-grey(){
  background: #EDEDED;
}


:deep(.fit-info-box){
  &.line{
    .fit-info-box-line{
      &:first-child{
        .g-divide-top();
      }
    }
  }
  .fit-info-box-line-left {
    .g-opacity-24();
  }
  .fit-info-box-line {
    .g-text-28();
    padding-bottom: 24 / @rem;
    &:last-child{
      padding-bottom: 32 / @rem;
    }
  }
  .fit-info-box-line-right {
    .g-opacity-56();
  }
  .fit-info-box-inner {
    padding: 0 / @rem 32 / @rem 0 / @rem 32 / @rem;
  }
  .fit-info-box-line{
    &:first-child{
      padding-top: 32/@rem;
    }
  }
  &+.fit-info-box{
    .fit-info-box-line{
      &:first-child{
        .g-divide-top();
      }
    }
  }
}

.remit-button-green {
  background-color: @green;
  color: #fff;
  border: none;
  font-size: 32/@rem;
}
.remit-button-green.weui-btn_disabled {
  background-color: rgba(7, 193, 96, 0.24);
}

/**
大按钮
**/
:deep(.weui-btn-size_big) {
  display: block;
  width: 686/@rem;
  height: 88/@rem;
  font-size: 32/@rem;
  line-height: 88/@rem;
  padding: 0;
  border-radius: 8/@rem;
}

:deep(.fit-msg){
  .weui-icon {
    font-size: 168 / @rem;
  }
  .fit-msg-hd {
    padding-top: 152 / @rem;
  }
  .fit-msg-hd-title {
    margin-top: 64 / @rem;
  }
  .fit-msg-bd{
    padding-top: 64 / @rem;
  }
  .fit-msg-ft{
    .weui-btn{
      margin-bottom: 48/@rem;
    }
  }
  .fit-msg-hd-desc{
    .g-text-28();
    .g-opacity-56();
    margin-top: 16/@rem;
  }
}

// tooltips会改变body的position为relative 之前fit-nation用的是absolute
:deep(.fit-nation-inner){
  position: fixed;
}


:deep(.fit-form-item-content-error){
  .g-text-24();
}



:deep(.fit-select),
:deep(.fit-multi-select){
  .weui-half-screen-dialog{
    padding-left: 32/@rem;
    padding-right: 32/@rem;
    .weui-half-screen-dialog-mixin();
    .weui-cell__ft {
      transform: translate(-20/@rem, -2/@rem);
      .fit-icon-tick {
        font-size: 40/@rem !important;
      }
    }
    &__bd {
      padding-bottom: 60/@rem;
    }
    &__hd {
      margin-bottom: 32/@rem !important;
    }
    &__hd__side {
      margin-right: 48/@rem !important;
    }
    &__ft {
      display: none;
    }
  }
  .fit-select-item {
    height: 120 / @rem;
    box-sizing: border-box;
  }
  .weui-cells {
    &::before {
      display: none;
    }
    .weui-cell {
      &::before {
        border-color: rgba(0, 0, 0, 0.08)
      }
    }
  }
  .weui-half-screen-dialog .weui-half-screen-dialog__hd{
    line-height: normal!important;
    height: auto!important;
  }
  .weui-half-screen-dialog__hd{
    min-height: 128/@rem;
    box-sizing: border-box;
    overflow-x: visible;
    .g-divide-bottom();
    &::after{
      width: 200%;
      transform: scaleY(0.5) translateX(-32/@rem);
    }
  }
  .weui-half-screen-dialog__ft{
    font-size: 0;
  }
}


:deep(.fit-check-box){
  width:28/@rem;
  height:28/@rem;
  margin-right:16/@rem;
  margin-top: -0.03*200/@rem;
  &:before{
    border-color: rgba(0, 0, 0, 0.56);
    border-width:6/@rem;
    box-sizing: border-box;
  }
  .fit-icon-tick{
     transform: translate(-47%, -50%);
    #Toast{
      stroke:rgba(0, 0, 0, 0.56);
      stroke-width: 8px;
      fill:rgba(0, 0, 0, 0.56);
    }
  }
}

:deep(.fit-form-box){
  &:after{
    display: none;
  }
}
:deep(.fit-form-item-box){
  position: relative;
  .g-text-32()!important;
  &::after{
    left: 0!important;
  }
}
:deep(.fit-form-item-label){
  padding-right:48/@rem;
}
:deep(.fit-form-item-content-error .svg-icon){
  display: none;
}
:deep(.fit-form-item:last-child .fit-form-item-box:after){
  left: 30/@rem;
}


/**
标签
**/
.tag {
  background: #f0fcf0;
  border-radius: 4 / @rem;
  color: #07C160;
  padding: 4/@rem 10/@rem;
  margin-right: 10/@rem;
  margin-left: 10/@rem;
  vertical-align: middle;
  .g-text-20();
}
.dark{
  .g-opacity-88();
}

// 全局渐变动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

// 拉新邀请活动
.invite-primary-color {
  color: #1FDE72;
}

// 按钮新版交互样式 mixin
.weui-btn-mixin() {
  height: 88 / @rem;
  width: 432 / @rem;
  font-weight: 600;
  font-size: 34 / @rem;
  line-height: 48 / @rem;
  background: #0CBD6A;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  padding: 20/@rem 64/@rem;
  gap: 10/@rem;
}

// 半弹层新版交互样式 mixin
.weui-half-screen-dialog-mixin() {
  padding-left: 40 / @rem !important;
  padding-right: 40 / @rem !important;
  padding-bottom: 64 / @rem !important;
  border-top-right-radius: 36 / @rem;
  border-top-left-radius: 36 / @rem;
  &__hd {
    margin-bottom: 24 / @rem !important;
    display: flex !important;
    flex-direction: row-reverse !important;
    background: transparent;
    &::after {
      display: none;
    }
  }
  &__hd__main {
    text-align: left !important;
    padding-left: 0 !important;
  }
  &__hd__side {
    margin-right: 60 / @rem;
    margin-top: 32 / @rem;
  }
  &__hd__desc {
    font-family: PingFangSC-Regular;
    font-size: 28 / @rem;
    line-height: 40 / @rem;
    color: rgba(0, 0, 0, 0.4);
    transform: translateY(-32 / @rem);
    padding-left: 7 / @rem;
  }
  &__title {
    padding-left: 7 / @rem;
    padding-top: 36 / @rem;
    font-size: 40 / @rem;
    line-height: 56 / @rem;
    color: rgba(0, 0, 0, 0.88);
  }
  .weui-icon-btn_close {
    width: 42 / @rem;
    margin-left: 10 / @rem;
  }
}

// 按钮新版交互样式 mixin
.weui-switch-mixin() {
  /deep/ .weui-switch-cp__input:checked ~ .weui-switch-cp__box:after {
    transform: translate(30/@rem, 2/@rem);
  }
  /deep/ .weui-switch-cp__box {
    width: 76 / @rem;
    height: 48 / @rem;
    border: none;
    &::before {
      width: 76 / @rem;
      height: 48 / @rem;
      background-color: #D6D6D6;
    }
    &::after {
      box-shadow: none;
      transform: translate(2/@rem, 2/@rem);
      width: 44 / @rem;
      height: 44 / @rem;
    }
  }
}
.weui-switch-mixin-nodeep() {
  .weui-switch-cp__input:checked ~ .weui-switch-cp__box:after {
    transform: translate(30/@rem, 2/@rem);
  }
  .weui-switch-cp__box {
    width: 76 / @rem;
    height: 48 / @rem;
    border: none;
    &::before {
      width: 76 / @rem;
      height: 48 / @rem;
      background-color: #D6D6D6;
    }
    &::after {
      box-shadow: none;
      transform: translate(2/@rem, 2/@rem);
      width: 44 / @rem;
      height: 44 / @rem;
    }
  }
}

.tool-tip-mixin() {
  box-sizing: border-box;
  position: absolute;
  padding: 32/@rem 40/@rem;
  width: 544/@rem;
  left: -224/@rem;
  top: 48/@rem;
  z-index: @above;
  border-radius: 8 / @rem;
  background: transparent;
  &::before{
    display: none;
  }
  .tool-tip-container {
    background: rgba(0, 0, 0, 1);
    border-radius: 8 / @rem;
    opacity: 0.72;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    &::before{
      content: '';
      display:block;
      width:0;
      height:0;
      position:absolute;
      border-top: 10/@rem solid transparent;
      border-bottom: 10/@rem solid rgba(0, 0, 0, 1);
      border-right: 10/@rem solid transparent;
      border-left: 10/@rem solid transparent;
      left: 240.5 / @rem;
      top: -19 / @rem;
    }
  }
  p {
    text-align: center;
    color: white;
    font-family: PingFangSC-Regular;
    font-weight: 600;
    font-size: 28 / @rem;
    line-height: 40 / @rem;
    text-align: left;
  }
}

.set-arrow(@color:rgba(0, 0, 0, 0.24)){
  border: solid @color;
  border-width: 0 2.6/@rem 2.6/@rem 0;
  display: inline-block;
  padding: 6.5/@rem;
  vertical-align: middle;
  margin: 2/@rem 14/@rem;
  margin-bottom: 2.8/@rem;
  transform: rotate(-45deg);
}



// 半弹层设置最大高度
.weui-half-screen-dialog__bd{
  @maxHeight: 200/@rem;
  max-height: calc(~"100vh - " @maxHeight - env(safe-area-inset-bottom));
}