import activityCenterModule, { ActCenterFrom } from '@/store/activity-center';
import homeModule from '@/store/home';
import { computed } from 'vue';
import { useRateActThree } from './useRateActThree';
import { useRateActTwo } from './useRateActTwo';

export const useRateAct = () => {
  const {
    hasRateActThree,
    canExtendRateActThree,
  } = useRateActThree();

  const {
    hasRateActTwo,
    canExtendRateActTwo,
  } = useRateActTwo();

  /**
   * 二期三期有其中一个在线即返回true
   */
  const hasRateCardActivity = computed(() => hasRateActThree.value || hasRateActTwo.value);

  /**
   * 汇率权益剩余天数
   */
  const rateCardRemainDay = computed(() => +(homeModule.homeInitRes?.rate_card_remain_day || 0));

  /**
   * 判断是否有汇率卡权益
   */
  const hasRateCard = computed(() => +rateCardRemainDay.value > 0);

  /**
   * 汇率权益卡过期时间
   */
  const rateCardExpireTime = computed(() => activityCenterModule.rateCardExpireTime?.split(' ')[0]);

  /**
   * 判断是否可延期汇率卡
   * 1、[or] 二期可延期
   * 2、[or] 三期可延期
   */
  const canExtendRateCard = computed(() => canExtendRateActTwo.value || canExtendRateActThree.value);

  /**
   * 跳转活动中心页
   */
  const goRateActCenter = () => activityCenterModule.goActivityCenter(ActCenterFrom.OTHER);

  return {
    hasRateCardActivity,
    rateCardRemainDay,
    canExtendRateCard,
    rateCardExpireTime,
    goRateActCenter,
    hasRateCard,
  };
};
