import cache from '@/business/cache';
import { CacheK<PERSON> } from '@/types';

// 获取预期印花数
export const useExpectedStamp = () => {
  // 预期印花数
  const expectedStampNum = ref(cache.getCache(CacheKey.ACT_EXPECTED_STAMP) || 0);
  // 根据当前印花数+1代表预期获取印花数，且有效期为24小时，避免超时关单场景还有预期印花数
  const addStampNum = (curStampCount: number) => {
    const expectNum = curStampCount + 1;
    cache.setCache(CacheKey.ACT_EXPECTED_STAMP, expectNum, 24 * 60 * 60 * 1000);
    expectedStampNum.value = expectNum;
  };
  return {
    expectedStampNum,
    addStampNum,
  };
};
