import { Ref } from 'vue';
import { AwardProperties } from '.';
import { CouponAward, CouponProperties } from './coupon';
import { RateCardAward, RateCardProperties } from './rate-card';
import { queryActAward } from '@/api';
import { CustomException } from '@tencent/ppd-base-libs';
export enum AwardType {
  COUPON = 'coupon',
  RATE_CARD = 'ratecard',
}
// 有新增奖品需要在此新增
export type BizAward = CouponAward|RateCardAward;

// 奖品工厂，用于创建奖品实例
export const awardFactory = <Y extends AwardProperties>(awardType: AwardType, award: Y): BizAward => {
  const formatAward = {
    ...award,
    awardType,
  };
  switch (awardType) {
    case AwardType.COUPON:
      return new CouponAward(formatAward as unknown as CouponProperties);
    case AwardType.RATE_CARD:
      return new RateCardAward(formatAward as unknown as RateCardProperties);
  }
};

export class AwardManager<Y extends AwardProperties> {
  // 根据奖品类型映射奖品数组
  awardMap: Ref<Partial<Record<AwardType, BizAward[]>>> = ref({});
  // 铺平奖品数据
  awardList: Ref<BizAward[]> = ref([]);
  queryActAward = queryActAward;
  setAward(awardType: AwardType, awardArr: Y[]) {
    this.awardMap.value[awardType] = this.awardMap.value[awardType] || [];
    awardArr.forEach((award) => {
      const awardInstance = awardFactory(awardType, award);
      this.awardMap.value[awardType]?.push(awardInstance);
      this.awardList.value.push(awardInstance);
    });
  }
  // 根据活动id获取奖品
  getAwardByActId(actId: string) {
    const award = this.awardList.value.find(award => award.properties?.act_id === actId);
    if (!award) throw new CustomException({ actId, awardList: this.awardList.value }, 'getAwardByActIdFail', '根据活动查询奖品失败');
    return ref(award);
  }
}
