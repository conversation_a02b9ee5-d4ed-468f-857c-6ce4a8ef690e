import { Award, AwardProperties } from '.';
import { AwardType } from './award-manager';
export interface RateCardProperties extends AwardProperties{
  // 权益卡有效天数
  rate_card_remain_day: string;
  awardType: AwardType
  effective_days: number;
}
// vip权益卡
export class RateCardAward extends Award<RateCardProperties> {
  constructor(properties) {
    super({
      ...properties,
      // 单个权益卡持续天数
      effective_days: 35,
    });
  }
  // 格式例子：70日匯率提升卡
  getAwardName(name: string): string {
    const receivedNum = +this.properties.received_num;
    return `${receivedNum * this.properties.effective_days}日${name}`;
  }
}
