import { queryCacheTimes, addCacheTimes } from '@/api';
import { <PERSON><PERSON><PERSON>ey } from '@/types';
import { CustomException, util } from '@tencent/ppd-base-libs';
import cache, { Cache } from '../cache';
import { CacheTimeCounter } from './time-counter-class';
import { CacheTimestampCounter } from './timestamp-counter-class';


export enum CountLimitType {
  DAY = 'DAY',
  DAY_10 = 'DAY_10',
  MONTH = 'MONTH',
  DAY_100 = 'DAY_100',
  YEAR = 'YEAR'
};

export enum TimeCountLimitMode {
  TIME = 'TIME',
  TIMESTAMP = 'TIMESTAMP'
}

/**
 * expire - 过期时间
 * getTimt - 获取时间（年，月，日等）
 */
export const TimeCountLimitConfig: Record<string, {
  expire: number,
  getTime: () => number,
}> = {
  [CountLimitType.DAY]: {
    expire: 24 * 60 * 60 * 1000,
    getTime: () => new Date().getDate(),
  },
  [CountLimitType.MONTH]: {
    expire: 31 * 24 * 60 * 60 * 1000,
    getTime: () => new Date().getMonth(),
  },
  [CountLimitType.YEAR]: {
    expire: 365 * 24 * 60 * 60 * 1000,
    getTime: () => new Date().getFullYear(),
  },
};

/**
 * expire - 过期时间
 */
export const TimestampCountLimitConfig: Record<string, {
  expire: number,
}> = {
  [CountLimitType.DAY_100]: {
    expire: 100 * 24 * 60 * 60 * 1000,
  },
  [CountLimitType.DAY_10]: {
    expire: 10 * 24 * 60 * 60 * 1000,
  },
};

/**
 * 异步化的缓存服务
 */
export const cacheService: Cache = {
  getCache: async (key: string | CacheKey) => cache.getCacheAsync(key),
  setCache: async (key: string | CacheKey, value: any, time: number) => {
    cache.setCacheAsync(key, value, time);
  },
  clearCache: async (key: string | CacheKey) => cache.clearCache(key),
};

/**
 * 计数器抽象接口
 */
export interface CounterInterface {
  key: CacheKey | string;
  expire: number;
  type: CountLimitType;
  cacheService: Cache;
  checkUnderCountLimit: (limit: number) => Promise<boolean>;
  increaseLimit: () => void;
}

/**
 * 计数器实例构造器
 * @param key 缓存key
 * @param type 类型
 * @param cacheService 缓存服务
 * @returns {CounterInterface}
 */
export const createCounterInstance = (key: CacheKey | string, type: CountLimitType, cacheService: Cache) => {
  if (TimeCountLimitConfig[type]) {
    return new CacheTimeCounter(key, type, cacheService);
  }
  if (TimestampCountLimitConfig[type]) {
    return new CacheTimestampCounter(key, type, cacheService);
  }
};

/**
 * 日期限制配置（当前ckv服务仅支持 日 和 月 的计数）
 * ! 后台ckv尚未支持年
 */
export interface LimitOption {
  [CountLimitType.DAY]?: number;
  [CountLimitType.DAY_10]?: number;
  [CountLimitType.MONTH]?: number;
  [CountLimitType.DAY_100]?: number;
  [CountLimitType.YEAR]?: number;
}

// 检查序列 - 日期间隔由小至大
const LimitCheckSequence = [
  CountLimitType.DAY,
  CountLimitType.DAY_10,
  CountLimitType.MONTH,
  CountLimitType.DAY_100,
  CountLimitType.YEAR,
];

// 增加本地缓存次数
export const increaseLimitLocal = async (key: CacheKey | string, limitOption: LimitOption) => {
  // 生成所有计数器的实例
  const counterArr = LimitCheckSequence
    .filter(type => limitOption[type]) // 过滤未配置的 type
    .map(type => createCounterInstance(key, type, cacheService)); // 初始化对应type实例并放入数组

  // 计数增加 (日期间隔由小至大)
  // eslint-disable-next-line no-restricted-syntax
  for (const counter of counterArr) {
    if (!counter) continue;
    await counter.increaseLimit();
  }
};

/**
 * 检测次数是否低于上限（本地缓存）
 * @param {string} key 缓存key
 * @param {LimitOption} limitOption 次数配置
 * @return {boolean} true - 未超过上限 ｜ false - 超过上限
 */
export const checkTimeCountLimitLocal = async (
  key: CacheKey | string,
  limitOption: LimitOption,
  autoIncrease: boolean,
) => {
  // 生成所有计数器的实例
  const counterArr = LimitCheckSequence
    .filter(type => limitOption[type]) // 过滤未配置的 type
    .map(type => createCounterInstance(key, type, cacheService)); // 初始化对应type实例并放入数组

  // 检查是否超过 日 -> 月 -> 年 (日期间隔由小至大)
  // eslint-disable-next-line no-restricted-syntax
  for (const counter of counterArr) {
    if (!counter) continue;
    const limit = limitOption[counter.type];
    const withinLimit = limit ? await counter.checkUnderCountLimit(limit) : true;
    if (!withinLimit) return false;
  }

  // 计数增加 日 -> 月 -> 年 (日期间隔由小至大)
  if (autoIncrease) {
    // eslint-disable-next-line no-restricted-syntax
    for (const counter of counterArr) {
      if (!counter) continue;
      await counter.increaseLimit();
    }
  }

  return true;
};

// 增加ckv计数
export const increaseLimitCkv = async (key: CacheKey | string) => {
  const [setErr] = await util.to(addCacheTimes(key));
  if (setErr) {
    new CustomException(setErr, 'failToSetCacheTimes', `累加缓存限制次数失败-${key}`);
  }
};

/**
 * 检测次数是否低于上限（后端提供ckv）
 * @param {string} key 缓存key
 * @param {LimitOption} limitOption 次数配置
 * @return {boolean} true - 未超过上限 ｜ false - 超过上限
 */
export const checkTimeCountLimitCkv = async (
  key: CacheKey | string,
  limitOption: LimitOption,
  autoIncrease: boolean,
) => {
  const [getErr, data] = await util.to(queryCacheTimes(key));

  if (getErr || !data) {
    new CustomException(getErr, 'failToGetCacheTimes', `获取缓存限制次数失败-${key}`);
    return true; // 放开，走本地缓存
  }

  const { DAY, DAY_10, MONTH, DAY_100 } = limitOption;
  const isUnderDayLimit = DAY ? +data.daily_time < DAY : true;
  // ! 后端“10天”目前也记在daily_time中
  const isUnderDay10Limit = DAY_10 ? +data.daily_time < DAY_10 : true;
  const isUnderMonthLimit = MONTH ? +data.monthly_time < MONTH : true;
  // ! 后端“100天”目前也记在monthly_time中
  const isUnderDay100Limit = DAY_100 ? +data.monthly_time < DAY_100 : true;

  if (!isUnderMonthLimit || !isUnderDay10Limit || !isUnderDayLimit || !isUnderDay100Limit) return false;

  // 增加ckv缓存计数
  autoIncrease && increaseLimitCkv(key);

  return true;
};

/**
 * @param {boolean} checkRemote 判断是否同时校验后台ckv缓存
 * @param {boolean} autoIncrease 判断是否自增缓存
 */
interface CheckTimeCountOption {
  checkRemote?: boolean; // 是否同时校验后台ckv缓存
  autoIncrease?: boolean;
}
/**
 * 检测次数是否低于上限
 * @param {string} key 缓存key
 * @param {LimitOption} limitOption 次数配置
 * @param {CheckTimeCountOption} option 配置参数
 * @return {boolean} true - 未超过上限 ｜ false - 超过上限
 */
export const checkTimeCountLimit = async (
  key: CacheKey | string,
  limitOption: LimitOption,
  option: CheckTimeCountOption = {
    checkRemote: true,
    autoIncrease: true,
  },
) => {
  const finalOption: Required<CheckTimeCountOption> = {
    checkRemote: true,
    autoIncrease: true,
    ...option,
  };
  const localRes = await checkTimeCountLimitLocal(key, limitOption, finalOption.autoIncrease);

  // 1. 如果不校验远端，直接返回本地缓存结果
  if (!finalOption.checkRemote) return localRes;
  // 2. 如果本地缓存检测到超过次数上限，说明命中本地缓存，直接返回本地结果。
  if (!localRes) return localRes;

  const ckvRes = await checkTimeCountLimitCkv(key, limitOption, finalOption.autoIncrease);

  if (localRes !== ckvRes) {
    new CustomException({ key, localRes, ckvRes }, 'cacheInconsistent', '本地缓存与服务端ckv不一致');
  }

  // 返回服务端ckv缓存结果
  return ckvRes;
};

/**
 * 累加次数
 * @param {string} key
 * @param {LimitOption} limitOption
 */
export const increaseTimeCount = async (key: CacheKey | string, limitOption: LimitOption) => {
  increaseLimitLocal(key, limitOption);
  increaseLimitCkv(key);
};
