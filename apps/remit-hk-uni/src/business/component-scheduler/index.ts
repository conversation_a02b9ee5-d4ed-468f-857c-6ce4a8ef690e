import {
  SchedulerBehavior,
  BlockBehavoir,
} from './behavior';

import {
  Schedulable,
  SchedulableContext,
  SchedulableConfig,
  ScheduleType,
  SchedulerOption,
  SchedulerState,
  SchedulerConfig,
  setConfig,
  ComponentScheduler,
  DebugLevel,
} from './component-scheduler';

import {
  useComponentSchedulerExecute,
  useComponentSchedulerRegister,
} from './useComponentScheduler';

import {
  getSchedulableConfigBySchedulerId,
} from './utils';

/**
 * ! 使用 useComponentSchedulerXXX 钩子时需要注意以下问题：
 * ! - 组件注册顺序为mounted钩子执行顺序： 孙子组件mounted -> 子组件mounted -> 父组件mounted
 * !   如果孙子组件上层 v-if 判断依赖异步数据、或者mounted是异步函数，父组件mounted可能先于子组件mounted执行。
 * !   以上情况会导致组件还未注册，调度器就开始执行了。
 * !   解决方案：
 * !    1. 将依赖异步数据的 v-if 改为 v-show
 * !    2. 不使用 useComponentSchedulerExecute 执行调度，直接通过调度器实例执行excute方法
 **/

/**
 * 调度器id
 * ! 所有 router name 都已经被默认占用被用作页面曝光时的调度，不要使用
 */
export enum SchedulerId {
  PAYSUCCESS_FINISH = 'PAYSUCCESS_FINISH', // 支付完成页点击完成
}

/**
 * 调度器优先级文档：https://iwiki.woa.com/pages/viewpage.action?pageId=4006819520
 */
export enum SchedulableIdHome {
  NEW_USER_PROM_LANDING = 'NEW_USER_PROM_LANDING', // 首页 - 新用户立减活动曝光
  RATE_NOTIFY_HALF_MODAL_URL = 'RATE_NOTIFY_HALF_MODAL_URL', // 首页 - 汇率提醒半弹层（通过公众号模板消息拉起）
  RATE_ACT_THREE_MODAL = 'RATE_ACT_THREE_MODAL', // 首页 - 汇率卡三期弹层
  NEW_USER_PROM_FINISH = 'NEW_USER_PROM_FINISH', // 首页 - 新用户立减活动领取弹层
  STAMP_PROM_MODAL = 'STAMP_PROM_MODAL', // 首页 - 印花活动曝光弹层
  STAMP_PROM_COMPOSE_MODAL = 'STAMP_PROM_COMPOSE_MODAL', // 首页 - 印花活动合成弹层曝光
  NEW_USER_PROM_MODAL = 'NEW_USER_PROM_MODAL', // 首页 - 新用户立减弹层 29.99
  OPERATION_MODAL = 'OPERATION_MODAL', // 首页 - 运营位弹层
  TOTAL_FEE_PROM_MODAL = 'TOTAL_FEE_PROM_MODAL', // 首页 - 手续费全免券弹层 30
  RATE_NOTIFY_HALF_MODAL = 'RATE_NOTIFY_HALF_MODAL', // 首页 - 汇率提醒半弹层（首页进入后自动拉起）
  RATE_ACT_TWO_MODAL = 'RATE_ACT_TWO_MODAL', // 首页 - 汇率卡二期弹层
  RATE_NOTIFY_HOME = 'RATE_NOTIFY_HOME', // 首页 - 汇率设置半弹层
  COUPON_NOTIFY_MODAL_URL = 'COUPON_NOTIFY_MODAL_URL', // 首页 - 优惠券提醒 - url链接拉起
  PROM_HALF_MODAL = 'PROM_HALF_MODAL', // 首页-活动半弹层
  DP_PROM_HALF_MODAL = 'DP_PROM_HALF_MODAL', // 首页 - 投放调度半弹窗
  RATE_NOTIFY_SLIDE_MODAL = 'RATE_NOTIFY_SLIDE_MODAL', // 首页 - 汇率提醒功能提示半弹窗
}

/**
 * 优先级文档：https://iwiki.woa.com/pages/viewpage.action?pageId=4008242392
 */
export enum SchedulableIdPaySuccessFinish {
  RATE_NOTIFY = 'RATE_NOTIFY', // 支付完成页 - 汇率订阅弹层
  NEW_USER_PROM_MODAL = 'NEW_USER_PROM_MODAL', // 支付完成页 - 新用户活动弹层
  STAMP_PROM_PAYSUCCESS_MODAL = 'STAMP_PROM_PAYSUCCESS_MODAL', // 支付完成页点击完成 - 汇三免一印花获取提醒
  TOTAL_FEE_CARD_MODAL = 'TOTAL_FEE_CARD_MODAL', // 支付完成页点击完成 - 手续费减免卡弹层
}

type SchedulableId = SchedulableIdHome | SchedulableIdPaySuccessFinish;

export type {
  SchedulerBehavior,
  Schedulable,
  SchedulableId,
  SchedulableContext,
  SchedulableConfig,
  SchedulerConfig,
  SchedulerOption,
};

export {
  setConfig,
  useComponentSchedulerExecute,
  useComponentSchedulerRegister,
  getSchedulableConfigBySchedulerId,
  DebugLevel,
  ComponentScheduler,
  SchedulerState,
  ScheduleType,
  BlockBehavoir,
};
