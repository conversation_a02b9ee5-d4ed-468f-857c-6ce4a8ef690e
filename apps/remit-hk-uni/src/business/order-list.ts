import { HisItem, DetailQryResp } from '@/api';
import { getOrderCurStateConfig } from './order-state';
import { CustomException } from '@tencent/ppd-base-libs';

interface StateConfig {
  title: string;
  desc: string | undefined;
  isEndProcess: boolean;
  isFailProcess: boolean;
};

interface HisItemWithStateConfig extends HisItem {
  stateConfig?: StateConfig;
};

export function formatOrderList(list: HisItem[]): HisItemWithStateConfig[] {
  return list.map((item) => {
    // 这里数据本来预期是订单详情数据 但是这里只需要拿到标题
    const stateConfig = getOrderCurStateConfig(item as unknown as DetailQryResp);
    if (!stateConfig) {
      new CustomException(item, 'formatOrderListFail', `当前state:${item.state}没有对应的流程配置`);
      return item;
    }
    return {
      ...item,
      stateConfig: {
        title: stateConfig.tit,
        desc: stateConfig.inProcess.desc,
        isEndProcess: !stateConfig.preProcess,
        isFailProcess: stateConfig.tag === 'fail',
      },
    };
  });
}

