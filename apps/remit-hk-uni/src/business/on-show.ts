/**
 * 通过切换后台、打开小程序会堵塞js进程的方式来判断是否处于后台阶段。
 *   如果处于后台 那么偏差量会越来越大 只要超过我们设定的阈值gap 就可以判断为切换了后台了
 *   每次回调后必须要清除计数
 *   ! 缺陷 - 部分安卓机器进入后台后一定时间内setInterval仍然会执行
 *
 *   ! 方式2 - 使用 ./on-show-vc.ts
 *   ! 监听visibilitychange事件，hidden表示进入后台，再次回来是执行回调
 */
let timer: undefined | NodeJS.Timer = undefined;
export default {
  init(callback, gap: number) {
    let count = 0;
    clearInterval(timer);
    const startTime = +new Date();
    timer = setInterval(() => {
      count += 1;
      const now = +new Date();
      const expectTime = startTime + (count * 1000);
      if (Math.abs(now - expectTime) > gap) {
        clearInterval(timer);
        console.log('触发回调');
        callback();
      }
      // console.log(expectTime, now, now - expectTime);
    }, 1000);
  },
  destroy() {
    clearInterval(timer);
  },
};

