import { getFilterSearch } from './url';
import { LOGIN_CONFIG } from '@/constant';
import { tracePerformanceT4 } from '@/domain/common/performace';
import { util } from '@tencent/ppd-base-libs';
import { login as loginReq } from '@/api';
import { showLoading, hideLoading } from '@/adapters/uni-api-common/show-loading';
import { downGradeNotice } from '@/business/notification';

/**
 * 获取登陆重定向地址
 * @param filter
 */
function getLoginUrl(filter: string[]) {
  const searchStr = getFilterSearch(filter);
  return `${location.protocol}//${location.host}${location.pathname}${searchStr}${location.hash}`;
}

/**
 * 获取应用appId
 * @param appId appId
 */
function getWechatLoginUrl(appId: string) {
  const redirectUrl = getLoginUrl(['code', 'state']);
  return `${location.protocol}//open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodeURIComponent(redirectUrl)}&response_type=code&scope=snsapi_base#wechat_redirect`;
}

/**
 * 跳转微信获取code
 * @param appId
 */
function redirectLogin(appId: string) {
  const wechatUrl = getWechatLoginUrl(appId);
  // 港陆打开链路耗时打点
  tracePerformanceT4();
  location.href = wechatUrl;
}

const codeArr: string[] = [];
function isCodeUsed(code: string) {
  return codeArr.indexOf(code) > -1;
}

function recordCode(code: string) {
  codeArr.push(code);
}

/**
 * 是否是未登录错误码
 * @param retCode
 */
function isUnloginRetCode(retCode: string) {
  return LOGIN_CONFIG.NEED_LOGIN_ARR.includes(retCode);
}

/**
 * 登陆接口
 * TODO: 是否缓存登陆接口，避免每次页面跳转就调用？若后续真的登陆态失效，其他接口也可以跳转登陆
 */
let hasLogin = false; // 内存级缓存登陆接口，避免每次页面切换都调用
async function login(forceLogin = false) {
  if (!forceLogin && hasLogin) return;
  showLoading();
  const [err] = await util.to(loginReq());
  hideLoading();
  if (err && !isUnloginRetCode(err.retcode)) {
    /**
   * 兜底公告
   * 1. 开关关闭 -> 调用showBusinessError
   * 2. 开关开启 -> 跳转兜底公告
   */
    downGradeNotice(err);
    throw err;
  }
  hasLogin = true;
}

export default {
  isUnloginRetCode,
  redirectLogin,
  getWechatLoginUrl,
  recordCode,
  isCodeUsed,
  login,
};
