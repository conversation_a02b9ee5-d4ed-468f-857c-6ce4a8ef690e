<template>
  <div>
    <div class="order-list-hd">
      <span class="title">{{ $t("汇款记录") }}</span>
      <span>
        <FitPicker
          :placeholder="$t('请选择')"
          :value="qryYear"
          :items="qryYearOptions"
          :confirm-btn-text="$t('确定')"
          @change="updateQryYear"
        />
        <SvgIcon
          icon-class="triangle"
          class-name="g-triangle-down"
        />
      </span>
    </div>
    <ScrollView
      :canload="canload"
      @scroll="onscroll"
    >
      <div
        v-if="listState === LIST_STATE.empty"
        class="empty"
      >
        <SvgIcon
          icon-class="empty"
          :color="'#98E2BB'"
          class-name="empty-icon"
        />
        <div>{{ $t("暂无汇款记录") }}</div>
      </div>
      <div
        v-for="(listItem, index) in list"
        :key="index"
        class="order-list-item"
        @click="goDetail(listItem.listid)"
      >
        <div class="order-list-item-left">
          <div class="order-list-item-hd">
            <span>{{ listItem.recv_name }}</span>
            <Amount
              class="order-list-item-amount"
              amt-type="amt"
              :currency="CURRENCY.HKD"
              :amt="listItem.remit_amt"
            />
          </div>
          <div class="order-list-item-ft">
            <span class="order-list-item-time">{{ listItem.create_time }}</span>
            <span
              v-if="listItem.stateConfig"
              :class="getStateClass(listItem.stateConfig)"
            >{{ listItem.stateConfig.title }}</span>
          </div>
        </div>
        <SvgIcon
          icon-class="triangle"
          class-name="g-triangle-right"
        />
      </div>
    </ScrollView>
  </div>
</template>

<script setup lang="ts">
import {
// FitButton,
  FitPicker,
} from '@/fit-ui';
import orderListModule from '@/store/order-list';
import ScrollView from '@/components/ScrollView.vue';
import Amount from '@/components/amount/Amount.vue';
import { CURRENCY } from '@/types';
import SvgIcon from '@/components/SvgIcon.vue';
import { ListState as LIST_STATE } from '@/utils/scroll';
import { getLastRoute } from '@/business/route';
import { ROUTE } from '@/router';
import { biHottag } from '@/business/biHottag';

/**
 * 年份
 */
const qryYear = computed(() => {
  console.log(orderListModule.qryYear);

  return orderListModule.qryYear;
});

/**
 * 年份选择器
 */
const qryYearOptions = computed(() => orderListModule.qryYearOptions);

/**
 * 年份
 */


const listState = computed(() => orderListModule.listState);

/**
 * 历史记录列表
 */
const list = computed(() => orderListModule.list);

/**
 * 是否可以请求数据 用于告诉scrollview能否触发onscroll
 */
const canload = computed(() => orderListModule.canload);

// created
/**
 * 初始化滚动
 */
async function created() {
  biHottag('remit.newhk2cn.history.brow');
  if (getLastRoute() === ROUTE.ORDER_DETAIL && await orderListModule.isInit()) return;// 如果是从详情过来 不刷新
  orderListModule.initScroll();
}

created();

/**
 * 滚动到底部触发
 */
function onscroll() {
  return orderListModule.fetchData();
}

function getStateClass(stateConfig) {
  return {
    'order-list-item-green': true,
    'order-list-item-grey': stateConfig.isEndProcess,
    'order-list-item-red': stateConfig.isFailProcess,
  };
}

/**
 * 跳转详情
 * @param listid
 */
function goDetail(listid) {
  return orderListModule.goDetail(listid);
}

/**
 * 年份变更
 * @param val
 */
async function updateQryYear(val) {
  await orderListModule.updateQryYear(val);
  await orderListModule.resetData();
  await orderListModule.fetchData();// 清空数据后要更新数据数
}


</script>
<style lang="less">
@import "~@/less/main.less";
.empty {
  .g-text-28();
  .g-opacity-24();
  text-align: center;
  .empty-icon{
    font-size: 96/@rem;
    margin-bottom: 32/@rem;
    margin-top: 160/@rem;
  }
}
.order-list {
  &-hd {
    padding: 56 / @rem 32 / @rem;
    .g-layout-between();
    .g-opacity-88();
    .g-text-56();
    .title{
      font-weight: bold;
    }
    :deep(.fit-form-line) {
      .g-text-28();
      .g-opacity-56();
    }
    :deep(.fit-multi-select) {
      display: inline-block;
    }
    .g-triangle-down {
      margin-left: 16 / @rem;
    }
  }
  &-item {
    .g-press();
    padding: 32 / @rem;
    .g-divide-bottom();
    overflow: hidden;
    .g-layout-between();
    &:last-child {
      &::after {
        display: none;
      }
    }
    &::after {
      left: 32 / @rem;
    }
    &-hd,
    &-ft {
      .g-layout-between();
    }
    &-left {
      flex-grow: 1;
      margin-right: 16 / @rem;
    }
    &-hd {
      .g-text-28();
      .g-opacity-88();
    }
    &-amount {
      .g-text-32();
      .g-opacity-88();
      margin-bottom: 7 / @rem;
    }
    &-time {
      .g-text-24();
      .g-opacity-24();
    }
    &-green {
      .g-color-green();
      font-size: 24 / @rem;
      letter-spacing: 0;
      text-align: right;
      opacity: 1;
    }
    &-grey {
      .g-color-grey();
    }
    &-red {
      .g-color-red();
    }
  }
}
</style>
