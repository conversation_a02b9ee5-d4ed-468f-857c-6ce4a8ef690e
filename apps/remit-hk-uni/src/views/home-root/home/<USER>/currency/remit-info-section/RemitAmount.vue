<template>
  <!-- 实际、预计付款金额 -->
  <div
    class="pay-amount-fee flex flex-col text5-reg mt-10rpx"
  >
    <span
      v-if="isBuyIn"
      class="color-text-auxiliary"
    > {{ $t("预计支付") }} </span>
    <span
      v-else
      class="color-text-auxiliary"
    > {{ $t("实际支付") }} </span>
    <Amount
      class="number4 mt-4rpx"
      :class="[ +payAmt>0 && isPayAmtActive && isUserInputFlag ? 'color-text-title' : 'color-text-disable' ]"
      amt-type="amt"
      :amt="payAmt"
      type="suffix"
      :currency="CURRENCY.HKD"
    />
    <div
      v-if="+totalDiscountAmt > 0"
      class="text-20rpx lh-20rpx Medium WechatSansSS-Medium color-Sunset-S600"
    >
      {{ $t("总节省") }} {{ totalDiscountAmt }} HKD
    </div>
  </div>
</template>

<script setup lang="ts">
import Amount from '@/components/amount/Amount.vue';
import { CURRENCY, TransType } from '@/types';
import { useBizCurrencyInputGroup } from '../useBizCurrencyInputGroup';
import { SearchState } from '@/utils/search';
import { useBizAmountCalculate } from '../useBizAmtCalculate';


const {
  lastAmountInfoBox, // 最近一次由金额计算接口返回的结果（无异常）
  amountSearchState, // 金额计算接口请求状态
  isUserInputFlag, // 用户是否输入过金额
} = useBizCurrencyInputGroup();

const {
  payAmt, // 支付金额
  totalDiscountAmt, // 总优惠金额
} = useBizAmountCalculate();

// 判断是否购买人民币
const isBuyIn = computed(() => lastAmountInfoBox.value?.transType === TransType.BUY_IN);

// 判断是否已经拿到支付金额
const isPayAmtActive = computed(() => amountSearchState.value === SearchState.SUCCESS);

</script>
<style lang="less" scoped>
@import "~@/less/main.less";
.pay-amount {
  &-fee {
    :deep(.suffix) {
      font-size: 28/@rem;
    }
  }
}
</style>
