module.exports = {
  extends: ['../../devconfig/.eslintrc.js'],
  parserOptions: {
    tsconfigRootDir: __dirname,
  },
  globals: {
    uni: true,
  },
  ignorePatterns: ['.eslintrc.js', 'auto-imports.d.ts'],
  rules: {
    'no-restricted-imports': [
      'error',
      {
        paths: [{
          name: "@tencent/ppd-common",
          message: '不要使用直接依赖该包，请从adapters引入使用'
        }],
        patterns: [
          "@tencent/ppd-common/*",
        ],
      }
    ]
  },
  overrides: [
    {
      files: ["*/adapters/*.ts", "*/adapters/**/*.ts"],
      rules: {
        "no-restricted-imports": "off"
      }
    }
  ],
};
