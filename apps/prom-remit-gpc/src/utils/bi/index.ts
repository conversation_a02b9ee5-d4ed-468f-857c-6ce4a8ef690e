import { App } from 'vue';
import { plugin, setMapPageNameFun  } from '@tencent/fit-bi-sdk';
import report, { CustomException } from '@/adapters/report';
import BI_CONFIG from '../../../mta-config-global';
import { getCurPageRoute, getBIPageMapAndBIParam, getBiParams } from './tool';
import { setBiParamsFromUrl } from './init-params';

export const initBiReport = async (app: App, biInitConfig: {
  reportAfterUin: boolean
} = {
  reportAfterUin: true,
}) => {
  const options = {
    ...BI_CONFIG,
    closeCheckExtF: true,
    closePv: { h5: true, mp: true },
    route: { beforeEach: () => {} },
    // #ifdef MP-WEIXIN
    mp: true,
    // #endif
    // #ifdef H5
    enableRouteListen: true,
    // #endif
    reportAfterUin: biInitConfig.reportAfterUin,
    beforeReport: (reportData) => {
      // 不用加上当前包过滤，因为上报实例上报的数据都需要上报，只需要拦住页面pv uv的上报即可（由onAppRoute拦截就行）
      Object.assign(reportData.params, getBiParams());
      report.reportEvent({
        key: reportData.key,
        params: reportData.params,
      });


      return reportData;
    },
  };
  const { pageMap } = getBIPageMapAndBIParam();
  plugin.install(app, { ...options });

  // 获取页面名称，A中的页面名
  setMapPageNameFun((originName: string) => {
    let hash = originName;
    if (!originName) {
      hash = `/${getCurPageRoute()}`; // @NOTE 兼容BI-SDK没有返回originName的情况，后面下沉到BI-SDK内实现
    }
    const mapResult = pageMap[hash];
    if (!mapResult) {
      report.reportInfo({
        key: 'BIReportGetAError',
        msg: JSON.stringify(mapResult),
        ext2: hash,
      });
      new CustomException(hash, 'BIReportGetAError', 'bi上报A变量映射错误');
    }
    return mapResult || 'default';
  });
  // #ifdef H5
  setBiParamsFromUrl();
  // #endif
};
