import { setBusDynamicParams } from '@/utils/bi/set-dynamic-params';
import login from './index';
import reportInstance from '@/adapters/report';

/**
 * 登录后
 */
export function afterLogin() {
  // BI登录态
  setBusDynamicParams(Object.assign({}, {
    fuin: login.getLoginData.value.openid,
    fopenid: login.getLoginData.value.openid,
  }));
  // 日志上报登录态
  reportInstance.setConfig({
    uin: login.getLoginData.value.openid,
  });
}
