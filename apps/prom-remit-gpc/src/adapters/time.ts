import dayjs from 'dayjs';
// import { globalStorageInstance, GlobalStorageKeys } from '@/adapters/storage';
import { getTimeDiff, setTimeDiff } from '@tencent/weremit-common';

let isSetDiff = false;
// 记录服务端时间差
export function setServerTimeDiff(sysTime: string) {
  if (isSetDiff) return;// 已经设置过避免重复设置
  if (!sysTime) {
    return;
  }
  const sysTimestamp = dayjs(sysTime).valueOf(); // 响应头时间戳

  const customTime = dayjs().valueOf();
  const timeDiff = customTime - sysTimestamp;
  try {
    setTimeDiff(timeDiff);
    isSetDiff = true;
  } catch (e) {
    console.error(`cachePara.configTimeDiff::error::${e}`);
  }
}

/**
 * @description 获取服务器时间戳
 * @returns {number}
 */
export function getServerTime(): number {
  const timeDiff = Number(getTimeDiff()) || 0;
  const nowTime = dayjs().valueOf();
  return nowTime - timeDiff;
}


/**
 * 计算传入时间到系统时间剩余的秒数
 * TODO 这个分包应该没用到，删了
 */
export function getTimeLeft(finishTime: string | number | Date | dayjs.Dayjs | null | undefined) {
  const systemMilliseconds = getServerTime();
  const finishMilliseconds = dayjs(finishTime).valueOf();
  const secondsLeft = Math.floor(finishMilliseconds - systemMilliseconds);
  return secondsLeft;
}
