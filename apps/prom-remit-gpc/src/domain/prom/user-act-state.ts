import type { QryUserActStateReq, QryUserActStateRes } from '@/api/prom/index';
import { qryUserActState } from '@/api/prom/index';

let qryUserActStateSinglePromise: Promise<QryUserActStateRes>|undefined = undefined;

/**
 * 获取用户-活动状态singlePromise
 * TODO 讨论。是否需要活动id维度的singlePromise
*/
const getQryUserActStateSinglePromise = (params: QryUserActStateReq) => {
  if (!qryUserActStateSinglePromise) {
    qryUserActStateSinglePromise = qryUserActState(params);
  }
  return qryUserActStateSinglePromise as Promise<QryUserActStateRes>;
};

export {
  getQryUserActStateSinglePromise,
};
