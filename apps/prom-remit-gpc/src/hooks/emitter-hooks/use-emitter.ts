import mitt, { Emitter } from 'mitt';
const emitter: Emitter<BizEvent> = mitt<BizEvent>();

/**
 * 事件参数类型
*/
type BizEvent = {
  /**
   * 规则按钮点击事件
   * 注：参数类型为字面量true，因为实际这个事件不用提交参数，故写死个true
  */
  ON_CLICK_RULE_POPUP_BTN: true
  /**
   * 图片弹窗按钮点击事件
   */
  ON_CLICK_IMAGE_POPUP_BTN: true
  /**
   * 分享按钮点击事件
   */
  ON_CLICK_USER_ACT_STATE_BTN_SHARE_TIPS: true
  /**
   * 领取奖品按钮点击事件
   */
  ON_CLICK_USER_ACT_STATE_BTN_CLAIM_PRIZE: true
};

/**
 * 原始的发布订阅对象
 * 使用场景，需要在业务的关键行为做事件处理，非必要应减少使用
 * 24年3月12日注：当前活动基座暂时没用到此方法
*/
export const useEmitter = () => ({
  emitter,
});

/**
 * 发布订阅具体事件
 * @param type 要监听/提交的事件名
 */
export const useEventEmitter = <T extends keyof BizEvent>(type: T) => ({
  /**
   * 发布事件
   * @param event 事件类型
   */
  emit: (event: BizEvent[T]) => emitter.emit(type, event),

  /**
   * 订阅事件
   * @param cb 监听回调
   */
  on: (cb: (event: BizEvent[T]) => void) => emitter.on(type, cb),

  /**
   * 先取消，再订阅事件
   * @param cb 监听回调
   */
  offOn: (cb: (event: BizEvent[T]) => void) => {
    emitter.off(type);
    emitter.on(type, cb);
  },
  /**
   * 取消订阅
   */
  off: () => emitter.off(type),
});
